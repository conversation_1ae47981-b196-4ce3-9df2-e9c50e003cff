import _                                from 'lodash';
import { StatusCodes }                  from 'http-status-codes';
import supertest                        from 'supertest';
import { v1 as uuid }                             from 'uuid';
import {
    OwnerType,
    UOM,
    ProductField,
    ProductType,
}                                       from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode,
}                                       from '@treez/commons/sharedTypings/priceTier';
import { ValidationType }               from '../../lib/sharedInterfaces';
import Catalog                          from '../../models/Catalog';
import PriceTier                        from '../../models/PriceTier';
import CatalogPriceTier                 from '../../models/CatalogPriceTier';
import CatalogProduct, {
    addProductToCatalog,
    CatalogProductStatus,
    defaultCatalogProductAttributes,
}                                       from '../../models/CatalogProduct';
import Product, {
    linkProducts,
}                                       from '../../models/Product';
import ProductRequirements              from '../../models/ProductRequirements';
import ProductRequirementsToCatalogs    from '../../models/ProductRequirementsToCatalogs';
import ProductUpdateOutbox              from '../../models/ProductUpdateOutbox';
import ExternalReference, {
    ExternalReferenceType
}                                       from '../../models/ExternalReference';
import { AssortmentType }               from '../../models/Assortment';
import { mockTreezAccess }              from '../../tests/testHelpers/util';
import {
    setupProductsAndCatalogs,
    storeCatalog,
    orgProduct,
    orgCatalog,
    brandProduct,
}                                       from '../testHelpers/data';
import app                              from './testApp';

const {SELL_TREEZ_ID} = ExternalReferenceType;

const testUser = {userAuthId: '<EMAIL>'};

describe('/catalogs endpoints', () => {
    it('Rejects attempt to delete a catalog that does not exist', async () => {
        const response = await supertest(app)
            .delete(`/catalogs/1000`);


        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Deletes a catalog', async () => {
        await Catalog.create({
            name: 'test catalog',
            ownerId: 1,
            ownerType: OwnerType.ORG
        });

        const response = await supertest(app)
            .delete(`/catalogs/1`);

        const noCatalog = await Catalog.findByPk(1);

        expect(response.status).toBe(StatusCodes.NO_CONTENT);
        expect(noCatalog).toBe(null);
    });

    it(`Gets a requestor's catalogs and *only* that requestor's catalog(s)`, async () => {
        const brandCatalog = await Catalog.create({
            name: 'test catalog',
            ownerId: 1,
            ownerType: OwnerType.BRAND
        });

        const product = await Product.create({
            amount       : "1",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        await Catalog.create({
            name           : "Test Brand Catalog 2",
            ownerId        : 2,
            ownerType      : OwnerType.BRAND,
            parentCatalogId: null,
        })

        brandCatalog.$add('products', [product.id]);

        const response = await supertest(app)
            .get(`/catalogs?ownerId=${brandCatalog.ownerId}&ownerType=brand`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            count:              1,
            data:               [{
                name     : 'test catalog',
                ownerId  : 1,
                ownerType: OwnerType.BRAND
            }],
        });
    });

    it('Gets catalog information', async () => {
        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}`);

        expect(response.body).toMatchObject({
            data: {
                name           : "Test Org Catalog",
                ownerId        : 55,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            },
        });
    });

    it('Gets assorments information', async () => {
        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/assortments/${AssortmentType.COLLECTION}`);

        expect(response.body).toMatchObject([{
            catalogId: 1,
            icon: "featured_star.svg",
            name: "Featured",
            order: 0,
            type: "COLLECTION",
        }]);
    });

    it('Updates a catalog via patch', async () => {
        const catalog = await Catalog.create({
            name           : "Test Brand Catalog",
            ownerId        : 7224,
            ownerType      : OwnerType.BRAND,
            parentCatalogId: null,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}`)
            .send({ name: "Patched Test" });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            name: "Patched Test",
        });
    });

    it(`Adds a brand treez product to a catalog`, async () => {

        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const product = await Product.create({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            price        : "25.000000",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products`)
            .send({
                productIds: [product.id],
            });

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
            }
        });

        const productAdded = await Product.findOne({
            where: {
                ownerId: catalog.ownerId,
                ownerType: catalog.ownerType,
                linkedTo: product.id
            }
        });
        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(1);
        expect(response.body[0].name).toBe(product.name);
        expect(catalogProduct).toMatchObject({
            id:         1,
            status:     CatalogProductStatus.ACTIVE,
            catalogId:  catalog.id,
        });
        expect(productAdded).not.toBeNull()
    });

    it(`Removes a product from a catalog`, async () => {
        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const product = await Product.create({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            price        : "25.00",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', product);

        const response = await supertest(app)
            .delete(`/catalogs/${catalog.id}/products`)
            .send({
                productIds: [product.id],
            });

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        expect(response.status).toBe(StatusCodes.NO_CONTENT);
        expect(_.get(catalogProduct, 'dataValues[status]')).toBe(CatalogProductStatus.DELETED);
    });

    it(`Rejects attempt to add invalid product id`, async () => {
        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products`)
            .send({
                productIds: [42],
            });

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    });

    it(`Rejects attempt to update a catalog that doesn't exist via patch`, async () => {
        const response = await supertest(app)
            .patch(`/catalogs/1000`)
            .send({
                name: `Bad Santa's`,
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Creates a new catalog', async () => {
        mockTreezAccess();

        const response = await supertest(app)
            .post('/catalogs')
            .send({
                name           : "Test Brand Catalog",
                ownerId        : 7224,
                ownerType      : OwnerType.BRAND,
                parentCatalogId: null,
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: {
                name           : "Test Brand Catalog",
                ownerId        : 7224,
                ownerType      : OwnerType.BRAND,
                parentCatalogId: null,
            },
        });
    });

    it('Rejects attempt to create a new invalid catalog', async () => {
        mockTreezAccess();

        const response = await supertest(app)
            .post('/catalogs')
            .send({
                name           : "Test Brand Catalog",
                ownerId        : 7224,
                parentCatalogId: null,
                ownerType      : 'fish',
            });

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    });

    it(`Adds product to multiple catalogs`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 7220,
                ownerType    : OwnerType.BRAND,
                price        : "25.000000",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const storeCatalog = await Catalog.create({
            name:               "Test Store Catalog",
            ownerId:            1000,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    1,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products`)
            .send({
                productIds: [product.id],
                childCatalogIds: [storeCatalog.id],
            });

        const catalogProductCount = await CatalogProduct.count({
            where: {
                catalogId: [orgCatalog.id, storeCatalog.id],
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(1);
        expect(response.body[0].id).not.toBe(product.id);
        expect(response.body[0].name).toBe(product.name);
        expect(catalogProductCount).toBe(2);
    });

    it('Updates active status of owned product', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const product = await Product.create({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', [product.id]);

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}`)
            .send({
                productIds: [product.id],
                status:     CatalogProductStatus.DEACTIVATED,
            });

        const catalogProduct = await CatalogProduct.findOne({
            attributes: defaultCatalogProductAttributes,
            where: {
                catalogId: catalog.id,
                productId: product.id,
            },
        });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(catalogProduct).toMatchObject({
            id       : 1,
            status   : CatalogProductStatus.DEACTIVATED,
            catalogId: catalog.id,
            productId: product.id,
        });
    });

    it('Updates overrides of Brand product', async () => {
        const catalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const product = await Product.create({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            price        : "25.00",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', [product.id]);

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}`)
            .send({
                descriptions: {
                    main: "description override",
                }
            });

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            },
        });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(catalogProduct).toMatchObject({
            id              : 1,
            status          : CatalogProductStatus.ACTIVE,
            catalogId       : catalog.id,
            catalogOverrides: {
                descriptions: {
                    main: "description override",
                }
            },
            productId       : product.id,
            price           : null,
        });
    });

    it(`filters out undefined values from overrides`, async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product.id,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}`)
            .send({
                subtype: undefined,
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });
    });

    it(`it does not filter out null values from overrides, allowing a user to hide a product levels information`, async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                description  : "test description",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product.id,
            price:     20,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}`)
            .send({
                description: null,
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });
        expect(response.body.data.description).toBeUndefined();
    });

    it('Rejects attempt to update active status of Brand product of an invalid catalog', async () => {
        await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        Product.create({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            price        : "25.00",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        const response = await supertest(app)
            .patch(`/catalogs/1000/products/1`)
            .send({
                active:     CatalogProductStatus.ACTIVE,
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Rejects attempt to override product that does not exist', async () => {
        await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.ORG,
            parentCatalogId: null,
        });

        const response = await supertest(app)
            .patch(`/1/products/1000`)
            .send({
                status:     CatalogProductStatus.DEACTIVATED,
                descriptions: {
                    main: "description override",
                }
            });

        expect(response.status).toBe(StatusCodes.NOT_FOUND);
    });

    it('Adds a new product', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        });

        const response = await supertest(app)
            .post('/catalogs/1/product')
            .send({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                price        : "25.000000",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: {
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
                ownerId      : catalog.ownerId,
                ownerType    : catalog.ownerType,
            },
        });
    });

    it('Adds the new product to the parent Catalog as well', async () => {
        const orgCatalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 1,
            ownerType: OwnerType.ORG,
        });

        const storeCatalog = await Catalog.create({
            name           : "Test Store Catalog",
            ownerId        : 1,
            ownerType      : OwnerType.STORE,
            parentCatalogId: orgCatalog.id
        });

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/product`)
            .send({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 7220,
                ownerType    : OwnerType.BRAND,
                price        : "25.000000",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            });

        const parentCatalog = await Catalog.findByPk(orgCatalog.id, {
            include: [
                {
                    model: Product,
                    as: 'products'
                }
            ]
        }) || {} as Catalog;

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(parentCatalog.products).toHaveLength(1);
    });

    it('Adds a new product with a price', async () => {
        const catalog = await Catalog.create({
            name     : "Test store Catalog",
            ownerId  : 55,
            ownerType: OwnerType.STORE,
        });

        const storeProductWithPrice = {
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 7220,
            ownerType    : OwnerType.BRAND,
            price        : "420.000000",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        }

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send(storeProductWithPrice);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: {
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                price        : "420.000000",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            },
        });

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: 1,
                productId: response.body.data.id,
            }
        }) as CatalogProduct;

        expect(catalogProduct.price).toBe("420.000000");
    });

    it('Adds a list of new products', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const response = await supertest(app)
            .post('/catalogs/1/product')
            .send([{
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }]);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: [{
                ...{
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Z0M&WHY?!",
                    name         : "ZOMGWHY?!",
                    ownerId      : 55,
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                ownerId: catalog.ownerId,
                ownerType: catalog.ownerType,
            }],
        });
    });

    it('Adds a list of new inactive products', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
        });

        const response = await supertest(app)
            .post('/catalogs/1/product')
            .send([{
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                status       : CatalogProductStatus.DEACTIVATED,
                uom          : UOM.EACH,
            }]);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: [{
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
                status       : CatalogProductStatus.DEACTIVATED,
                ownerId      : catalog.ownerId,
                ownerType    : catalog.ownerType,
            }],
        });
    });

    it('new products default to active ', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
        });

        const response = await supertest(app)
            .post('/catalogs/1/product')
            .send([{
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }]);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            data: [{
                ...{
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Z0M&WHY?!",
                    name         : "ZOMGWHY?!",
                    ownerId      : 7220,
                    ownerType    : OwnerType.BRAND,
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                    status       : CatalogProductStatus.ACTIVE
                },
                ownerId: catalog.ownerId,
                ownerType: catalog.ownerType,
            }],
        });
    });


    it('Rejects attempt to add invalid product via post', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        });

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send({name: 'Failure'});

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    });

    it(`correctly validates a product per requirements posted to the database`, async () => {
        const [
            requirements,
            catalog,
        ] = await Promise.all([
            ProductRequirements.build({
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        requiredField: true,
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                    requiredField: true,
                                }
                            }
                        }
                    }
                }
            }).save(),
            Catalog.build({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'Central Catalog',
            }).save(),
        ]);

        await ProductRequirementsToCatalogs.build({
            productRequirementsId: requirements.id,
            catalogId: catalog.id,
        }).save()

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : ProductType.BEVERAGE,
                uom          : UOM.EACH,
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body.data).toMatchObject({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : catalog.ownerId,
            ownerType    : catalog.ownerType,
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : ProductType.BEVERAGE,
            uom          : UOM.EACH,
        });
    });

    it(`correctly invalidates a product per requirements posted to the database for that state`, async () => {
        const [
            requirements,
            catalog,
        ] = await Promise.all([
            ProductRequirements.create({
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        requiredField: true,
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                    requiredField: true,
                                }
                            }
                        }
                    }
                }
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'Central Catalog',
            }),
        ]);

        await ProductRequirementsToCatalogs.create({
            productRequirementsId: requirements.id,
            catalogId: catalog.id,
        });

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 7220,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : ProductType.BEVERAGE,
                uom          : UOM.EACH,
            });

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    });

    it(`allows requirements to be added to a catalog`, async () => {
        const catalog = await Catalog.create({
            ownerId: 1,
            ownerType: OwnerType.ORG,
            name: 'Central Catalog',
        });

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/requirements`)
            .send({
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        requiredField: true,
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                    requiredField: true,
                                }
                            }
                        }
                    }
                }
            });

        const catalogWithRequirements = await Catalog.findByPk(catalog.id, {
            include: [
                {
                    model: ProductRequirements,
                    as: 'productRequirements'
                }
            ]
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(_.get(catalogWithRequirements, 'productRequirements')).toHaveLength(1);
    });

    it(`allows product requirements to be removed from a catalog`, async () => {
        const [
            requirements,
            catalog,
        ] = await Promise.all([
            ProductRequirements.create({
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        requiredField: true,
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                    requiredField: true,
                                }
                            }
                        }
                    }
                }
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'Central Catalog',
            })
        ]);

        await catalog.$add('productRequirements', requirements);

        const response = await supertest(app)
            .delete(`/catalogs/${catalog.id}/requirements/${requirements.id}`);

        const catalogWithRequirements = await Catalog.findByPk(catalog.id, {
            include: [
                {
                    model: ProductRequirements,
                    as: 'productRequirements'
                }
            ]
        });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(_.get(catalogWithRequirements, 'productRequirements')).toHaveLength(0);
    });

    it('Sets product price to parent product price', async () => {
        const {
            catalogs,
        } = await setupProductsAndCatalogs([brandProduct], [orgCatalog, storeCatalog]);
        const parentCatalog = catalogs[0];
        const childCatalog = catalogs[1];
        childCatalog.parentCatalogId = parentCatalog.id;
        await childCatalog.save();

        const organizationProduct = await Product.create(orgProduct);

        const parentCatalogProduct = await CatalogProduct.create({
            productId: organizationProduct.id,
            catalogId: parentCatalog.id,
            price: 420,
        });

        const response = await supertest(app)
        .patch(`/catalogs/${childCatalog.id}/products`)
        .send({
            productIds: [organizationProduct.id],
        });

        expect(response.status).toBe(StatusCodes.CREATED);

        const childCatalogProduct = await CatalogProduct.findOne({
            where: {
                productId: organizationProduct.id,
                catalogId: childCatalog.id
            }
        }) as CatalogProduct;

        expect(childCatalogProduct.price).toBe(parentCatalogProduct.price);
    });

    it('Sets product price to 0 if no parent product price', async () => {
        const {
            catalogs,
        } = await setupProductsAndCatalogs([brandProduct], [orgCatalog, storeCatalog]);
        const parentCatalog = catalogs[0];
        const childCatalog = catalogs[1];
        childCatalog.parentCatalogId = parentCatalog.id;
        childCatalog.ownerType = OwnerType.STORE;
        await childCatalog.save();

        const organizationProduct = await Product.create({
            ...orgProduct
        });

        // parent catalog product
        await CatalogProduct.create({
            productId: organizationProduct.id,
            catalogId: parentCatalog.id,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${childCatalog.id}/products`)
            .send({
                productIds: [organizationProduct.id],
            });

        expect(response.status).toBe(StatusCodes.CREATED);

        const childCatalogProduct = await CatalogProduct.findOne({
            where: {
                productId: organizationProduct.id,
                catalogId: childCatalog.id
            }
        }) as CatalogProduct;

        expect(childCatalogProduct.price).toBe("0.000000");
    });

    it(`merges two products in the same catalog, creating a new product and eliminating the other two`, async () => {
        const [
            product1,
            product2,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, product1.id, testUser),
            addProductToCatalog(storeCatalog, product2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
                    name         : "Ignacio's Munchies",
                    price        : 25.000000,
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                productIds: [product1.id, product2.id],
            });

        const oldProductsInCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog.id,
                productId: [product1.id, product2.id]
            }
        });

        const [
            activeProductsInStoreCatalog,
            deletedProductsInStoreCatalog,
        ] = await Promise.all([
            CatalogProduct.findAll({
                where: {
                    catalogId: storeCatalog.id,
                    status: CatalogProductStatus.ACTIVE
                }
            }),
            CatalogProduct.findAll({
                where: {
                    catalogId: storeCatalog.id,
                    status: CatalogProductStatus.DELETED,
                }
            })
        ]);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(_.uniq(_.map(oldProductsInCatalog, 'status'))).toMatchObject([CatalogProductStatus.DELETED]);
        expect(activeProductsInStoreCatalog).toHaveLength(1);
        expect(activeProductsInStoreCatalog[0]).toMatchObject({
            productId: response.body.id,
        });
        expect(deletedProductsInStoreCatalog).toHaveLength(2);
    });

    it(`merges products in a single catalog together by choosing one correct record, eliminating the others and keeping the true record`, async () => {
        const [
            product1,
            product2,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
        ]);

        await product1.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await product2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await Promise.all([
            addProductToCatalog(storeCatalog, product1.id, testUser),
            addProductToCatalog(storeCatalog, product2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/products/merge/${product1.id}`)
            .send({
                productIds: [product2.id],
            });

        const activeProductsInStoreCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog.id,
                status: CatalogProductStatus.ACTIVE
            }
        });

        const deletedProductsInStoreCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog.id,
                status: CatalogProductStatus.DELETED
            }
        });

        //Need to have reference work done here.
        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeProductsInStoreCatalog).toHaveLength(1);
        expect(activeProductsInStoreCatalog[0]).toMatchObject({
            productId: product1.id,
        });
        expect(deletedProductsInStoreCatalog).toHaveLength(1);
        expect(deletedProductsInStoreCatalog[0]).toMatchObject({
            productId: product2.id,
        });
    });

    it(`merges multiple products to an existing org level product, eliminating the assignment of old products in all indicated catalogs and org catalog`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            orgProduct,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "BlueBerry Cat Snack",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 20.000000,
                size         : "10 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 2,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
            storeCatalog2.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${orgProduct.id}`)
            .send({
                productIds: [storeProduct1.id, storeProduct2.id],
            });

        const activeProductsInStore1Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog1.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInStore2Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog2.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        //Need to have reference work done here.
        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeProductsInStore1Catalog).toHaveLength(1);
        expect(activeProductsInStore1Catalog[0]).toMatchObject({
            productId: orgProduct.id,
        });
        expect(activeProductsInStore2Catalog).toHaveLength(1);
        expect(activeProductsInStore2Catalog[0]).toMatchObject({
            productId: orgProduct.id,
        });
        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(activeProductsInOrgCatalog[0]).toMatchObject({
            productId: orgProduct.id,
        });
    });

    it(`merges products from various stores together from a central catalog creating a new org product and replacing the stores' products in all catalogs`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store 1 Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Store 2 Catalog",
                ownerId:            2,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Store 2 Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await Promise.all([
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
            storeCatalog2.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
        ])

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
                    name         : "Ignacio's Munchies",
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                productIds: [storeProduct1.id, storeProduct2.id]
            });

        const activeProductsInStore1Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog1.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInStore2Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog2.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const newOrgProduct = await Product.findByPk(_.get(_.head(activeProductsInOrgCatalog), 'productId'));

        //Need to have reference work done here.
        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(activeProductsInOrgCatalog[0]).not.toBe(storeProduct1.id);
        expect(activeProductsInOrgCatalog[0]).not.toBe(storeProduct2.id);
        expect(newOrgProduct).toMatchObject({
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
            name         : "Ignacio's Munchies",
            ownerId      : orgCatalog.ownerId,
            ownerType    : orgCatalog.ownerType,
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });
        expect(activeProductsInStore1Catalog).toHaveLength(1);
        expect(activeProductsInStore1Catalog[0]).toMatchObject({
            productId: activeProductsInOrgCatalog[0].productId
        });
        expect(activeProductsInStore2Catalog).toHaveLength(1);
        expect(activeProductsInStore2Catalog[0]).toMatchObject({
            productId: activeProductsInOrgCatalog[0].productId
        });
    });

    it(`merged store products still maintains reference to existing externalId`, async () => {
        const [
            product1,
            product2,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
        ]);

        await product1.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'ABC',
        );

        await product2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'DEF',
        );

        await Promise.all([
            addProductToCatalog(storeCatalog, product1.id, testUser),
            addProductToCatalog(storeCatalog, product2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
                    name         : "Ignacio's Munchies",
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                productIds: [product1.id, product2.id],
            });

        const postMergeProduct1 = await Product.findByPk(product1.id) as Product;
        const postMergeProduct1SellTreezId = await postMergeProduct1.getSellTreezId();

        const postMergeProduct2 = await Product.findByPk(product2.id) as Product;
        const postMergeProduct2SellTreezId = await postMergeProduct2.getSellTreezId();

        expect(postMergeProduct1SellTreezId).toBe('ABC');
        expect(postMergeProduct2SellTreezId).toBe('DEF');

        expect(response.status).toBe(StatusCodes.CREATED);
    });

    it(`merges to existing org product creates a store external reference of org product's externalId`, async () => {
        const [
            storeProduct,
            orgProduct,
            storeCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 2,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            2,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await storeProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'ABC',
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'XYZ',
        );

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            storeCatalog.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${orgProduct.id}`)
            .send({
                productIds: [storeProduct.id],
            });

        const storeExternalReference = await ExternalReference.findOne({
            where: {
                productId: orgProduct.id,
            },
            order: [['createdAt', 'DESC']]
        }) as ExternalReference;

        const postMergeStoreProductSellTreezId = storeExternalReference.externalId;

        expect(response.status).toBe(StatusCodes.CREATED);

        expect(postMergeStoreProductSellTreezId).toBe('XYZ');
    });

    it(`merges org product into another org product does not create another externalReference`, async () => {
        const [
            orgProduct1,
            orgProduct2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await orgProduct2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'XYZ',
        );

        await Promise.all([
            addProductToCatalog(orgCatalog, orgProduct1.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${orgProduct2.id}`)
            .send({
                productIds: [orgProduct1.id],
            });

        const orgExternalReferences = await ExternalReference.findAll({
            where: {
                productId: orgProduct2.id,
                externalId: 'XYZ'
            },
            order: [['createdAt', 'DESC']]
        }) as ExternalReference[];

        expect(response.status).toBe(StatusCodes.CREATED);

        expect(orgExternalReferences.length).toBe(1);
    });

    it(`set product's mergeTo after a merge `, async () => {
        const [
            orgProduct1,
            orgProduct2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await orgProduct1.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'ABC',
        );

        await orgProduct2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'XYZ',
        );

        await Promise.all([
            addProductToCatalog(orgCatalog, orgProduct1.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${orgProduct1.id}`)
            .send({
                productIds: [orgProduct2.id],
            });

        const postMergeProduct1 = await Product.findByPk(orgProduct1.id) as Product;
        const postMergeProduct2 = await Product.findByPk(orgProduct2.id) as Product;

        expect(postMergeProduct1.mergedTo).toBeNull();
        expect(postMergeProduct2.mergedTo).toBe(orgProduct1.id);

        expect(response.status).toBe(StatusCodes.CREATED);
    });

    it(`creates an org product if merging products to a correct record that is owned by a store, eliminating all store owned records assigned to catalogs in indicated catalogs`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 2,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await Promise.all([
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
            storeCatalog2.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${storeProduct1.id}`)
            .send({
                productIds: [storeProduct2.id],
            });

        const activeProductsInStore1Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog1.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInStore2Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog2.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const newOrgProduct = await Product.findByPk(activeProductsInOrgCatalog[0].productId);

        //Need to have reference work done here.
        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(activeProductsInOrgCatalog[0]).not.toBe(storeProduct1.id);
        expect(activeProductsInOrgCatalog[0]).not.toBe(storeProduct2.id);
        expect(newOrgProduct).toMatchObject({
            ownerId: orgCatalog.ownerId,
            ownerType: orgCatalog.ownerType,
        });
        expect(activeProductsInStore1Catalog).toHaveLength(1);
        expect(activeProductsInStore1Catalog[0]).toMatchObject({
            productId: activeProductsInOrgCatalog[0].productId
        });
        expect(activeProductsInStore2Catalog).toHaveLength(1);
        expect(activeProductsInStore2Catalog[0]).toMatchObject({
            productId: activeProductsInOrgCatalog[0].productId
        });
    });

    it(`removes the old products of a merge operation from a central catalog`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 2,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
            addProductToCatalog(orgCatalog, storeProduct1.id, testUser),
            addProductToCatalog(orgCatalog, storeProduct2.id, testUser),
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
            storeCatalog2.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${storeProduct1.id}`)
            .send({
                productIds: [storeProduct2.id],
            });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeProductsInOrgCatalog).toHaveLength(1);
    });

    it(`Reading CatalogProductInformation as a dictionary during a merge into a new product`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store 1 Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Store 2 Catalog",
                ownerId:            2,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Store 2 Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
            storeCatalog2.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
                    name         : "Ignacio's Munchies",
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                productIds: [storeProduct1.id, storeProduct2.id],
                catalogProductInformation: {
                    [storeCatalog1.id]: {
                        price: '10.00',
                        status: CatalogProductStatus.ACTIVE,
                    },
                    [storeCatalog2.id]: {
                        price: '20.00',
                        status: CatalogProductStatus.DEACTIVATED,
                    }
                }
            });

        const activeProductsInStore1Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog1.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInStore2Catalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog2.id,
                status: CatalogProductStatus.DEACTIVATED,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);

        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(activeProductsInOrgCatalog[0].productId).not.toBe(storeProduct1.id);
        expect(activeProductsInOrgCatalog[0].productId).not.toBe(storeProduct2.id);

        expect(activeProductsInStore1Catalog).toHaveLength(1);
        expect(activeProductsInStore1Catalog[0].productId).toBe(activeProductsInOrgCatalog[0].productId);
        expect(activeProductsInStore1Catalog[0].price).toBe('10.000000');
        expect(activeProductsInStore1Catalog[0].status).toBe(CatalogProductStatus.ACTIVE);

        expect(activeProductsInStore2Catalog).toHaveLength(1);
        expect(activeProductsInStore2Catalog[0].productId).toBe(activeProductsInOrgCatalog[0].productId);
        expect(activeProductsInStore2Catalog[0].price).toBe('20.000000');
        expect(activeProductsInStore2Catalog[0].status).toBe(CatalogProductStatus.DEACTIVATED);
    });

    it(`Reading CatalogProductInformation as a dictionary during a merge into an existing product`, async () => {
        const [
            storeProduct,
            orgProduct,
            storeCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            storeCatalog.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${orgProduct.id}`)
            .send({
                productIds: [storeProduct.id],
                catalogProductInformation: {
                    [storeCatalog.id]: {
                        price: '10.00',
                        status: CatalogProductStatus.ACTIVE,
                    }
                }
            });

        const activeProductsInStoreCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);

        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(activeProductsInOrgCatalog[0].productId).toBe(orgProduct.id);

        expect(activeProductsInStoreCatalog).toHaveLength(1);
        expect(activeProductsInStoreCatalog[0].productId).toBe(activeProductsInOrgCatalog[0].productId);
        expect(activeProductsInStoreCatalog[0].price).toBe('10.000000');
        expect(activeProductsInStoreCatalog[0].status).toBe(CatalogProductStatus.ACTIVE);
    });

    it(`Reading CatalogProductInformation as a dictionary during a merge into a brand product`, async () => {
        const [
            storeProduct,
            brandProduct,
            storeCatalog,
            brandCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Store Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 2,
                ownerType    : OwnerType.BRAND,
                price        : 25.000000,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Brand Catalog",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Org 1 Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await storeCatalog.update({
            parentCatalogId: orgCatalog.id
        });

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, testUser),
            addProductToCatalog(orgCatalog, storeProduct.id, testUser),
            addProductToCatalog(brandCatalog, brandProduct.id, testUser),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${brandProduct.id}`)
            .send({
                productIds: [storeProduct.id],
                catalogProductInformation: {
                    [storeCatalog.id]: {
                        price: '10.00',
                        status: CatalogProductStatus.ACTIVE,
                    },
                }
            });

        const activeProductsInStoreCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInBrandCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: brandProduct.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const activeProductsInOrgCatalog = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        const orgCatalogProduct: CatalogProduct | undefined = _.head(activeProductsInOrgCatalog);

        const linkedProduct = Product.findOne({
            where: {
                ownerId: 1,
                ownerType: OwnerType.ORG,
                linkedTo: brandProduct.id
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);

        expect(activeProductsInOrgCatalog).toHaveLength(1);
        expect(_.get(orgCatalogProduct, 'productId')).not.toBe(storeProduct.id);

        expect(activeProductsInStoreCatalog).toHaveLength(1);
        expect(activeProductsInStoreCatalog[0].productId).toBe(activeProductsInOrgCatalog[0].productId);
        expect(activeProductsInStoreCatalog[0].price).toBe('10.000000');
        expect(activeProductsInStoreCatalog[0].status).toBe(CatalogProductStatus.ACTIVE);

        expect(activeProductsInBrandCatalog).toHaveLength(1);
        expect(linkedProduct).toBeDefined();
    });

    it(`Rejects the attempt to merge products if one of the catalogs does not belong to the organization`, async () => {
        const [
            storeProduct1,
            storeProduct2,
            storeCatalog1,
            storeCatalog2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 2,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Store 2 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct1.id, testUser),
            addProductToCatalog(storeCatalog2, storeProduct2.id, testUser),
            storeCatalog1.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge/${storeProduct1.id}`)
            .send({
                productIds: [storeProduct2.id],
            })

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Rejects a store trying to merge an org product into a store product in the store catalog`, async () => {
        const [
            storeProduct,
            orgProduct,
            storeCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Org Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, testUser),
            addProductToCatalog(storeCatalog, orgProduct.id, testUser),
            storeCatalog.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/products/merge/${storeProduct.id}`)
            .send({
                productIds: [orgProduct.id],
            })

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Rejects a store trying to merge an org product into a new product via a merge in the store catalog`, async () => {
        const [
            storeProduct,
            orgProduct,
            storeCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Nacho's Edible Snacks",
                name         : "Nacho's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store 1 Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
            Catalog.create({
                name           : "Test Org Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.ORG,
                parentCatalogId: null,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, testUser),
            addProductToCatalog(storeCatalog, orgProduct.id, testUser),
            storeCatalog.set('parentCatalogId', orgCatalog.id).save(),
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${storeCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "1.000000",
                    brandName    : "Brand Tester",
                    eCommerceName: "Ignacio's, Nacho El Gato, Edible Snacks",
                    name         : "Ignacio's Munchies",
                    size         : "50 piece bucket",
                    subtype      : "Utter Nonsense",
                    type         : "Nonsense",
                    uom          : UOM.EACH,
                },
                productIds: [orgProduct.id],
                catalogIds: [storeCatalog.id],
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    //To maintain backwards compatibility for sellTreez. Needs to be removed eventually

    it('Updates overrides, price, and status to the overrides endpoint', async () => {
        const {
            catalogs,
            products,
        } = await setupProductsAndCatalogs([brandProduct], [orgCatalog]);
        const product = products[0];
        const catalog = catalogs[0];

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/override/${product.id}`)
            .send({
                price: 25.000000,
                status: CatalogProductStatus.DEACTIVATED,
                descriptions: {
                    main: "description override",
                }
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);

        const catalogProduct = await CatalogProduct.findOne({
            attributes: defaultCatalogProductAttributes,
            where: {
                catalogId:      catalog.id,
                productId:      product.id,
            },
        });

        expect(catalogProduct).toMatchObject({
            catalogId       : catalog.id,
            catalogOverrides: {
                descriptions: {
                    main    : "description override",
                }
            },
            price           : "25.000000",
            productId       : product.id,
            status          : CatalogProductStatus.DEACTIVATED,
        })
    });

    it('records a product change in the ProductUpdateOutbox when using overrides endpoint', async () => {
        const {
            catalogs,
            products,
        } = await setupProductsAndCatalogs([brandProduct], [orgCatalog]);
        const product = products[0];
        const catalog = catalogs[0];

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await supertest(app)
            .patch(`/catalogs/${catalog.id}/override/${product.id}`)
            .send({
                price: 25.000000,
                status: CatalogProductStatus.DEACTIVATED,
                descriptions: {
                    main: "description override",
                }
            });

        const productChangeRecords = await ProductUpdateOutbox.findAll();

        const affectedSellTreezIdsProperty = 'affectedSellTreezIds';

        const productChangeRecord = _.head(productChangeRecords);
        const payload = _.get(productChangeRecord, 'payload');
        const affectedSellTreezIds = _.get(payload, affectedSellTreezIdsProperty);

        expect(productChangeRecords).toHaveLength(1);
        expect(payload).toMatchObject({
            changedProductId: product.id,
            affectedCatalogIds: [catalog.id],
            affectedProductIds: [product.id],
        });
        expect(payload).toHaveProperty(affectedSellTreezIdsProperty);
        expect(affectedSellTreezIds).toHaveLength(1);
    });

    it(`updates both the catalog product information and the product information when using catalog route`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            });

        await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            status: CatalogProductStatus.DEACTIVATED,
            price: "24.999999",
            eCommerceName: "Nacho's Edible Snacks",
        });
        expect(orgProduct.get()).toMatchObject({
            eCommerceName: "Nacho's Edible Snacks",
        });
        expect(catalogProduct.get()).toMatchObject({
            status: CatalogProductStatus.DEACTIVATED,
            price: "24.999999",
        })
    });

    it(`it creates a record in the ProductUpdateOutbox table when updating a product using catalog route`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            });

        const ProductUpdateRecords = await ProductUpdateOutbox.findAll();
        const productUpdateRecord = _.head(ProductUpdateRecords);
        const payload = _.get(productUpdateRecord, 'payload');

        expect(ProductUpdateRecords).toHaveLength(1);
        expect(payload).toMatchObject({
            changedProductId:   orgProduct.id,
            affectedProductIds: [orgProduct.id],
            affectedCatalogIds: [orgCatalog.id],
            affectedSellTreezIds: [],
        });
    });

    it(`updates a product that is linked, updating both the product overrides and the owner's value fields themselves`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Real BrandName",
                eCommerceName:      "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const catalogProductUpdates = {
            status: CatalogProductStatus.DEACTIVATED,
            price: 24.999999,
            eCommerceName: "Nacho's Edible Snacks",
        };

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send(catalogProductUpdates);

        await orgProduct.reload();

        //price is put in manually to account for sequelize returning decimals as strings.
        expect(response.body.data).toMatchObject({
            ...catalogProductUpdates,
            price: "24.999999"
        });
        expect(orgProduct.productOverrides).toMatchObject({
            eCommerceName: "Nacho's Edible Snacks",
        });
        expect(orgProduct.eCommerceName).toBe("Nacho's Edible Snacks");
    });

    it(`updates a product in a catalog that is not owned by the catalog owner and just applies catalog overrides`, async () => {
        const [
            brandProduct,
            orgProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Real BrandName",
                eCommerceName: "Nacho's Munchies",
                name         : "Ignacio's Edible Snacks",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${orgProduct.id}`)
            .send({
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            });

        await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(response.body.data).toMatchObject({
            eCommerceName: "Nacho's Edible Snacks",
            price      : "24.999999",
            status     : CatalogProductStatus.DEACTIVATED,
        });
        expect(orgProduct.productOverrides).toMatchObject({});
        expect(orgProduct).toMatchObject(orgProduct.get());
        expect(catalogProduct.catalogOverrides).toMatchObject({
            eCommerceName: "Nacho's Edible Snacks",
        });
    });

    it(`updates weedMapsProductVariantId in the catalog product information when using catalog route`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await orgProduct.addExternalReference(
            ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID,
        );

        const newExternalId = 'new_uuid';

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: newExternalId,
            });

        await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: newExternalId,
        });
    });

    it(`updates non existing weedMapsProductVariantId reference in the catalog product information when using catalog route`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const newExternalId = 'new_uuid';

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: newExternalId,
            });

        await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: newExternalId,
        });
    });

    it(`deletes external reference in the catalog product information`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const newExternalId = uuid();

        await supertest(app)
        .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
        .send({
            [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: newExternalId,
        });

        const deleteExternalReferenceResponse = await supertest(app)
        .delete(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}/externalReference/${newExternalId}`)
        .send();

        expect(deleteExternalReferenceResponse.status).toBe(StatusCodes.ACCEPTED);
    });

    it(`returns error on deleting non-existing external reference in the catalog product information`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const fakeExternalId = uuid();

        const deleteExternalReferenceResponse = await supertest(app)
        .delete(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}/externalReference/${fakeExternalId}`)
        .send();

        expect(deleteExternalReferenceResponse.status).toBe(StatusCodes.NOT_FOUND);
        expect(deleteExternalReferenceResponse.body.message).toContain(
            `External Reference "${fakeExternalId}" Not Found for product "${orgProduct.id}"`
        )
    });

    it('returns an uuid on catalog product create', async () => {
        const orgProduct = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:        "TEST FLOWER 1",
            name:               "TEST FLOWER 1",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               "FLOWER",
            uom:                UOM.G,
        }

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        });

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product/`)
            .send(orgProduct);

        expect(response.body.data.uuid).not.toBeNull();
    });

    it('returns an uuid on catalog products bulk create', async () => {
        const orgProduct = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:        "TEST FLOWER 1",
            name:               "TEST FLOWER 1",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               "FLOWER",
            uom:                UOM.G,
        }

        const orgProduct2 = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:        "TEST CARTRIDGE",
            name:               "TEST CARTRIDGE",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "OTHER",
            type:               "CARTRIDGE",
            uom:                UOM.EACH,
        }

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        })

        const orgProducts = [orgProduct, orgProduct2];

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product/`)
            .send(orgProducts);

        expect(response.body.data).toHaveLength(2);
        expect(response.body.data[0].uuid).not.toBeNull();
        expect(response.body.data[1].uuid).not.toBeNull();
        expect(response.body.data[0].uuid).not.toBe(response.body.data[1].uuid);
    })

    it('returns an uuid on catalog product patch with externalId', async () => {
        const orgProduct = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:        "TEST FLOWER 1",
            name:               "TEST FLOWER 1",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               "FLOWER",
            uom:                UOM.EACH,
        }

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        })

        const createRes = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send(orgProduct);

        const product = createRes.body.data;

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}/${product.uuid}`)
            .send({
                name: "TEST FLOWER 2"
            });

        expect(response.body.data.uuid).toBe(product.uuid);
        expect(response.body.data.name).toBe('TEST FLOWER 2');
    });

    it('it ignores linked product when excludeLinkedProduct query param is passed on a patch request', async () => {
        const [
            orgProduct,
            brandProduct,
        ] =  await Promise.all([
                Product.create({
                    amount:             "1.000000",
                    brandName:          "Store Tester",
                    eCommerceName:      "TEST FLOWER 1",
                    name:               "TEST FLOWER 1",
                    ownerId:            1,
                    ownerType:          OwnerType.ORG,
                    subtype:            "PRE-PACK",
                    type:               "FLOWER",
                    uom:                UOM.EACH,
                }),
                Product.create({
                    amount:             "1.000000",
                    brandName:          "Store Tester",
                    eCommerceName:      "TEST FLOWER BRAND VERSION",
                    name:               "TEST FLOWER 1",
                    ownerId:            1,
                    ownerType:          OwnerType.BRAND,
                    subtype:            "PRE-PACK",
                    type:               "FLOWER",
                    uom:                UOM.EACH,
                })
        ]);

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        })

        await catalog.$add('product', orgProduct);

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        const uuid = await orgProduct.getSellTreezId();

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${orgProduct.id}/${uuid}`)
            .query({
                excludeLinkedProduct: true
            })
            .send({
                name: "TEST FLOWER 2"
            });

        expect(response.body.data).toMatchObject({
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:      "TEST FLOWER 1",
            name:               "TEST FLOWER 2",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               "FLOWER",
            uom:                UOM.EACH,
        });
    })

    //Required for ST backwards compatibility v2.9.7
    it('returns an uuid on catalog product override patch', async () => {

        const product = await Product.create({
            amount:             "1.000000",
            brandName:          "Store Tester",
            eCommerceName:      "TEST FLOWER 1",
            name:               "TEST FLOWER 1",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               "FLOWER",
            uom:                UOM.EACH,
        });

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        });

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await catalog.$add('product', product);

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/override/${product.id}`)
            .send({
                status: "DEACTIVATED"
            });

        expect(response.body.data.uuid).not.toBeUndefined();
        expect(response.body.data.uuid).not.toBeNull();
        expect(response.body.data.status).toBe('DEACTIVATED');
    });

    it('creates external reference for each uuid on catalog products bulk create', async () => {
        const orgProduct = {
            amount       : "1.000000",
            brandName    : "Store Tester",
            eCommerceName: "TEST FLOWER 1",
            name         : "TEST FLOWER 1",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            subtype      : "PRE-PACK",
            type         : "FLOWER",
            uom          : UOM.G,
            uuid         : 'abcd',
        }

        const orgProduct2 = {
            amount       : "1.000000",
            brandName    : "Store Tester",
            eCommerceName: "TEST CARTRIDGE",
            name         : "TEST CARTRIDGE",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            subtype      : "OTHER",
            type         : "CARTRIDGE",
            uom          : UOM.EACH,
            uuid         : 'efgh'
        }

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        })

        const orgProducts = [orgProduct, orgProduct2];

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            .send(orgProducts);

        const product1 = await ExternalReference.findOne({
            where: {
                externalId: 'abcd'
            }
        });

        const product2 = await ExternalReference.findOne({
            where: {
                externalId: 'efgh'
            }
        });

        if (product1 == null
            || product2 == null) {
            fail(`External References for products were not maintained upon import`);
        }

        expect(response.body.data).toHaveLength(2);
        expect(response.body.data[0].uuid).toBe(orgProduct.uuid);
        expect(response.body.data[1].uuid).toBe(orgProduct2.uuid);
    });

    it(`returns a product with all of its overrides, catalog and product overrides, when using an external reference`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "Real BrandName",
                eCommerceName: "Nacho's Munchies",
                name         : "Ignacio's Edible Snacks",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "Brand Tester",
                eCommerceName: "Ignacio's Edible Snacks",
                name         : "Ignacio's Munchies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                subtype      : "Utter Nonsense",
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        const sellTreezId = await orgProduct.getSellTreezId();

        if (sellTreezId == null) {
            fail (`External Reference failed to create`);
        }

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const catalogProductUpdates = {
            status: CatalogProductStatus.DEACTIVATED,
            price: 24.999999,
            eCommerceName: "Nacho's Edible Snacks",
        };

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}/${sellTreezId}`)
            .send(catalogProductUpdates);

        await orgProduct.reload();

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect({...
            response.body.data,
            attributes: null,
            details: null,
        }).toMatchObject({
            ...brandProduct.getLinkableFields(),
            status: CatalogProductStatus.DEACTIVATED,
            price: "24.999999",
            eCommerceName: "Nacho's Edible Snacks",
            uuid: sellTreezId,
        });
    });

    it('allows bulk catalog product create of more than 2 products', async () => {
        const orgProduct = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            displayName:        "TEST FLOWER 1",
            name:               "TEST FLOWER 1",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "PRE-PACK",
            type:               ProductType.FLOWER,
            uom:                UOM.G,
        }

        const orgProduct2 = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            displayName:        "TEST CARTRIDGE",
            name:               "TEST CARTRIDGE",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "OTHER",
            type:               ProductType.CARTRIDGE,
            uom:                UOM.EACH,
        }

        const orgProduct3 = {
            amount:             "1.000000",
            brandName:          "Store Tester",
            cannabis:           false,
            displayName:        "TEST MERCH",
            name:               "TEST MERCH",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            subtype:            "OTHER",
            type:               ProductType.MERCH,
            uom:                UOM.EACH,
        }

        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        })

        const orgProducts = [orgProduct, orgProduct2, orgProduct3];

        const response = await supertest(app)
            .post(`/catalogs/${catalog.id}/product/`)
            .send(orgProducts);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body.data).toHaveLength(3);
    })

    it(`Adds an organization product to a store catalog and generates store level externalReference`, async () => {

        const catalog = await Catalog.create({
            name           : "Test Store Catalog",
            ownerId        : 123456789,
            ownerType      : OwnerType.STORE,
            parentCatalogId: null,
        });

        const product = await Product.create({
            amount       : "1.000000",
            brandName    : "SC",
            eCommerceName: "MAGIC",
            name         : "MAGIC?!",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            price        : "25.000000",
            size         : "2 pack",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        });

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'ABC',
        );

        const response = await supertest(app)
            .patch(`/catalogs/${catalog.id}/products`)
            .send({
                productIds: [product.id],
            });

        const externalReferences = await ExternalReference.findAll({
            where: {
                productId: product.id,
                type: ExternalReferenceType.SELL_TREEZ_ID,
            }
        })

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(externalReferences).not.toBeNull()
        expect(externalReferences).toHaveLength(1);
        expect(externalReferences[0].externalId).toBe('ABC');
    });

    it(`Removes a single value of catalogOverrides`, async () => {
        const [
            brandProduct,
            orgProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "B",
                eCommerceName: "Bob's Cookies",
                name         : "Bob's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Store Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${orgProduct.id}`)
            .send({
                eCommerceName: "Charlie's Cookies",
            });

        await supertest(app)
            .delete(`/catalogs/${storeCatalog.id}/products/${orgProduct.id}/override`)
            .send({
                fields: ['eCommerceName'],
            });

        const [
            updatedCatalogProduct,
            updatedOrgProduct,
        ] = await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(updatedOrgProduct.productOverrides).toMatchObject({});
        expect(updatedOrgProduct).toMatchObject(orgProduct.get());
        expect(updatedCatalogProduct.catalogOverrides.eCommerceName).toBeUndefined();
    });

    it(`Removes a multiple values of catalogOverrides`, async () => {
        const [
            brandProduct,
            orgProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "B",
                eCommerceName: "Bob's Cookies",
                name         : "Bob's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${orgProduct.id}`)
            .send({
                eCommerceName: "Charlie's Cookies",
                details: {
                    doses: 5,
                }
            });

        await supertest(app)
            .delete(`/catalogs/${storeCatalog.id}/products/${orgProduct.id}/override`)
            .send({
                fields: ['eCommerceName', 'doses'],
            });

        const [
            updatedCatalogProduct,
            updatedOrgProduct,
        ] = await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(updatedOrgProduct.productOverrides).toMatchObject({});
        expect(updatedCatalogProduct.catalogOverrides).toMatchObject({});
        expect(updatedCatalogProduct.catalogOverrides.eCommerceName).toBeUndefined();
        expect(updatedCatalogProduct.catalogOverrides.details).toBeUndefined();
    });

    it(`Removes a single value of productOverrides`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "B",
                eCommerceName: "Bob's Cookies",
                name         : "Bob's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                eCommerceName: "Charlie's Cookies",
            });

        await supertest(app)
            .delete(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}/override`)
            .send({
                fields: ['eCommerceName'],
            });

        const [
            updatedCatalogProduct,
            updatedOrgProduct,
        ] = await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(updatedOrgProduct!.productOverrides!.eCommerceName).toBeUndefined();
        expect(updatedCatalogProduct.catalogOverrides).toMatchObject({});
    });

    it(`Removes a multiple values of productOverrides`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "B",
                eCommerceName: "Bob's Cookies",
                name         : "Bob's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                eCommerceName: "Charlie's Cookies",
                details: {
                    doses: 5,
                }
            });

        await supertest(app)
            .delete(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}/override`)
            .send({
                fields: ['eCommerceName', 'doses'],
            });

        const [
            updatedCatalogProduct,
            updatedOrgProduct,
        ] = await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(updatedOrgProduct!.productOverrides!.eCommerceName).toBeUndefined();
        expect(updatedOrgProduct!.productOverrides!.details).toBeUndefined();
        expect(updatedCatalogProduct.catalogOverrides).toMatchObject({});
    });

    it(`creates a record in the ProductUpdateOutbox with all relevant product Ids, sellTreez Ids, and Catalog Ids when updating a brand product linked to many products`, async () => {
        const brandProduct = await Product.create({
            amount       : "0.500000",
            brandName    : 'Treez Testers',
            eCommerceName: 'Brand Product 1',
            name         : 'Brand Product 1',
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : '1g',
            type         : 'CARTRIDGE',
            uom          : 'g',
        });

        const [
            orgProduct,
            store1Product,
            store2Product,
            store2Product2,
            brandCatalog,
            orgCatalog,
            storeCatalog1,
            storeCatalog2,
        ] = await Promise.all([
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Org Product 1',
                linkedTo     : brandProduct.id,
                name         : 'Org Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 1',
                linkedTo     : brandProduct.id,
                name         : 'Store Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 2',
                linkedTo     : brandProduct.id,
                name         : 'Store Product 2',
                ownerId      : 2,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 3',
                linkedTo     : brandProduct.id,
                name         : 'Store Product 3',
                ownerId      : 2,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Catalog.create({
                name     : "brand catalog",
                ownerId  : 1,
                ownerType: OwnerType.BRAND,
            }),
            Catalog.create({
                name     : "org catalog",
                ownerId  : 1,
                ownerType: OwnerType.ORG,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 2,
                ownerType: OwnerType.STORE,
            })
        ])

        const [
            orgSellTreezReference,
            storeSellReference1,
            storeSellReference2,
            storeSellReference3,
        ] = await Promise.all([
            orgProduct.addExternalReference(SELL_TREEZ_ID),
            store1Product.addExternalReference(SELL_TREEZ_ID),
            store2Product.addExternalReference(SELL_TREEZ_ID),
            store2Product2.addExternalReference(SELL_TREEZ_ID),
            addProductToCatalog(brandCatalog, brandProduct.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            addProductToCatalog(storeCatalog1, store1Product.id, testUser),
            addProductToCatalog(storeCatalog2, store2Product.id, testUser),
            addProductToCatalog(storeCatalog2, store2Product2.id, testUser),
        ]);

        await supertest(app)
            .patch(`/catalogs/${brandCatalog.id}/products/${brandProduct.id}`)
            .send({
                ecommerceName: 'The Correct EcommerceName',
            });

        const productUpdateOutboxRecords = await ProductUpdateOutbox.findAll();
        const productUpdateRecord = _.head(productUpdateOutboxRecords);

        const payload = _.mapValues(_.get(productUpdateRecord, 'payload'), (value: string | number | string[] | number[]) => {
            if (Array.isArray(value)){
                return value.sort();
            }
            else {
                return value
            };
        }); ;

        const expectedPayload = {
            changedProductId: brandProduct.id,
            affectedProductIds: [
                brandProduct.id,
                orgProduct.id,
                store1Product.id,
                store2Product.id,
                store2Product2.id,
            ].sort(),
            affectedCatalogIds: [
                brandCatalog.id,
                orgCatalog.id,
                storeCatalog1.id,
                storeCatalog2.id,
            ].sort(),
            affectedSellTreezIds: _.map([
                orgSellTreezReference,
                storeSellReference1,
                storeSellReference2,
                storeSellReference3,
            ], 'externalId').sort()
        };

        expect(productUpdateOutboxRecords).toHaveLength(1);
        expect(productUpdateRecord).toBeDefined();
        expect(payload).toMatchObject(expectedPayload);
    });

    it(`updates visible field through patch api`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                visible: false,
            });

        await Promise.all([
            catalogProduct.reload(),
            orgProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            visible: false,
        });
    });

    it(`updates visible field through patch api - backwards compatibility field visibility - visible`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
                visible:            false,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC`)
            .send({
                visibility: 'visible',
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            visible: true,
        });
    });

    it(`updates visible field through patch api - backwards compatibility field visibility - hidden`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC`)
            .send({
                visibility: 'hidden',
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            visible: false,
        });
    });

    it(`updates visible field through patch api - with mixed backwards compatibility field visibility`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC`)
            .send({
                visibility: 'visible',
                visible: false,
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            visible: false,
        });
    });

    it(`returns visibility on catalog product patch for backward compatibility`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
                visible:            false,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC`)
            .send({
                visibility: 'visible',
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data).toMatchObject({
            visible: true,
            visibility: 'visible',
        });
    });

    it(`returns attributes on catalog product patch with excludeLinkedProduct=true`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
                attributes: {
                    flavor: ['F1'],
                    general: ['G1'],
                },
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC?excludeLinkedProduct=true`)
            .send({
                attributes: {
                    flavor: ['F1', 'F2'],
                }
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data.attributes!.flavor).toStrictEqual([
            "F1",
            "F2",
        ]);
        expect(response.body.data.attributes!.general).toStrictEqual(["G1"]);
    });

    it(`returns attributes on catalog product patch with excludeLinkedProduct=false`, async () => {
        const [
            storeProduct,
            storeCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
                attributes: {
                    flavor: ['F1'],
                    general: ['G1'],
                },
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    null,
            })
        ]);

        const catalogProduct = await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: storeProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await storeProduct.addExternalReference(SELL_TREEZ_ID, "ABC");

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${storeProduct.id}/ABC`)
            .send({
                attributes: {
                    flavor: ['F1', 'F2'],
                }
            });

        await Promise.all([
            catalogProduct.reload(),
            storeProduct.reload(),
        ]);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data.attributes!.flavor).toStrictEqual([
            "F1",
            "F2",
        ]);
        expect(response.body.data.attributes.general).toStrictEqual(["G1"]);
    });

    it(`adds a the newly merged to product into every catalog that had any of the merged to products in`, async () => {
        const [
            orgProduct,
            storeProduct,
            orgCatalog,
            storeCatalog1,
            storeCatalog2,
        ] = await Promise.all([
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Org Product 1',
                name         : 'Org Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 1',
                name         : 'Store Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Catalog.create({
                name     : "org catalog",
                ownerId  : 1,
                ownerType: OwnerType.ORG,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 2,
                ownerType: OwnerType.STORE,
            })
        ]);

        await Promise.all([
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            addProductToCatalog(storeCatalog1, storeProduct.id, testUser),
            addProductToCatalog(storeCatalog2, orgProduct.id, testUser),
            storeCatalog1.update({
                parentCatalogId: orgCatalog.id,
            }),
            storeCatalog2.update({
                parentCatalogId: orgCatalog.id,
            })
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "0.500000",
                    brandName    : 'Treez Brand',
                    eCommerceName: 'Org Product Merged',
                    name         : 'Org Product Merged',
                    ownerId      : 1,
                    size         : '1g',
                    type         : 'CARTRIDGE',
                    uom          : 'g',
                },
                productIds: [orgProduct.id, storeProduct.id],
            });

        const store2CatalogProducts = await CatalogProduct.findAll({
            where: {
                catalogId: storeCatalog2.id,
                status: CatalogProductStatus.ACTIVE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(store2CatalogProducts).toHaveLength(1);
        expect(_.head(store2CatalogProducts)).toMatchObject({
            productId: response.body.id
        })
    });

    it('adds the newly merged product to a catalog only once, even if the operation is merging multiple products together from the same catalog', async () => {
        const [
            orgProduct,
            storeProduct,
            orgCatalog,
            storeCatalog1,
            storeCatalog2,
        ] = await Promise.all([
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Org Product 1',
                name         : 'Org Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 1',
                name         : 'Store Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Catalog.create({
                name     : "org catalog",
                ownerId  : 1,
                ownerType: OwnerType.ORG,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 2,
                ownerType: OwnerType.STORE,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog1, storeProduct.id, testUser),
            addProductToCatalog(orgCatalog, orgProduct.id, testUser),
            addProductToCatalog(storeCatalog1, orgProduct.id, testUser),
            addProductToCatalog(storeCatalog2, orgProduct.id, testUser),
            storeCatalog1.update({parentCatalogId: orgCatalog.id}),
            storeCatalog2.update({parentCatalogId: orgCatalog.id})
        ]);

        const response = await supertest(app)
            .post(`/catalogs/${orgCatalog.id}/products/merge`)
            .send({
                product: {
                    amount       : "0.500000",
                    brandName    : 'Treez Brand',
                    eCommerceName: 'Org Product Merged',
                    name         : 'Org Product Merged',
                    ownerId      : 1,
                    size         : '1g',
                    type         : 'CARTRIDGE',
                    uom          : 'g',
                },
                productIds: [orgProduct.id, storeProduct.id],
            });

        const activeCatalogProducts = await CatalogProduct.findAll({
            where: {
                catalogId: [orgCatalog.id, storeCatalog1.id, storeCatalog2.id],
                status: CatalogProductStatus.ACTIVE
            }
        });

        const activeProductIdsInCatalogs = _.map(activeCatalogProducts, 'productId');

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(activeCatalogProducts).toHaveLength(3);
        expect(activeProductIdsInCatalogs).toMatchObject(Array(3).fill(response.body.id));
    });

    it(`edits the product directly, not applying overrides, when a parent catalog edits a product owned by one of its stores`, async () => {
        const [
            storeProduct,
            storeCatalog,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 1',
                name         : 'Store Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "org catalog",
                ownerId  : 1,
                ownerType: OwnerType.ORG,
            }),
        ]);

        await Promise.all([
            storeCatalog.update({
                parentCatalogId: orgCatalog.id
            }),
            addProductToCatalog(orgCatalog, storeProduct.id, testUser),
            addProductToCatalog(storeCatalog, storeProduct.id, testUser)
        ]);

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${storeProduct.id}`)
            .send({
                brandName: 'Nacho Time',
            });

        await storeProduct.reload();

        const orgCatalogProduct = await CatalogProduct.findOne({
            where: {
                productId: storeProduct.id,
                catalogId: orgCatalog.id
            }
        });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(storeProduct.brandName).toBe('Nacho Time');
        expect(orgCatalogProduct).toBeDefined();
        expect(_.get(orgCatalogProduct, 'catalogOverrides.brandName')).toBeUndefined();
    });

    it(`updates catalogProduct priceTierId through patch api`, async () => {
        const [
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "A",
                eCommerceName:      "A",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const PriceTierTemplate = {
            label: "RipOff Tier",
            ownerId: 91,
            ownerType: OwnerType.ORG,
            isActive: true,
            rangeMode: RangeMode.FIXED_PRICE,
            thresholdType: PriceTierThresholdType.FLAT,
            method: PriceTierMethod.WEIGHT,
            thresholds: [
                {
                    value: 4,
                    start: 1,
                    end: 3.5
                },
                {
                    value: 3,
                    start: 3.5,
                    end: 5
                },
                {
                    value: 1.5,
                    start: 5,
                    end: null
                }
            ]
        }

        const priceTier = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: orgCatalog.id,
            priceTierId: priceTier.id,
        });

        const response = await supertest(app)
            .patch(`/catalogs/${orgCatalog.id}/products/${orgProduct.id}`)
            .send({
                priceTierId: priceTier.id,
            });

        const orgCatalogProduct = await CatalogProduct.findOne({
            where: {
                productId: orgProduct.id,
                catalogId: orgCatalog.id
            }
        });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(orgCatalogProduct!.priceTierId).toBe(priceTier.id);
    });

    it(`returns history for product created through catalog`, async () => {
        // GIVEN a product created for a catalog
        const catalog = await Catalog.create({
            name     : "Test store Catalog",
            ownerId  : 55,
            ownerType: OwnerType.STORE,
        });
        const storeProductWithPrice = {
            amount       : "1.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name         : "ZOMGWHY?!",
            ownerId      : 7220,
            ownerType    : OwnerType.BRAND,
            price        : "420.000000",
            size         : "50 piece bucket",
            subtype      : "Utter Nonsense",
            type         : "Nonsense",
            uom          : UOM.EACH,
        }

        const productResponse = await supertest(app)
            .post(`/catalogs/${catalog.id}/product`)
            // TODO STZ-4409
            // .set('userAuthId', 'superuser')
            .send(storeProductWithPrice);
        const productId = productResponse.body.data.id;

        // WHEN the history is queried for that product
        const historyResponse = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/${productId}/history`);

        // THEN both the product and catalog product history are returned
        expect(historyResponse.status).toBe(StatusCodes.OK);
        const history = historyResponse.body;

        expect(history).toHaveLength(2);
        expect(history[0].description).toBe('Added to Catalog');
        // expect(history[0].userAuthId).toBe('superuser');
        expect(history[1].description).toBe('Created Product');
        // expect(history[1].userAuthId).toBe('superuser');
    });

    it(`returns history for product and catalog changes`, async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Catalog',
                ownerId: 1,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
        ]);

        // History starts here (creation log is tested separately)
        await addProductToCatalog(
            catalog,
            product.id,
            testUser,
            {
                price: 42,
            }
        );

        await supertest(app)
            .patch(`/catalogs/${catalog.id}/products/${product.id}`)
            .send({
                price: 420,
                eCommerceName: "Nachie's Cream",
            });

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/${product.id}/history`);
        const history = response.body;

        expect(history).toHaveLength(3);
        expect(history[0].description).toBe('Price in catalog changed from 42 to 420');
        expect(history[1].description).toBe(`Menu Title changed from 'Nachis Edibles' to 'Nachie's Cream'`);
        expect(history[2].description).toBe('Added to Catalog');
    }); // end of /history tests

    it('Return a catalog product reference by product id - GET /:id/products/:productId', async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Catalog',
                ownerId: 1,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
        ]);

        await addProductToCatalog(catalog, product.id, testUser);

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/${product.id}`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.id).toBe(product.id);
        expect(response.body.name).toBe(product.name);
    })

    it('Return a catalog product reference by external reference externalId - GET /:id/products/:productId', async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Catalog',
                ownerId: 1,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
        ]);

        await product.addExternalReference(SELL_TREEZ_ID, "ABC");
        await addProductToCatalog(catalog, product.id, testUser);

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/ABC`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.id).toBe(product.id);
        expect(response.body.name).toBe(product.name);
    })

    it('Throws StatusCodes.NOT_FOUND if there are no catalog product references returned - GET /:id/products/:productId', async () => {
        const [
            catalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Catalog',
                ownerId: 1,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
        ]);

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/${product.id}`);

        expect(response.status).toBe(StatusCodes.NOT_FOUND);
    })

    it('Throws StatusCodes.CONFLICT if there are multiple catalog product references returned - GET /:id/products/:productId', async () => {
        const [
            catalog,
            product,
            product2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Catalog',
                ownerId: 1,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
            }),
        ]);

        await product.addExternalReference(SELL_TREEZ_ID, "ABC");
        await product2.addExternalReference(SELL_TREEZ_ID, "ABC");
        await addProductToCatalog(catalog, product.id, testUser);
        await addProductToCatalog(catalog, product2.id, testUser);

        const response = await supertest(app)
            .get(`/catalogs/${catalog.id}/products/ABC`);

        expect(response.status).toBe(StatusCodes.CONFLICT);
    });

    it(`correctly records store catalogProduct overrides for organization owned products`, async () => {
        const [
            centralCatalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                name: 'test org',
                ownerId: 89,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                name: 'test product',
                ownerId: 89,
                ownerType: OwnerType.ORG,
                uom: UOM.EACH,
                type: ProductType.CARTRIDGE,
                visible: false,
            })
        ]);

        const storeCatalog = await Catalog.create({
            name: 'test store',
            ownerId: 100,
            ownerType: OwnerType.STORE,
            parentCatalogId: centralCatalog.id,
        })

        const {externalId} = await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID);

        await addProductToCatalog(storeCatalog, product.id, testUser);

        const response = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${product.id}/${externalId}`)
            .send({
                visible: true,
            });


        expect(response.status).toBe(StatusCodes.ACCEPTED)
        expect(response.body).toMatchObject({
            data: {
                visible: true
            }
        });

        const response2 = await supertest(app)
            .patch(`/catalogs/${storeCatalog.id}/products/${product.id}/${externalId}`)
            .send({
                visible: false,
            });

        expect(response2.status).toBe(StatusCodes.ACCEPTED)
        expect(response2.body).toMatchObject({
            data: {
                visible: false
            }
        });
    });

    it(`does not include brand-specific fields when adding a product from BrandTreez`, async () => {
        const [
            retailCatalog,
            brandProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 1000,
                ownerType: OwnerType.STORE,
                name: 'Test Store',
            }),
            Product.create({
                amount       : "50",
                brandName    : "Brand Tester",
                eCommerceName: "Nachis Edibles",
                name         : "Nachi's Edibles",
                ownerId      : 1,
                ownerType    : OwnerType.BRAND,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
                visible      : false,
                externalId   : 'abc123'
            })
        ]);

        const response = await supertest(app)
            .patch(`/catalogs/${retailCatalog.id}/products`)
            .send({
                productIds: [brandProduct.id]
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(1);

        const clonedStoreProduct = response.body[0];

        expect(clonedStoreProduct).toMatchObject({
            brandName    : "Brand Tester",
            eCommerceName: "Nachis Edibles",
            name         : "Nachi's Edibles",
            ownerId      : retailCatalog.ownerId,
            ownerType    : OwnerType.STORE,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        expect(clonedStoreProduct.createdAt).not.toBe(brandProduct.createdAt);
        expect(clonedStoreProduct.updatedAt).not.toBe(brandProduct.updatedAt);
        expect(clonedStoreProduct.externalId).not.toBe(brandProduct.externalId);
        expect(clonedStoreProduct.visible).not.toBe(brandProduct.visible);
    });
});
