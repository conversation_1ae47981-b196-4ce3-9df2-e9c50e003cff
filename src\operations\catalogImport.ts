import _ from "lodash"
import Catalog from "../models/Catalog"
import CatalogPriceTier from "../models/CatalogPriceTier"
import CatalogProduct from "../models/CatalogProduct"
import PriceTier from "../models/PriceTier"
import Product from "../models/Product"
import { CatalogExportData, ExportPriceTiersType, ExportProductType } from "./catalogExport"
import { v4 as uuidv4 } from 'uuid';
import ExternalReference from "../models/ExternalReference"
import { ErrorResponse } from "@treez/dev-pack/errors"
import { StatusCodes } from "http-status-codes"
import { Op, QueryTypes } from 'sequelize';
import { connection } from "@treez/dev-pack/db"
import { validateExternalReferenceIdQuery } from "./catalogCloneQueries"

export interface ExportReport {
    catalogId: number,
    productsCount: number;
    priceTiersCount: number;
    externalReferencesCount: number;
}

export interface CatalogImportParams {
    targetCatalogId?: number;
    targetCatalogName?: string;
    ownerId?: number;
    keepExternalIds: boolean;
}


export class CatalogImportHandler {

    private catalogId: number;
    private productsCount: number = 0;
    private priceTiersCount: number = 0;
    private externalReferencesCount: number = 0;
    private readonly productIdsMapping: Map<number, number>;
    private readonly priceTierIdsMapping: Map<number, number>;

    constructor(private params: CatalogImportParams) {
        this.productIdsMapping = new Map<number, number>();
        this.priceTierIdsMapping = new Map<number, number>();
    }

    public async append(chunk: CatalogExportData): Promise<void> {
        if (!_.isNil(chunk.catalog)) {
            await this.importCatalog(chunk.catalog);
        }
        if (!_.isNil(chunk.priceTiers)) {
            await this.importPricerTier(chunk.priceTiers);
            await this.importCatalogPriceTier(chunk.priceTiers);
        }
        if (!_.isNil(chunk.products)) {
            await this.importProducts(chunk.products);
            await this.importCatalogProduct(chunk.products);
        }
        if (!_.isNil(chunk.externalReferences)) {
            await this.importExternalReferences(this.params.keepExternalIds, chunk.externalReferences);
        }
    }

    public getExportReport(): ExportReport {
        return {
            catalogId: this.catalogId,
            productsCount: this.productsCount,
            priceTiersCount: this.priceTiersCount,
            externalReferencesCount: this.externalReferencesCount
        }
    }

    public async validateExternalReference(externalReferences: ExternalReference[]): Promise<void> {
        const externalIds = "[" + externalReferences.map(reference => '"' + reference.externalId + '"').join(',') + "]";

        const result = await connection.query(validateExternalReferenceIdQuery,
            { type: QueryTypes.SELECT, plain: true, replacements: { externalIds, catalogId: this.params.targetCatalogId } }
        ) as unknown as { count: number };

        if (result.count > 0) {
            throw new ErrorResponse(400, 'external references collisions');
        }
    }

    private async checkCatalogExist(id: number, name: string) {
        const catalog = await Catalog.findOne({
            where: {
                [Op.and]: [
                    { id },
                    { name }
                ]
            }
        });
        if (_.isNull(catalog)) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `catalog id and catalog name must match {id=${id}, name=${name}}`
            );
        }
    }

    private async importCatalog(catalog: Partial<Catalog>): Promise<void> {
        const { targetCatalogId, targetCatalogName, ownerId } = this.params;
        if (!_.isNil(targetCatalogId) && !_.isNil(targetCatalogName)) {
            await this.checkCatalogExist(targetCatalogId, targetCatalogName);
            this.catalogId = targetCatalogId;
        } else {
            const finalCatalogName = targetCatalogName ? targetCatalogName : catalog.name;
            const catalogData = _.omit({ ...catalog, ownerId: (ownerId || catalog.ownerId), name: finalCatalogName }, 'id', "parentCatalogId");
            const newCatalog = await Catalog.create(catalogData);
            this.catalogId = newCatalog.id;
        }
    }

    private async importPricerTier(priceTiers: ExportPriceTiersType): Promise<void> {
        let priceTiersData = priceTiers
            .map(priceTier => ({ ..._.omit(priceTier, 'id', "catalogPriceTiers") }));

        const { ownerId } = this.params;
        if (ownerId) {
            priceTiersData = priceTiersData.map(priceTier => ({ ...priceTier, ownerId }));
        }

        const newPriceTiers = await PriceTier.bulkCreate(priceTiersData);
        priceTiers.forEach((priceTiers, index) => {
            this.priceTierIdsMapping.set(priceTiers.id, newPriceTiers[index].id);
        });
        this.priceTiersCount += newPriceTiers.length;
    }

    private async importCatalogPriceTier(priceTiers: ExportPriceTiersType): Promise<void> {
        const catalogPriceTiersData = priceTiers.map(priceTier => {
            const catalogPriceTierData: CatalogPriceTier = _.get(priceTier, "catalogPriceTiers");
            return _.omit({
                ...catalogPriceTierData,
                catalogId: this.catalogId,
                priceTierId: this.priceTierIdsMapping.get(priceTier.id)
            }, 'id');
        })
        await CatalogPriceTier.bulkCreate(catalogPriceTiersData);
    }

    private async importProducts(products: ExportProductType): Promise<void> {
        let productsData = products.map(product => {
            return { ..._.omit(product, 'id', "catalogProducts", "externalReferences") }
        });
        const { ownerId } = this.params;
        if (ownerId) {
            productsData = productsData.map(data => ({ ...data, ownerId }))
        }
        const newProducts = await Product.bulkCreate(productsData);
        products.forEach((p, index) => {
            this.productIdsMapping.set(p.id, newProducts[index].id) // keeping products id mapping
        });
        this.productsCount += newProducts.length;
    }

    private async importCatalogProduct(products: ExportProductType): Promise<void> {
        const catalogProductsData = products.map(product => {
            const catalogProductData: CatalogProduct = _.get(product, "catalogProducts") || {};
            return {
                productId: this.productIdsMapping.get(product.id),
                status: catalogProductData.status,
                catalogId: this.catalogId,
                price: catalogProductData.price
            }
        });

        await CatalogProduct.bulkCreate(catalogProductsData);
    }

    private async importExternalReferences(keepExternalId: boolean, externalReferences: ExternalReference[]) {
        const referencesToCreate = externalReferences.map(reference => {
            if (keepExternalId) {
                return {
                    type: 'sellTreezId',
                    externalId: reference.externalId,
                    productId: this.productIdsMapping.get(reference.productId),
                    createdAt: new Date(),
                }
            } else {
                return {
                    type: 'sellTreezId',
                    externalId: uuidv4(),
                    productId: this.productIdsMapping.get(reference.productId),
                    createdAt: new Date(),
                }
            }
        });
        await ExternalReference.bulkCreate(referencesToCreate);
        this.externalReferencesCount += referencesToCreate.length;
    }
}
