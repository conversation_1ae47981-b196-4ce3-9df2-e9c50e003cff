import { json<PERSON>ield } from "@treez/dev-pack/db/fieldTypes";
import TreezModel    from "@treez/dev-pack/models/TreezBaseModel";
import _             from "lodash";
import {
    AutoIncrement,
    Column,
    PrimaryKey,
    Table,
}                    from 'sequelize-typescript';
import LRU           from 'lru-cache';
import Product       from "./Product";

const Cache = new LRU(50);
const CAPABILITIES_CACHE_KEY = 'Capabilities';

interface CapabilityRules{
    toType:     string[];
}

export interface CapabilityTransformed {
    name: string,
    rules: CapabilityRules
}

@Table({tableName: 'capabilities'})
export default class Capability extends TreezModel<Capability>{
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                                     number;

    @Column
    type:                                   string;

    @Column
    subtype:                                string;

    @Column
    capability:                             string;

    @Column(jsonField)
    rules:                                  CapabilityRules;
}

export const getAllCapabilities = async () => {
    if (Cache.has(CAPABILITIES_CACHE_KEY)) {
        return Cache.get(CAPABILITIES_CACHE_KEY) as Capability[];
    }

    const capabilities = await Capability.findAll()
        .then((capabilities) => capabilities.map(capability => capability.get()));

    Cache.set(CAPABILITIES_CACHE_KEY, capabilities);

    return capabilities as Capability[];
}

const getCapabilitiesMapKey = (type: string, subtype: string) => `${type}/${subtype}`;

const getMappedCapabilities = async () => {
    const capabilities = await getAllCapabilities();
    const capabilitiesMap = new Map<string, Record<string, CapabilityTransformed>>();

    capabilities.forEach(capabilityRecord => {
        const mapKey = getCapabilitiesMapKey(capabilityRecord.type, capabilityRecord.subtype);
        const {capability: name, rules } = capabilityRecord;

        if(capabilitiesMap.has(mapKey)) {
            const currentCapabilities = capabilitiesMap.get(mapKey)!;
            const newCapabilities = {
                ...currentCapabilities,
                [name]: {
                    name,
                    rules
                }
            };

            capabilitiesMap.set(mapKey, newCapabilities);
        }
        else {
            capabilitiesMap.set(mapKey, {
                [name]: {
                    name,
                    rules
                }
            });
        }

    });

    return capabilitiesMap;
}

export const decorateWithCapabilities = async (products: Product[]) => {
    const capabilitiesMap = await getMappedCapabilities();

    return products.map(product => {
        const mapKey = getCapabilitiesMapKey(product.type, (product.subtype ?? ''));

        if (product.subtype !== null && capabilitiesMap.has(mapKey)) {
            const capabilitiesGrouping = capabilitiesMap.get(mapKey);
            return {
                ...product,
                _internal: {
                    capabilities: {
                        ...capabilitiesGrouping
                    }
                }
            };
        }

        return {
            ...product,
            _internal: {
                capabilities: null
            }
        };
    });
}
