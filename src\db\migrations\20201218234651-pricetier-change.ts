import { QueryInterface }       from 'sequelize';
import {
    allowNull,
    createUpdateColumns,
    intField,
    autoPKField,
    enumField,
    foreignKey,
    jsonField,
    notNull,
    stringField,
}                               from '@treez/dev-pack/db/fieldTypes';
import { PriceTierAction }      from '../../models/PriceTierChange';

const priceTiers                = 'priceTiers';
const priceTierChanges          = 'priceTierChanges';
const priceTierId               = 'priceTierId'
const id                        = 'id';
const priceTierChangeIndex      = 'priceTierChanges_priceTierId_idx';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.createTable(priceTierChanges, {
            id:                 notNull(autoPKField),
            actionType:         notNull(enumField(PriceTierAction)),
            [priceTierId]:      notNull(foreignKey(intField, priceTiers,id)),
            oldPriceTier:       allowNull(jsonField),
            newPriceTier:       notNull(jsonField),
            changes:            notNull(jsonField),
            userAuthId:         allowNull(stringField),
            sellTreezUserId:    allowNull(stringField),
            version:            notNull(intField),

            ...createUpdateColumns
        });

        await queryInterface.addIndex(priceTierChanges, [priceTierId] ,{
            name: priceTierChangeIndex
        });
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.dropTable(priceTierChanges);
    }
}

