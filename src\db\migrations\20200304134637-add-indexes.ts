import { QueryInterface }               from 'sequelize';

export = {
    up: async function( queryInterface: QueryInterface) {

        await queryInterface.sequelize.query(`
            CREATE INDEX type_subtype_idx ON products (type, subtype);
            CREATE UNIQUE INDEX uuid_idx ON products (uuid);

            -- This index was created in some DBs, but not all of them, and is replaced by uuid_idx (above).
            -- It does not need to be replaced in the down migration because it shouldn't be in the DB to begin with.
            DROP INDEX IF EXISTS products_uuid;

            DROP INDEX products_type;
            DROP INDEX products_subtype;

            CREATE UNIQUE INDEX "catalogId_productId_idx" ON "catalogProducts" ("catalogId", "productId");

            CREATE INDEX "ownerId_ownerType_idx" ON catalogs ("ownerId", "ownerType");
            CREATE INDEX "parentCatalogId_idx" ON catalogs ("parentCatalogId");
        `)
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            DROP INDEX type_subtype_idx;
            DROP INDEX uuid_idx;

            CREATE INDEX products_type ON products (type);
            CREATE INDEX products_subtype ON products (subtype);

            DROP INDEX "catalogId_productId_idx";

            DROP INDEX "ownerId_ownerType_idx";
            DROP INDEX "parentCatalogId_idx";
        `)
    },
}
