import { QueryInterface } from 'sequelize';

export = {
  up: async function (queryInterface: QueryInterface) {
    await queryInterface.sequelize.query(`
            UPDATE "products" 
            SET "name" = "name" || ' ' || 'PRODUCT'
            WHERE (details->>'isPayment' = 'false' OR details->'isPayment' IS NULL) and (name ~* '^\\s*PAYMENT\\s+FEE\\s*$' OR name ~* '^\\s*DELIVERY\\s+FEE\\s*$');
        `);
  },

  down: async function (queryInterface: QueryInterface) {
    return;
  },
};
