import { UploadedFile } from "express-fileupload"
import * as fs from 'fs';
import * as readline from 'readline';

export class UploadedFileReader {
    private fileStream: fs.ReadStream;
    private reader: readline.Interface;


    constructor(private file: UploadedFile) { }


    private initReader() {
        this.fileStream = fs.createReadStream(this.file.tempFilePath);
        this.reader = readline.createInterface({
            input: this.fileStream,
            crlfDelay: Infinity
        });
    }

    private destroyReader() {
        this.reader.close();
        this.fileStream.close();
    }

    public async readLines(callback: (line: string) => Promise<void>): Promise<void> {
        this.initReader();
        for await (let line of this.reader) {
            await callback(line);
        }
        this.destroyReader();
    }
}