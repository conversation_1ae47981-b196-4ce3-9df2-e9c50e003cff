import _                                from 'lodash';
import {
    booleanField,
    jsonField,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const PRODUCTS = 'products';

const BARCODES = 'barcodes';
const E_COMMERCE_NAME = 'eCommerceName';
const EXTERNAL_ID = 'externalId';
const PACKAGE_TRACKED = 'packageTracked';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(PRODUCTS, BARCODES, jsonField);
        await queryInterface.addColumn(PRODUCTS, E_COMMERCE_NAME, stringField);
        await queryInterface.addColumn(PRODUCTS, EXTERNAL_ID, stringField);
        await queryInterface.addColumn(PRODUCTS, PACKAGE_TRACKED, booleanField);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn(PRODUCTS, PACKAGE_TRACKED);
        await queryInterface.removeColumn(PRODUCTS, EXTERNAL_ID);
        await queryInterface.removeColumn(PRODUCTS, E_COMMERCE_NAME);
        await queryInterface.removeColumn(PRODUCTS, BARCODES);
    },
}
