import { QueryInterface }   from 'sequelize';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            UPDATE products SET "searchVector" =
            setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce("brandName", '')), 'B') ||
            setweight(to_tsvector('english', coalesce(subtype, '')), 'C') ||
            setweight(to_tsvector('english', coalesce(classification, '')), 'D') ||
            setweight(to_tsvector('english', coalesce(size, '')), 'D') ||
            setweight(to_tsvector('english', coalesce(type, '')), 'D') ||
            setweight(to_tsvector('english', coalesce(attributes->>'effects', '')), 'D') ||
            setweight(to_tsvector('english', coalesce(attributes->>'flavor', '')), 'D') ||
            setweight(to_tsvector('english', coalesce(attributes->>'general', '')), 'D') ||
            setweight(to_tsvector('english', coalesce(attributes->>'ingredients', '')), 'D')
        `)
    },
    down: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            UPDATE products SET "searchVector" =
            setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce("brandName", '')), 'B') ||
            setweight(to_tsvector('english', coalesce(subtype, '')), 'C') ||
            setweight(to_tsvector('english', coalesce(classification, '')), 'D') ||
            setweight(to_tsvector('english', coalesce(size, '')), 'D') ||
            setweight(to_tsvector('english', coalesce(type, '')), 'D')
        `)
    },
};