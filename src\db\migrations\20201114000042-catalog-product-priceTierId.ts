import { QueryInterface }   from 'sequelize';
import {
    intField,
    foreignKey,
}                           from '@treez/dev-pack/db/fieldTypes';

const priceTiers = "priceTiers";
const priceTierId = "priceTierId"
const id = "id";
const catalogProducts = "catalogProducts";
const catalogProductsPriceTierIdIndex = 'catalog_products_priceTierId_idx';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.addColumn(catalogProducts, priceTierId, foreignKey(intField, priceTiers, id));

        await queryInterface.addIndex(catalogProducts, [priceTierId], {
            name: catalogProductsPriceTierIdIndex
        });
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.removeColumn(catalogProducts, priceTierId);

        await queryInterface.removeIndex(catalogProducts, priceTierId);
    }
}