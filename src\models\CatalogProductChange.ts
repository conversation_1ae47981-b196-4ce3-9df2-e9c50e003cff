import { StatusCodes }              from 'http-status-codes';
import _                            from 'lodash';
import {
    AutoIncrement,
    CreatedAt,
    BeforeCreate,
    BelongsTo,
    Column,
    ForeignKey,
    PrimaryKey,
    Table,
    BeforeBulkCreate,
    Sequelize,
}                                   from 'sequelize-typescript';
import { ValidationOptions } from 'sequelize/types/instance-validator';
import {
    enumField,
    jsonField,
    intField,
    stringField,
    autoPKField,
}                                   from '@treez/dev-pack/db/fieldTypes';
import { TreezModel }               from '@treez/dev-pack/models/TreezBaseModel';
import { ErrorResponse }            from '@treez/dev-pack/errors';
import {
    generateChangeset,
    getCatalogChangeDescription,
}                                   from '../lib/productHistory';
import { CatalogProductAction }     from '../lib/sharedInterfaces';
import CatalogProduct               from './CatalogProduct';


@Table({ tableName: 'catalogProductChanges'})
export default class CatalogProductChange extends TreezModel<CatalogProductChange> {
    @AutoIncrement
    @PrimaryKey
    @Column(autoPKField)
    id:                                 number;

    @Column(enumField(CatalogProductAction))
    actionType:                         CatalogProductAction;

    @ForeignKey(() => CatalogProduct)
    @Column({
        allowNull: false,
    })
    catalogProductId:                   number;

    @Column(jsonField)
    newCatalogProduct:                  Partial<CatalogProduct>;

    @Column(jsonField)
    oldCatalogProduct:                  Partial<CatalogProduct>;

    @Column(jsonField)
    changes:                            PropertyChange[];

    @Column(stringField)
    userAuthId:                         string;

    @Column(stringField)
    sellTreezUserId:                    string | null;

    @Column(intField)
    version:                            number;

    @BelongsTo(() => CatalogProduct)
    catalogProduct:                     CatalogProduct;

    @CreatedAt
    @Column({
        allowNull: false,
    })
    createdAt:                          Date;

    @BeforeCreate
    static async versionHistory(instance: CatalogProductChange) {
        const latestVersion = await retrieveLatestCatalogProductVersion(instance.catalogProductId)
        return await this.updateVersion(latestVersion, instance);
    }

    static async updateVersion(latestVersion: number, instance: CatalogProductChange): Promise<CatalogProductChange> {
        if (!Number.isInteger(latestVersion)) {
            instance.version = 1
        }
        else {
            instance.version = latestVersion + 1
        }
        return instance;
    }

    static sortByCatalogProductIdAsc = (a: CatalogProductChange, b: CatalogProductChange): number => a.catalogProductId - b.catalogProductId;

    @BeforeBulkCreate
    static async versionHistories(instances: CatalogProductChange[]) {
        const latestChanges = await retrieveAllCatalogProductVersion(_.map(instances, 'catalogProductId'));

        // latest changes and current sorted share the same index
        latestChanges.sort(this.sortByCatalogProductIdAsc);
        instances.sort(this.sortByCatalogProductIdAsc);

        // take the latest change's version and assign it to current catalogProductChange
        return await instances.map((e, index) => this.updateVersion(latestChanges[index]?.version, e))
    }

    public validate(options?: ValidationOptions) {
        if (_.includes(Object.values(CatalogProductAction), this.actionType)) {
            return super.validate(options);
        }
        else {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid enum value "${this.actionType}" for actionType!`
            );
        }
    }
}


const getPropertyChanges = (
    actionType: CatalogProductAction,
    newCatalogProduct: Partial<CatalogProduct>,
    oldCatalogProduct?: Partial<CatalogProduct>): PropertyChange[] | undefined => {
    let changes: PropertyChange[] = [];
    if (actionType !== CatalogProductAction.CREATE) {
        changes = generateChangeset(
            newCatalogProduct?.toJSON?.() ?? newCatalogProduct,
            oldCatalogProduct?.toJSON?.() ?? oldCatalogProduct,
            getCatalogChangeDescription,
        );
        if (actionType === CatalogProductAction.UPDATE && changes.length === 0) {
            return  ;
        }
    }

    return changes 

}

export const recordCatalogProductChange = async (
    catalogProductId    : number,
    actionType          : CatalogProductAction,
    userInfo            : UserInfo,
    newCatalogProduct   : Partial<CatalogProduct>,
    oldCatalogProduct?  : Partial<CatalogProduct>,
): Promise<CatalogProductChange | undefined> => {

    const changes = getPropertyChanges(actionType, newCatalogProduct, oldCatalogProduct);

    if (changes) {
        return await CatalogProductChange.create({
            actionType,
            catalogProductId,
            changes,
            newCatalogProduct,
            oldCatalogProduct,
            userAuthId: userInfo.userAuthId,
            sellTreezUserId: userInfo.sellTreezUserId,
        } as Partial<CatalogProductChange>);
    }
};

export const bulkRecordCatalogProductChanges = async (
    userInfo: UserInfo,
    catalogProductSet: {
        actionType: CatalogProductAction,
        catalogProductId: number,
        newCatalogProduct: Partial<CatalogProduct>,
        oldCatalogProduct: Partial<CatalogProduct>
    }[]): Promise<void> => {

        const catalogProductChanges = catalogProductSet.reduce((result, current) => {            
            const {
                catalogProductId,
                newCatalogProduct,
                oldCatalogProduct,
                actionType
            } = current;

            const changes = getPropertyChanges(actionType, newCatalogProduct, oldCatalogProduct);
            
            if(changes) {
                result.push({
                    actionType,
                    catalogProductId,
                    changes,
                    newCatalogProduct,
                    oldCatalogProduct,
                    userAuthId: userInfo.userAuthId,
                    sellTreezUserId: userInfo.sellTreezUserId,
                });
            }
            return result;

        }, [] as Partial<CatalogProductChange>[]);
        
        await CatalogProductChange.bulkCreate(catalogProductChanges);
}


export const retrieveLatestCatalogProductVersion = async (catalogProductId: number): Promise<number> => {
    return await CatalogProductChange.max("version", {
        where: {
              catalogProductId
        }
    })
}

export const retrieveAllCatalogProductVersion = async (catalogProductIds: number[]) => {
    return await CatalogProductChange.findAll({
        attributes: [[Sequelize.fn('max', Sequelize.col('version')), 'version'], "catalogProductId"],
        where: {
            catalogProductId: catalogProductIds,
        },
        group: ["catalogProductId"]
    });
}