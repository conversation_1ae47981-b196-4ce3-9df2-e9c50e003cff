import {
    QueryInterface,
}                                       from 'sequelize';
import logger                           from '@treez/dev-pack/logger';

const catalogs            = 'catalogs';
const priceTiers          = 'priceTiers';
const productRequirements = 'productRequirements';
const products            = 'products';

export = {

    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await fixIds(queryInterface, productRequirements);
            await fixIds(queryInterface, priceTiers);
            await fixIds(queryInterface, products);
            await fixIds(queryInterface, catalogs);
        });
    },

    down: async(_queryInterface: QueryInterface) => {
        //There is no return from this. 
        logger.warn(`Migration 20210513856910-foreignKeyAlignment2 cannot be reverted`);
    },
};

const fixIds = async (queryInterface: QueryInterface, tableName: string) => {
    logger.info(`normalizing entity IDs for ${tableName}...`);

    await queryInterface.sequelize.query(`
        UPDATE
            "${tableName}"
        SET "ownerId" = (
            RIGHT("ownerId" ::VARCHAR, 3) ::INTEGER
        )
        WHERE
            "ownerType" IS NOT NULL
            AND
            "ownerId" IS NOT NULL
        ;
    `);
};
