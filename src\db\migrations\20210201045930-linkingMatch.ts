import { QueryInterface }           from 'sequelize';
import {
    intField,
    enumField,
    createUpdateColumns,
    autoPKField,
    foreignKey,
    notNull,
    decimalField,
}                                   from '@treez/dev-pack/db/fieldTypes';
import { MatchType }                from '../../models/SuggestedLink';
import weightedLinksCreationQuery   from '../queries/weightedLinksCreation';

const SuggestedLinks = 'suggestedLinks';
const Products = 'products';
const id = 'id';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.createTable(SuggestedLinks, {
            id               : autoPKField,
            productToLinkId  : notNull(foreignKey(intField, Products, id)),
            brandProductId   : notNull(foreignKey(intField, Products, id)),
            rankWeight       : decimalField,
            type             : notNull(enumField(MatchType)),
            ...createUpdateColumns,
        });

        await queryInterface.sequelize.query(weightedLinksCreationQuery);

        await queryInterface.addIndex(SuggestedLinks, ['productToLinkId']);
        await queryInterface.addIndex(SuggestedLinks, ['brandProductId']);
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.dropTable(SuggestedLinks)
    },
}
