{"openapi": "3.0.0", "info": {"title": "Treez Product API", "version": "1.0.0"}, "paths": {"/catalog_products": {"patch": {"summary": "updates the status of multiple products across multiple catalogs. If a centralCatalogId is provided then the central catalog product and all child catalog products associated with that centralCatalogId also have their status automatically updated.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#components/schemas/CatalogProduct"}}}}, "responses": {"202": {"description": "Accepted changes to product statuses across all catalogs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CatalogProduct"}}}}, "400": {"description": "Must have at least one catalog and product in the request."}}}, "get": {"summary": "Gets all catalogs for an entity", "parameters": [{"in": "query", "name": "ownerId", "schema": {"type": "integer", "items": {"type": "integer"}}, "required": true}, {"in": "query", "name": "ownerType,", "schema": {"type": "string", "items": {"type": "integer"}, "enum": ["store", "brand", "organization"]}, "required": true}], "responses": {"200": {"description": "Retreived catalogs for ownerId and type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CatalogProduct", "type": "object", "properties": {"count": {"title": "Catalog Count", "type": "integer"}, "data": {"title": "Catalogs", "type": "array", "items": {"$ref": "#/components/schemas/Catalog"}}}}}}}, "400": {"description": "BAD REQUEST- must have ownerId and ownerType identified"}, "401": {"description": "UNAUTHENTICATED- must have bearer token and be authenticated user"}}, "tags": ["/catalogs"]}, "delete": {"summary": "Marks a catalogProduct DELETED and records a catalogProductChange", "parameters": [{"in": "path", "name": "catalogId", "schema": {"type": "integer"}, "required": true}, {"in": "path", "name": "productId", "schema": {"type": "integer"}, "required": true}], "responses": {"204": {"description": "Found catalogProduct successfully deleted"}, "400": {"description": "Path paramaters catalogId and productId must be valid integers"}, "404": {"description": "No catalogProduct was found with supplied catalogId and productId"}}}}, "/catalogs/${id}": {"delete": {"summary": "Deletes a catalog. Treez Only", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"204": {"description": "catalog deleted."}, "403": {"description": "Forbidden. Must be <PERSON>z"}}}, "patch": {"summary": "Updates a catalog by id", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"202": {"description": "ACCEPTED - Updates accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "401": {"description": "UNAUTHORIZED - User must be authorized user"}, "403": {"description": "FORBIDDEN - User must have UPDATE CATALOG permissions for catalog or parent catalog"}}}}, "/catalogs/${id}/products": {"delete": {"summary": "Removes a product from a catalog and optionally its children catalogs", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"204": {"description": "Product soft \"deleted\" (status set as DELETED)"}}}}, "/catalogs/{id}": {"get": {"summary": "gets a catalog by id", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "OK- Retrieved catalog by id", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"title": "Catalog Retrieved", "$ref": "#/components/schemas/Catalog"}}}}}}, "401": {"description": "UNAUTHORIZED- User must have been authorized and request had bearer token"}}}}, "/catalogs/${id}/retailPresence": {"get": {"summary": "Gets a brand catalog's retail presence", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "OK- returns a catalogs adoption data", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CatalogCarryingBrandProduct"}}}}}, "401": {"description": "UNAUTHORIZED - must be authorized user"}, "403": {"description": "FORBIDDEN - must have read catalog permission"}}}}, "/catalogs/{id}/override/{productId}": {"patch": {"summary": "Updates a product with catalog specific information such as status and product overrides.", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}, {"in": "path", "name": "productId", "schema": {"type": "integer"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/requestBodies/ProductOverrides"}}}}, "responses": {"202": {"description": "ACCEPTED - changes accepted, catalog specific information applied.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "401": {"description": "UNAUTHORIZED - must be authorized user"}, "403": {"description": "FORBIDDEN - must have Update Catalog permission"}}}}, "/catalogs/{id}/products": {"patch": {"summary": "Adds a product to a catalog and optionally its children catalogs", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "id of the catalog to add products to"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"productIds": {"type": "array", "items": {"type": "integer"}}, "childCatalogIds": {"type": "array", "items": {"type": "integer"}}, "catalogInformation": {"type": "object", "description": "catalog specific information"}}}}}}, "responses": {"201": {"description": "Created Product(s) in catalog(s)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "401": {"description": "UNAUTHORIZED - must be authenticated user"}, "403": {"description": "FORBIDDEN - must have update priveldges on parent"}}}}, "/catalogs/{id}/products/{productId}": {"patch": {"summary": "Updates only catalog specific information", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true, "description": "catalog id"}, {"in": "path", "name": "productId", "schema": {"type": "integer"}, "required": true, "description": "product Id"}], "responses": {"202": {"description": "Accepted changes ok"}}}, "get": {"summary": "Gets a product as it appears in a particular catalog", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "description": "id of catalog", "required": true, "schema": {"type": "integer"}}, {"in": "path", "name": "productId", "description": "id of the product to retreive", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "OK - Product Retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInCatalog"}}}}, "401": {"description": "UNAUTHORIZED - User must be authenticed user"}}}}, "/catalogs/{id}/products/{productId}/{externalId}": {"patch": {"summary": "Updates a product and returns the external id referenced in the request path. Made to maintain dependency from SellTreez.", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "description": "id of the catalog to apply the update to", "required": true, "schema": {"type": "integer"}}, {"in": "path", "name": "productId", "description": "id of the product to edit", "required": true, "schema": {"type": "integer"}}, {"in": "path", "name": "externalId", "description": "The SellTreez UUID of reference for the product.", "schema": {"type": "string", "format": "uuid"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/requestBodies/ProductOverrides"}}}}, "responses": {"202": {"description": "ACCEPTED - changes applied", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "401": {"description": "UNAUTHORIZED - must be authorized user"}, "403": {"description": "FORBIDDEN - must have UpdateCatalog permission"}}}}, "/catalogs/{id}/requirements/{requirementsId}": {"delete": {"summary": "Removes product requirements from a catalog and optionally any of its children catalogs", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "description": "id of catalog", "schema": {"type": "integer"}, "required": true}, {"in": "path", "name": "requirementsId", "description": "id of the requirements you want to remove from the catalog", "schema": {"type": "integer"}, "required": true}], "responses": {"202": {"description": "ACCEPTED - Requirements removed from catalog"}, "401": {"description": "UNAUTHORIZED - User must be authorized"}, "403": {"description": "FORBIDDEN - User must have UPDATE CATALOG permissions for catalog"}}}, "post": {"summary": "Adds an already created set of requirements to a catalog. Optionally can apply the requirements to children catalogs", "tags": ["/catalogs", "Requirements"], "parameters": [{"in": "path", "name": "id", "description": "id of the catalog to add the requirements to", "required": true, "schema": {"type": "integer"}}, {"in": "path", "name": "requirementsId", "description": "id of the requirements to apply to the catalog", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"childCatalogIds": {"type": "array", "items": {"type": "integer"}}}}}}}, "responses": {"202": {"description": "ACCEPTED - products applied to catalog(s)"}, "401": {"description": "UNAUTHORIZED - User must be authorized"}, "403": {"description": "FORBIDDEN - User must have UPDATE CATALOG permissions"}}}}, "/catalogs": {"post": {"summary": "Creates a new catalog", "tags": ["/catalogs"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "name of the catalog"}, "parentCatalogId": {"type": "integer", "description": "id of the parent catalog"}, "ownerId": {"type": "integer", "description": "the id of the entity that owns this catalog"}, "ownerType": {"type": "string", "description": "the type of owner", "enum": ["brand", "organization", "store"]}}}}}}, "responses": {"201": {"description": "CREATED - catalog was created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "401": {"description": "UNAUTHORIZED - user must be authorized"}}}}, "/catalogs/{id}/product": {"post": {"summary": "Creates a new product and adds it to its \"native\" catalog", "tags": ["/catalogs"], "parameters": [{"in": "path", "name": "id", "description": "id of the catalog that is the native catalog for the product", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInCatalog"}}}}, "responses": {"201": {"description": "CREATED - Product was created and added to catalog", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInCatalog"}}}}, "401": {"description": "UNAUTHORIZED - User must be authenticated"}, "403": {"description": "FORBIDDEN - User must have UPDATE CATALOG permission on native catalog"}}}}, "/catalogs/{id}/products/merge": {"post": {"summary": "Merges products together into an identified catalog", "description": "merge existing products into one new product. catalogProductInformation is a dictionary object with the catalog as key and catalog specific information as the value.", "tags": ["/catalogs", "Merge Products"], "parameters": [{"in": "path", "description": "id of the catalog", "name": "id", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/requestBodies/MergeToNewProductRequestBody"}}}}, "responses": {"201": {"description": "CREATED - products merged to new product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInCatalog"}}}}, "401": {"description": "UNAUTHORIZED - User must be authenticated"}, "403": {"description": "FORBIDDEN - Requestor must have UPDATE CaTALOG permission for catalog id'd"}}}}, "/catalogs/{id}/products/merge/{productId}": {"post": {"summary": "Merges products to an existing product", "description": "merge existing products into one existing product. Products' native catalogs must share a parent catalog. catalogProductInformation is a dictionary object with the catalog as key and catalog specific information as the value.", "tags": ["/catalogs", "Merge Products"], "parameters": [{"in": "path", "description": "the id of the catalog that is to be the native catalog for the merged product", "name": "id", "required": true, "schema": {"type": "integer"}}, {"in": "path", "description": "id of the product that all other products are supposed to merge to", "name": "productId", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/requestBodies/MergeToExistingProductRequestBody"}}}}, "responses": {"201": {"description": "CREATED - Products were merged and catalog products created"}, "401": {"description": "UNAUTHORIZED - Must be authenticated user"}, "403": {"description": "FORBIDDEN - Must have UPDATE CATALOG permission on native catalog for target catalog"}}}}, "/catalogs/{id}/requirements": {"post": {"summary": "Creates product requirements and adds them to a catalog", "description": "Creates the native catalog for product requirements", "tags": ["/catalogs", "Requirements"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#components/schemas/ProductRequirements"}}}}, "responses": {"201": {"description": "CREATED - requirements created and applied to catalog."}, "401": {"description": "UNAUTHORIZED - Must be authenticated user"}, "403": {"description": "FORBIDDEN - User must have UPDATE CATALOG permission"}}}}, "/operations/organizations/${organizationId}": {"post": {"summary": "Creates a new central catalog.  This takes the central catalog name to create a new organization catalog with ownerId of the organizationId param and assigns all store owned products to it. An optional parameter of promoting a catalog is available.", "tags": ["operations", "treez-only"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "description": "the id of the organization as saved in Directory-API", "schema": {"type": "string"}}], "requestBody": {"$ref": "#/components/requestBodies/CreateOrganizationRequestBody"}, "responses": {"201": {"description": "Organization central catalog created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "400": {"description": "Could be for bad catalogIdToPromote where the catalog identified isn't also in the storeCatalogIds, could also be that the organization already has a catalog. If that is the case you should use that /operations/${organizationId}/addStores route"}, "403": {"description": "Must be <PERSON>z"}}}}, "/operations/cloneCatalog/${id}": {"post": {"summary": "creates a clone of a catalog.  Catalog being cloned must have a parent catalog. All ownership of products in the catalog that is being cloned will be passed to the organization. Ownership is passed to the organization as cloning a catalog, or passing a store product to another store, is an implicit adoption of that product at the organizational level.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "the id of the catalog to be saved.", "schema": {"type": "string"}}, {"name": "cloneRequest", "in": "body", "required": true, "description": "the body of the cloning request", "schema": {"$ref": "#/components/schemas/CloneCatalogRequest"}}], "consumes": ["application/json"], "responses": {"201": {"description": "new catalog created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "400": {"description": "Could be because the catalog that was being cloned did not have a parent catalog"}, "403": {"description": "Must be <PERSON>z"}}}}, "/operations/export_catalog/${id}": {"get": {"summary": "export all catalog data into JSON format", "parameters": [{"name": "id", "in": "path", "required": true, "description": "the id of the catalog to be exported", "schema": {"type": "number"}}], "responses": {"201": {"description": "catalog successfully exported", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "403": {"description": "Must be <PERSON>z"}, "404": {"description": "Could be because the catalog that was being cloned does not exists"}}}}, "/operations/import_catalog": {"post": {"summary": "import catalog data from json to the storage", "responses": {"201": {"description": "catalog successfully exported", "content": {"multipart/form-data": {"schema": {"ownerId": {"type": "number"}, "catalogId": {"type": "number"}, "catalogName": {"type": "string"}, "file": {"type": "string"}}}}}, "400": {"description": "Could be because the targetCatalogId already exists"}, "403": {"description": "Must be <PERSON>z"}}}}, "/operations/organization/${organizationId}/stores": {"post": {"summary": "adds stores to a central catalog", "tags": ["operations", "treez-only"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "description": "the id organization whose catalog should be the parent of the store catalog ids identified in the body", "schema": {"type": "string"}}], "requestBody": {"$ref": "#/components/requestBodies/AddStoresToOrganizationCatalog"}, "responses": {"201": {"description": "new catalog created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Catalog"}}}}, "400": {"description": "Could be because the catalog that was being cloned did not have a parent catalog"}, "403": {"description": "Must be <PERSON>z"}}}}, "delete": {"/catalog_products": {"delete": {"summary": "Marks a list of catalogProducts DELETED and records a catalogProductChange", "parameters": [{"in": "path", "name": "catalogId", "schema": {"type": "integer"}, "required": true}, {"in": "path", "name": "productId", "schema": {"type": "integer"}, "required": true}]}}}, "/products/{id}": {"patch": {"summary": "Updates Product Information", "tags": ["/products"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "number"}, "description": "id of product we want to update"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/requestBodies/ProductOverrides"}}}}, "responses": {"202": {"description": "Accepted the Updates", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "403": {"description": "Must have update access on catalog that owns the product, is hierarchical access"}}}, "get": {"summary": "Gets Product Information by Id", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "number"}, "description": "id of product we want to update"}, {"name": "catalogId", "in": "query", "required": false, "schema": {"type": "number"}, "description": "optional parameter to retrieve the presence a product has in a particular catalog with overrides and pricing applied"}, {"name": "include", "in": "query", "required": false, "schema": {"type": "string", "enum": ["catalog"]}, "description": "related models to include"}], "responses": {"200": {"description": "Product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}}}}, "/products/{id}/link/{brandProductId}": {"patch": {"summary": "Links a retail product to a brand product", "tags": ["/products"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "id of the product we want to link"}, {"in": "path", "name": "brandProductId", "required": true, "schema": {"type": "integer"}, "description": "id of brand product we want to link the retail product to"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#components/requestBodies/ProductOverrides"}}}}, "responses": {"202": {"description": "Link and Overrides, if any, accepted", "content": {"application/json": {"schema": {"$ref": "#components/schemas/Product"}}}}, "403": {"description": "Must have Update Catalog permission on the native catalog of the Product"}}}}, "/products/{id}/unlink": {"patch": {"summary": "Unlink a specific product", "tags": ["/products"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "id of the product we want to unlink", "schema": {"type": "string"}}], "responses": {"202": {"description": "unlinked product", "content": {"application/json": {"schema": {"$ref": "#components/schema/Product"}}}}}}}, "/products/${id}/discontinue": {"patch": {"summary": "Discontinues a brand product", "parameters": [{"in": "path", "name": "id", "schema": {"type": "integer"}, "required": true}], "responses": {"202": {"description": "Brand product discontinued"}, "400": {"description": "Product ownerType must be a 'brand'"}}}}, "/products": {"get": {"summary": "Search for products", "tags": ["/products"], "responses": {"202": {"description": "products that match query", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}}, "/products/search": {"post": {"summary": "Search for products via POST request", "tags": ["/products"], "responses": {"202": {"description": "products that match query", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}}, "/products/brands": {"get": {"summary": "gets all brands", "tags": ["/product"], "responses": {"202": {"description": "Brand Info", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"ownerId": {"type": "string"}, "brandName": {"type": "string"}}}}}}}}}}, "/products/distinct": {"get": {"summary": "get list of products that match distinct values", "tags": ["/products"], "parameters": [{"in": "query", "name": "attributes", "required": true, "schema": {"type": "string"}, "description": "product attributes"}, {"in": "query", "name": "catalogId", "schema": {"type": "string"}, "description": "product catalogId"}, {"in": "query", "name": "fields", "schema": {"type": "string"}, "description": "product fields"}, {"in": "query", "name": "productIds", "schema": {"type": "string"}, "description": "list of product Id's"}], "responses": {"200": {"description": "products that match the query", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}}, "/products/mergedProductIds": {"get": {"summary": "Search for product ids", "parameters": [{"name": "productIds", "in": "query", "required": true, "schema": {"type": "string"}, "description": "a comma delimitered list of product Id's"}], "tags": ["product"], "responses": {"200": {"description": "merged products that match product Id's", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}}}, "components": {"schemas": {"CatalogCarryingBrandProduct": {"properties": {"brandProducts": {"title": "Number of brand products present", "type": "number"}, "catalogId": {"title": "The id of the catalog where brand products are present", "type": "number"}, "catalogName": {"title": "The name of the catalog", "type": "string"}, "catalogOwnerType": {"title": "The type of owner for the catalog", "type": "string", "enum": ["store", "organization"]}}}, "Catalog": {"properties": {"id": {"title": "Catalog Unique Identifier/Primary Key", "type": "integer"}, "name": {"title": "Name of the Catalog", "type": "string"}, "ownerId": {"title": "Unique Identifier of the Owner Type", "type": "number"}, "ownerType": {"title": "Type of Entity that owns the Catalog", "type": "string", "enum": ["brand", "organization", "store"]}, "parentCatalogId": {"title": "Parent Catalog Id", "type": "integer"}}, "required": ["name", "ownerId", "ownerType"]}, "CatalogProduct": {"properties": {"id": {"title": "Catalog Product Id", "type": "integer"}, "catalogid": {"title": "Catalog Id", "type": "integer"}, "catalogOverrides": {"description": "Catalog Specific information for the product", "title": "Catalog Overrides", "$ref": "#/components/schemas/Product"}, "price": {"title": "Price", "type": "number", "minimum": 0}, "priceTierId": {"title": "Price Tier Id", "type": "integer"}, "productId": {"title": "Product Id", "type": "integer"}, "status": {"title": "Product Status", "type": "string", "enum": ["ACTIVE", "DEACTIVATED", "DELETED"]}}}, "ProductInCatalog": {"allOf": [{"$ref": "#/components/schemas/CatalogProduct"}, {"$ref": "#/components/schemas/Product"}]}, "ProductDetails": {"properties": {"cbdMg": {"title": "CBD (mg)", "type": "number"}, "doseCbdMg": {"title": "CBD per dose", "type": "number"}, "doses": {"title": "Do<PERSON>", "type": "number"}, "doseThcMg": {"title": "THC per dose", "type": "number"}, "extractionMethod": {"title": "Extraction Method", "type": "string"}, "strain": {"title": "Strain", "type": "string"}, "thcMg": {"title": "THC (mg)", "type": "number"}, "totalFlowerWeight": {"title": "Total Flower Weight", "type": "number"}}}, "ProductAttributes": {"properties": {"effect": {"title": "Effects", "type": "array", "items": {"type": "string"}}, "flavor": {"title": "Flavors", "type": "array", "items": {"type": "string"}}, "general": {"title": "General", "type": "array", "items": {"type": "string"}}, "ingredient": {"title": "Ingredients", "type": "array", "items": {"type": "string"}}}}, "Product": {"properties": {"id": {"title": "Product Id", "type": "integer"}, "amount": {"title": "Amount", "type": "integer", "nullable": true}, "attributes": {"$ref": "#/components/schemas/ProductAttributes"}, "barcodes": {"title": "Barcodes", "type": "object", "nullable": true}, "brandName": {"title": "Brand", "type": "string", "nullable": true}, "cannabis": {"title": "Cannabis", "type": "boolean", "nullable": true}, "classification": {"title": "Classification", "type": "string", "nullable": true}, "displayName": {"title": "Display Name", "type": "string", "nullable": true}, "descriptions": {"title": "Descriptions", "type": "object", "nullable": true}, "details": {"$ref": "#/components/schemas/ProductDetails"}, "eCommerceName": {"title": "eCommerce Name", "type": "string", "nullable": true}, "images": {"type": "array", "items": {"type": "object", "properties": {"default": {"type": "boolean"}, "url": {"type": "string"}}}}, "msrp": {"title": "MSRP", "type": "number", "nullable": true}, "price": {"title": "Price", "type": "number"}, "name": {"title": "Name", "type": "string"}, "packageTracked": {"title": "Package Tracked", "type": "boolean", "nullable": true}, "productShortCode": {"title": "Product Short Code", "type": "string", "nullable": true}, "size": {"title": "Size", "type": "string", "nullable": true}, "sku": {"title": "SKU", "type": "string", "nullable": true}, "subtype": {"title": "Subtype", "type": "string", "nullable": true}, "tpc": {"title": "TPC", "type": "string", "nullable": true}, "type": {"title": "Type", "type": "string"}, "uom": {"title": "Unit of Measure", "type": "string"}, "upc": {"title": "UPC", "type": "string"}}}, "ProductRequirements": {"description": "Requirements to make a product valid or not", "properties": {"id": {"title": "id of the record", "type": "number"}, "ownerId": {"title": "entityId of the owner", "type": "number"}, "ownerType": {"title": "type of owner", "type": "string", "enum": ["brand", "organization", "store"]}, "description": {"title": "description of the requirements", "type": "string"}, "requirements": {"title": "requirements JSON object", "type": "object", "description": "JSON Schema of requirements"}}}, "CloneCatalogRequest": {"properties": {"name": {"title": "name of the new catalog", "type": "string"}, "ownerId": {"title": "The owner id", "type": "number"}, "ownerType": {"title": "the ownerType", "type": "string", "enum": ["store", "organization"]}, "parentCatalogId": {"title": "The parent Catalog Id", "type": "number"}}}, "SearchFilter": {"properties": {"field": {"type": "string"}, "fuzzy": {"type": "boolean"}, "not": {"type": "boolean"}, "values": {"type": "array", "items": {"type": "string"}}}}}, "requestBodies": {"CatalogProductsStatusUpdate": {"description": "Object to communicate what products in which catalogs to update to a singular status", "content": {"application/json": {"schema": {"type": "object", "properties": {"catalogIds": {"title": "Catalog Ids", "type": "array", "items": {"type": "number"}}, "productIds": {"title": "Product Ids", "type": "array", "items": {"type": "number"}}, "status": {"title": "Product Status", "type": "string", "enum": ["ACTIVE", "DELETED", "DEACTIVED"]}}}}}}, "MergeToNewProductRequestBody": {"description": "Merge existing products, productIds, into new product. Give catalog specific information by catalog, the key in dictionary.", "content": {"application/json": {"schema": {"type": "object", "properties": {"product": {"description": "final merge product", "$ref": "#/components/schemas/Product"}, "productIds": {"description": "ids of the products to merge", "type": "array", "items": {"type": "integer"}}, "catalogProductInformation": {"description": "catalog specific information for each any of the catalogs the product is to be assigned", "type": "object"}}}}}}, "MergeToExistingProductRequestBody": {"description": "Merge existing products, productIds, into new product. Give catalog specific information by catalog, the key in dictionary.", "content": {"application/json": {"schema": {"type": "object", "properties": {"productIds": {"description": "ids of the products to merge", "type": "array", "items": {"type": "integer"}}, "catalogProductInformation": {"description": "catalog specific information for each any of the catalogs the product is to be assigned", "type": "object"}}}}}}, "CreateOrganizationRequestBody": {"description": "Object that consists of the store catalog ids that are to create the central catalog along with an optional promote catalog value that makes all products in a particular catalog organization products.", "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"title": "Organization Catalog Name", "type": "string"}, "storeCatalogIds": {"type": "array", "items": {"type": "number"}}, "catalogIdToPromote": {"type": "number"}}, "required": ["name", "storeCatalogIds"]}}}}, "AddStoresToOrganizationCatalog": {"description": "Catalog Ids to add to organization's central catalog", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "number"}}}}}, "ProductOverrides": {"description": "product overrides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "DiscontinueProductRequestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"isDiscontinued": {"title": "IsDiscontinued", "type": "boolean"}}}}}}, "SearchCriteria": {"description": "search configuration", "content": {"application/json": {"schema": {"type": "object", "properties": {"brandMatch": {"type": "string"}, "catalogId": {"type": "number"}, "dateRange": {"type": "string"}, "excludeLinkedProduct": {"type": "boolean"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/SearchFilter"}}, "include": {"type": "array", "items": {"type": "string"}}, "offset": {"type": "number"}, "orderByRank": {"type": "boolean"}, "orgId": {"type": "number"}, "page": {"type": "number"}, "pageSize": {"type": "number"}, "productIds": {"type": "array", "items": {"type": "number"}}, "searchBrands": {"type": "boolean"}, "searchString": {"type": "string"}, "sortBy": {"type": "array", "items": {"type": "string"}}, "status": {"type": "array", "items": {"type": "string"}}, "weights": {"type": "string"}}}}}}}}, "tags": []}