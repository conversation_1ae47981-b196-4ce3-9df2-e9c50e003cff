import { StatusCodes }          from 'http-status-codes';
import _                        from 'lodash';
import { ValidationOptions } from 'sequelize/types/instance-validator';
import {
    AutoIncrement,
    BeforeCreate,
    Column,
    CreatedAt,
    ForeignKey,
    PrimaryKey,
    Table,
}                               from 'sequelize-typescript';
import {
    enumField,
    jsonField,
    intField,
    stringField,
}                               from '@treez/dev-pack/db/fieldTypes';
import { ErrorResponse }        from '@treez/dev-pack/errors';
import { TreezModel }           from '@treez/dev-pack/models/TreezBaseModel';
import { generateChangeset }    from '../lib/productHistory';
import { ProductAction }        from '../lib/sharedInterfaces';
import Product                  from './Product';

@Table({ tableName: 'productChanges' })
export default class ProductChange extends TreezModel<ProductChange> {
    @AutoIncrement
    @PrimaryKey
    @Column({
        allowNull: false,
    })
    id:                         number;

    @Column(
        enumField(ProductAction)
    )
    actionType:                 ProductAction;

    @Column(jsonField)
    newProduct?:                Partial<Product>;

    @Column(jsonField)
    oldProduct?:                Partial<Product>;

    @Column(jsonField)
    mergedFrom?:                number[];

    @Column(jsonField)
    changes?:                   PropertyChange[];

    @ForeignKey(() => Product)
    @Column({
        allowNull: false,
    })
    productId:                  number;

    @Column({
        allowNull: false,
    })
    userAuthId:                 string;

    @Column(stringField)
    sellTreezUserId:            string | null;

    @Column(intField)
    version:                    number;

    @CreatedAt
    @Column({
        allowNull: false,
    })
    createdAt:                  Date;

    @BeforeCreate
    static async versionHistory(instance: ProductChange) {
        const latestVersion = await retrieveLatestProductVersion(instance.productId)

        if (latestVersion == null
            || _.size(latestVersion) < 1) {
                instance.version = 1
        }
        else {
            instance.version = latestVersion[0].version + 1
        }

        return instance;
    }

    public validate(options?: ValidationOptions) {
        if (_.includes(Object.values(ProductAction), this.actionType)) {
            return super.validate(options);
        }
        else {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid enum value "${this.actionType}" for actionType!`
            );
        }
    }
}

export const retrieveLatestProductVersion = async (productId: number) => {
    return await ProductChange.findAll({
        limit: 1,
        where: {
            productId
        },
        order: [['version', 'DESC']]
    });
}

export const recordChange = async (
    productId:              number,
    actionType:             ProductAction,
    userInfo:               UserInfo,
    newProduct:             Partial<Product> | undefined,
    oldProduct?:            Partial<Product>,
    mergedFrom?:            number[],
): Promise<ProductChange | undefined> => {
    let changes: PropertyChange[] = [];

    if (actionType !== ProductAction.CREATE) {
        changes = generateChangeset(
            newProduct?.toJSON?.() ?? newProduct,
            oldProduct?.toJSON?.() ?? oldProduct,
        );
        if (actionType === ProductAction.UPDATE && changes.length === 0) {
            return undefined;
        }
    }

    const productChange: Partial<ProductChange> = {
        changes,
        actionType,
        productId,
        newProduct,
        oldProduct,
        userAuthId: userInfo.userAuthId,
        sellTreezUserId: userInfo.sellTreezUserId,
        mergedFrom,
    };

    return await ProductChange.create(productChange);
};
