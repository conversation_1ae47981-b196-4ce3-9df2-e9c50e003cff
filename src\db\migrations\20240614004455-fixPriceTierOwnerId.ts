import { QueryInterface } from "sequelize";
import { Sequelize } from "sequelize-typescript";

export = {
    up: async function (queryInterface: QueryInterface, sequelize: Sequelize) {
        await queryInterface.sequelize.query(`
            UPDATE "priceTiers" pt
            SET "ownerId" = c."ownerId"
            FROM "catalogPriceTiers" cpt
            JOIN "catalogs" c ON c.id = cpt."catalogId"
            WHERE pt.id = cpt."priceTierId"
            AND c.id = pt."ownerId"; 
        `)
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
    },
};
