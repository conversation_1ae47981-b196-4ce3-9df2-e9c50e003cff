import { QueryInterface }               from 'sequelize';

export = {
    up: async function( queryInterface: QueryInterface) {

        await queryInterface.sequelize.query(`
            BEGIN;

                CREATE TYPE status AS ENUM ('ACTIVE','DISABLED', 'DELETED');
            
                alter table "catalogProducts"
                    add column "status" status DEFAULT 'ACTIVE' not null;
            
                update "catalogProducts" set "status" = 'ACTIVE' where active = true;
            
                update "catalogProducts" set "status" = 'DISABLED' where active = false;
            
                alter table "catalogProducts"
                    drop column active;
            
            COMMIT;
        `)
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            BEGIN;
                DROP TYPE status;
                
                alter table "catalogProducts"
                    add column "active" boolean;
            
                update "catalogProducts" set "active" = true where status = 'ACTIVE';
            
                update "catalogProducts" set   active = false where "status" = 'DISABLED' ;
            
                alter table  "catalogProducts"
                    alter column "active" SET not null;
            
                alter table "catalogProducts"
                    drop column status;
            
            COMMIT;
        `)
    },
}
