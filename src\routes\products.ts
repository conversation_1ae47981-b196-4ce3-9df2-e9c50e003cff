import {
    NextFunction,
    Request,
    Response,
    Router,
}                                          from 'express';
import { StatusCodes }                     from 'http-status-codes';
import _, { Dictionary }                                   from 'lodash';
import Papa                                from 'papa<PERSON><PERSON>';
import { QueryTypes, ValidationError }                 from 'sequelize';
import {
    OwnerType,
    ProductField,
    ProductDetailsField,
    ProductAttributesField,
    CatalogField,
}                                         from "@treez/commons/sharedTypings/product";
import { AuthenticatedRequest }            from '@treez/dev-pack/auth';
import {
    connection,
    transaction,
}                                          from '@treez/dev-pack/db';
import {
    sendErrorResponse,
    ErrorResponse,
}                                          from '@treez/dev-pack/errors';
import logger                              from '@treez/dev-pack/logger';
import {
    getIdsFromRequestQuery,
    getUserInfo,
}                                           from '../lib/expressUtils';
import { ProductPermission }                from '../lib/permissions';
import {
    checkProductPermissions,
    checkPermissionsOnMultipleProducts,
}                                           from '../middleware';
import CatalogProduct, {
    getDistinctCatalogProductValues,
    updateCatalogProduct,
}                                           from '../models/CatalogProduct';
import Product, {
    findProductById,
    getProductWithOverrides,
    linkProducts,
    ProductRecord,
    unlinkProduct,
    updateProduct,
}                                           from '../models/Product';
import SuggestedLink                        from '../models/SuggestedLink';
import ProductUpdateOutbox                  from '../models/ProductUpdateOutbox';
import { TypeOfProductChanges }             from '../pulsar/producer';
import searchProducts, {
    downloableProductsGenerator,
    getCatalogIdList,
    searchQueryToGetCatalogIdsMaxPriceByProductId,
    searchQueryToGetPriceTiersByProductId
}                                           from '../search';
import {
    SearchCriteria,
    SearchType,
}                                           from '../search/searchUtils';
import { EPMProductUploadRequest, uploadProducts } from '../operations/EPMImportProducts';
import { assertAllNotNil }                  from '../lib/commonFunctions';
import Catalog from "../models/Catalog";

/**
 * @swagger
 *   components:
 *     requestBodies:
 *          ProductOverrides:
 *             description: product overrides
 *             content:
 *                 application/json:
 *                     schema:
 *                         $ref: '#/components/schemas/Product'
 *          DiscontinueProductRequestBody:
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              isDiscontinued:
 *                                  title: "IsDiscontinued"
 *                                  type: boolean
 */

const DEFAULT_EXPORT_COLUMNS = [
    _.startCase(ProductField.PRODUCT_ID),
    _.startCase(CatalogField.CATALOG_ID),
    _.startCase(ProductField.BRAND_NAME),
    _.startCase(ProductField.NAME),
    _.startCase(ProductField.PRODUCT_TYPE),
    _.startCase(ProductField.SUBTYPE),
    _.startCase(ProductField.AMOUNT),
    _.startCase(ProductField.UNIT_OF_MEASURE),
    _.startCase(ProductField.MENU_TITLE),
    _.startCase(ProductField.CLASSIFICATION),
    _.startCase(ProductDetailsField.EXTRACTION_METHOD),
    _.startCase(ProductDetailsField.THC_MG),
    _.startCase(ProductDetailsField.CBD_MG),
    _.startCase(ProductField.SIZE),
    _.startCase(ProductDetailsField.DOSES),
    _.startCase(ProductDetailsField.DOSE_THC_MG),
    _.startCase(ProductDetailsField.TOTAL_WEIGHT),
    _.startCase(ProductField.EXTERNAL_ID),
    _.startCase(ProductField.PRICING),
    _.startCase(ProductField.PRICE_TIER),
    _.startCase(ProductAttributesField.GENERAL),
    _.startCase(ProductAttributesField.FLAVOR),
    _.startCase(ProductAttributesField.EFFECT),
    _.startCase(ProductAttributesField.INGREDIENT),
    _.startCase(ProductAttributesField.INTERNAL),
    _.startCase(ProductField.PRODUCT_BARCODES),
    _.startCase(ProductField.DESCRIPTIONS),
    _.startCase(ProductField.HIDE_FROM_MENU),
    _.startCase(ProductField.IMAGE_ID),
    _.startCase(ProductField.RELATIVE_PATH),
];

const {
    UpdateCatalog,
    ReadCatalog,
} = ProductPermission;

const route = Router();

/**
 * @swagger
 *  paths:
 *      /products/{id}:
 *          patch:
 *              summary: 'Updates Product Information'
 *              tags:
 *                - /products
 *              parameters:
 *                - in: path
 *                  name: id
 *                  required: true
 *                  schema:
 *                      type: number
 *                  description: id of product we want to update
 *              requestBody:
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/requestBodies/ProductOverrides'
 *              responses:
 *                  202:
 *                      description: Accepted the Updates
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Product'
 *                  403:
 *                      description: Must have update access on catalog that owns the product, is hierarchical access
 *
 */
route.patch('/:id',
checkProductPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    const {
        body: updates,
        params,
    } = request;

    const { id: productId } = params;

    const userInfo = getUserInfo(request);

    try {
        const data =  await transaction( async () => {
            const product = await updateProduct(
                Number(productId),
                updates,
                userInfo,
            );

            let productResult: any = product.get();

            //Required for ST backwards compatibility v2.9.7
            //This needs to be deprecated before org products can be created otherwise stores will be editing each others prices

            if (product.ownerType === OwnerType.STORE && updates.price != null) {
                const catalogProduct = await CatalogProduct.findOne({
                    where: {
                        productId
                    }
                });

                if (catalogProduct) {
                    const updatedCatalogProduct = await updateCatalogProduct(
                        catalogProduct.catalogId,
                        catalogProduct.productId,
                        userInfo,
                        {
                            price: updates.price
                        }
                    );

                    productResult = {
                        ...productResult,
                        price: updatedCatalogProduct.price,
                        status: updatedCatalogProduct.status,
                    };
                }
            }

            //Required for ST backwards compatibility v2.9.7
            const sellTreezId = await product.getSellTreezId();

            await ProductUpdateOutbox.createRecord(product.id, product.ownerType, TypeOfProductChanges.UPDATE);

            return {
                ...productResult,
                uuid: sellTreezId
            };
        });

        return response.status(StatusCodes.ACCEPTED).send({
            data
        });
    }
    catch (error) {
        logger.error("Error patching product on product api in products: ", error);

        if (error instanceof ValidationError) {
            return sendErrorResponse(response, new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Unable to process entity: ${error.message}`
            ));
        }
        else {
            return sendErrorResponse(response, error);
        }
    }
});

/**
 * @swagger
 *  paths:
 *      /products/{id}/link/{brandProductId}:
 *          patch:
 *              summary: Links a retail product to a brand product
 *              tags:
 *                - /products
 *              parameters:
 *                - in: path
 *                  name: id
 *                  required: true
 *                  schema:
 *                      type: integer
 *                  description: id of the product we want to link
 *                - in: path
 *                  name: brandProductId
 *                  required: true
 *                  schema:
 *                      type: integer
 *                  description: id of brand product we want to link the retail product to
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#components/requestBodies/ProductOverrides'
 *              responses:
 *                  202:
 *                      description: Link and Overrides, if any, accepted
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#components/schemas/Product'
 *                  403:
 *                      description: Must have Update Catalog permission on the native catalog of the Product
 */
route.patch('/:id/link/:brandProductId',
checkProductPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
    } = request;

    const {
        id,
        brandProductId,
    } = params;

    const userInfo = getUserInfo(request);

    try {
        const linkedProduct = await transaction( async () => {
            return await linkProducts(
                Number(id),
                Number(brandProductId),
                userInfo,
                _.omit(
                    body,
                    [
                        'id',
                        'ownerType',
                        'ownerId',
                        'linkedTo',
                    ]
                ) as Partial<Product>
            );
        });

        response.status(StatusCodes.ACCEPTED).json(linkedProduct);
    }
    catch (error) {
        logger.error(`Error linking product ${id} to ${brandProductId}`);
        return sendErrorResponse(response, error)
    }
});

/**
 * @swagger
 *  paths:
 *      /products/{id}/unlink:
 *          patch:
 *              summary: Unlink a specific product
 *              tags:
 *                - /products
 *              parameters:
 *                - name: id
 *                  in: path
 *                  required: true
 *                  description: id of the product we want to unlink
 *                  schema:
 *                      type: string
 *              responses:
 *                  202:
 *                      description: unlinked product
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#components/schema/Product'
 */
route.patch('/:id/unlink',
checkProductPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        params: {
            id
        }
    } = request;

    const userInfo = getUserInfo(request);

    try {
        const product = await transaction( async () => {
            return await unlinkProduct(Number(id), userInfo);
        })

        response.status(StatusCodes.ACCEPTED).json(product);
    }
    catch (error) {
        logger.error(`Error unlinking product ${id}`, error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *   components:
 *     requestBodies:
 *          ProductOverrides:
 *             description: product overrides
 *             content:
 *                 application/json:
 *                     schema:
 *                         $ref: '#/components/schemas/Product'
 *          DiscontinueProductRequestBody:
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              isDiscontinued:
 *                                  title: "IsDiscontinued"
 *                                  type: boolean
 */
interface DiscontinueProductRequestBody {
    isDiscontinued: boolean;
}

/**
 * @swagger
 *  paths:
 *      /products/${id}/discontinue:
 *          patch:
 *              summary: Discontinues a brand product
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  202:
 *                      description: Brand product discontinued
 *                  400:
 *                      description: Product ownerType must be a 'brand'
 *
 */
route.patch('/:id/discontinue',
checkProductPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const { id } = request.params;
    const {isDiscontinued = true} = request.body as DiscontinueProductRequestBody

    try {
        const product = await findProductById(Number(id));

        if (product.ownerType !== OwnerType.BRAND) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `The product is not a brand owned product`
            );
        }

        const updatedProduct = await product.update({
            visible: !isDiscontinued
        });

        response.status(StatusCodes.ACCEPTED).send(updatedProduct);
    }
    catch (error) {
        logger.error(`Error updating the "visible" field on brand product ${id}`, error);
        sendErrorResponse(response, error);
    }
})

/**
 * @swagger
 *  paths:
 *      /products:
 *          get:
 *              summary: Search for products
 *              tags:
 *                  - /products
 *              responses:
 *                  202:
 *                      description: products that match query
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: array
 *                                  items:
 *                                      $ref: '#/components/schemas/Product'
 *
 */
route.get('/',
async (request: Request, response: Response) => {
    try {
        logger.debug('Request endpoint reached for search endpoint....');
        const query = request.query.query || request.body; // support GET body for cases of complex/loaded querying

        if (query == null) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Missing required parameters.  Either "query" (for a search) or "productIds" (for specific products) must be included in the request query parameters.'
            );
        }

        let searchQuery: SearchCriteria;

        try {
            searchQuery = (typeof query == 'string') ? JSON.parse(query) : query;
        }
        catch (error) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid/Unparsable query format: ${error}`
            );
        }

        const results = await searchProducts(searchQuery);

        response.status(StatusCodes.OK).send(results);
    }
    catch (error) {
        logger.error("Error searching on product api: ", error);
        return sendErrorResponse(response, error);
    }
});


/**
 * @swagger
 *  paths:
 *      /importProductsEPM:
 *          post:
 *              summary: import new and existing product to epm
 *              responses:
 *                  202:
 *                      description: Products where imported successfuly
 *                  400:
 *                      description: Invalid body parameters
 *                  500:
 *                      description: When something goes wrong processing the request
 */
route.post('/importProductsEPM', async (request: AuthenticatedRequest, response: Response) => {
    const requestBody = request.body as EPMProductUploadRequest;
    const { catalogId, productUploadList } = requestBody;

    logger.debug("executed with params: ", request.body);

    try {
        assertAllNotNil([catalogId, productUploadList],
            "Missing required parameters.");

        const userInfo = getUserInfo(request);

        await uploadProducts(requestBody, userInfo);

        response.status(StatusCodes.OK);
        response.end();
    } catch (error) {
        return sendErrorResponse(response, error);
    }
});


/**
 * @swagger
 *  paths:
 *      /products/search:
 *          post:
 *              summary: Search for products via POST request
 *              tags:
 *                  - /products
 *              responses:
 *                  202:
 *                      description: products that match query
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: array
 *                                  items:
 *                                      $ref: '#/components/schemas/Product'
 *
 */
route.post('/search',
async (request: Request, response: Response) => {
    try {
        logger.debug('Request endpoint reached for POST search endpoint....');
        const searchQuery : SearchCriteria = request.body;

        if (searchQuery == null) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Missing required parameters.  The query must be included in the request body.'
            );
        }

        const results = await searchProducts(searchQuery);

        response.status(StatusCodes.OK).send(results);
    }
    catch (error) {
        logger.error("Error searching on product api: ", error);
        return sendErrorResponse(response, error);
    }
});

route.get('/adoption',
checkPermissionsOnMultipleProducts('query', 'productIds', [[ReadCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    const { productIds } = request.query as { productIds: string };

    try {
        if (!_.isString(productIds) || _.isEmpty(productIds)) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid product ids identified in request query. Request must have at least one product id identified`
            );
        }
        else {
            const productsToGetAdoptionFor = _.chain(productIds)
            .split(',')
            .map(_.toNumber)
            .value();

            const adoption = await Product.getProductsAdoptionMetrics(productsToGetAdoptionFor);

            response.status(StatusCodes.OK).send(adoption);
        }
    }
    catch (error) {
        logger.error(`error getting adoption metrics for products ${productIds}`, error);
        return sendErrorResponse(response, error);
    }
})

route.post(`/:id/suggestedLink/:productToLinkId`,
checkProductPermissions('productToLinkId', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    const {
        id: brandProductId,
        productToLinkId,
    } = request.params;

    try {

        const suggestedMatchRecordCreated = await SuggestedLink.addBrandSuggestedLink(
            Number(brandProductId),
            Number(productToLinkId)
        );

        response.status(StatusCodes.CREATED).send(suggestedMatchRecordCreated)
    }
    catch(error) {
        logger.error(`Error posting brand suggested match for brand product ${brandProductId} for the retail product ${productToLinkId}`, error);
        return sendErrorResponse(response, error);
    }
});

route.get('/brandSuggestedLinks',
checkPermissionsOnMultipleProducts('query', 'productIds', [[ReadCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    const {productIds} = request.query;

    try {
        const productIdsToSearch = getIdsFromRequestQuery('productIds', request);

        const output = await SuggestedLink.getProductMatches(productIdsToSearch);

        response.status(StatusCodes.OK).send(output);
    }
    catch (error) {
        logger.error(`Error getting brand suggested matches for products ${productIds}`, error)
        return sendErrorResponse(response, error);
    }
})

route.get('/download',
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    response.setTimeout(0)
    try {
        const query = request.query.query || request.body; // support GET body for cases of complex/loaded querying

        if (query == null) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Missing required parameters.  Either "query" (for a search) or "productIds" (for specific products) must be included in the request query parameters.'
            );
        }

        let searchQuery: SearchCriteria;

        try {
            searchQuery = (typeof query == 'string') ? JSON.parse(query) : query;
        }
        catch (error) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid/Unparsable query format: ${error}`
            );
        }

        const { requestor } = request;
        const missingCatalogIdAndRequestorNotTreez = !searchQuery.catalogId && !requestor?.isTreez();

        if (missingCatalogIdAndRequestorNotTreez) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Missing required parameters. "catalogId" must be included in the request query parameters.'
            )
        }

        response.set('content-type', 'text/csv');
        let enableHeader = true;

        const catalogIds = (await connection.query(getCatalogIdList,
            {
                raw: true,
                type: QueryTypes.SELECT,
                model: Catalog,
                replacements: { catalogId: searchQuery.catalogId }
            })).map(catalog => catalog.id);

        for await (let result of downloableProductsGenerator(searchQuery.catalogId!)) {
            let productIdList: Array<number> = [];
            let productsData = _.get(result, 'data');
            productsData.forEach((product: Product) => {
                productIdList.push(_.get(product, 'id'));
            });

            const productIdsStr = "[" + productIdList.join(',') + "]";
            const queryResults = await connection.query(searchQueryToGetCatalogIdsMaxPriceByProductId,
                {
                    raw: true,
                    type: QueryTypes.SELECT,
                    model: CatalogProduct,
                    replacements: { productIdsStr, catalogIds },
                }
            );

            const ptQueryResults = await connection.query(searchQueryToGetPriceTiersByProductId,
                {
                    raw: true,
                    type: QueryTypes.SELECT,
                    replacements: { productIdsStr, catalogIds },
                }
            );

            const finalResult = _.reduce(
                queryResults,
                (result: Dictionary<string>, row) => {
                    let key = _.get(row, 'productId');
                    let values = _.get(row, 'catalogId') + ':' + _.get(row, 'price');
                    result[key] = values;
                    return result;
                },
                {} as Dictionary<string>
            );

            const priceTiers = ptQueryResults;
            const flattenProducts = _.map(_.get(result, 'data'), (product: ProductRecord) => {
                return {
                    'Product Id': _.get(product, 'id'),
                    'Catalog Id': _.split(_.get(finalResult, _.get(product, 'id')), ':')[0],
                    'Brand Name': _.get(product, 'brandName', ''),
                    'Name': _.get(product, 'name'),
                    'Product Type': _.get(product, 'type', ''),
                    'Subtype': _.get(product, 'subtype', ''),
                    'Amount': _.get(product, 'amount', ''),
                    'Unit Of Measure': _.get(product, 'uom', ''),
                    'Menu Title': _.get(product, 'eCommerceName', ''),
                    'Classification': _.get(product, 'classification', ''),
                    'Extraction Method': _.get(product, 'details.extractionMethod', ''),
                    'Thc Mg': _.get(product, 'details.thcMg', ''),
                    'Cbd Mg': _.get(product, 'details.cbdMg', ''),
                    'Size': _.get(product, 'size', ''),
                    'Doses': _.get(product, 'details.doses', ''),
                    'Dose Thc Mg': _.get(product, 'details.doseThcMg', ''),
                    'Total Weight': _.get(product, 'details.totalFlowerWeight', ''),
                    'External Id': _.get(product, 'externalId', ''),
                    'Pricing':
                        _.get(_.find(priceTiers, { productId: _.get(product, 'id') }), 'label') != undefined
                            ? 'TIER'
                            : 'FLAT',
                    'Price Tier':
                        _.get(_.find(priceTiers, { productId: _.get(product, 'id') }), 'label') != undefined
                            ? _.get(_.find(priceTiers, { productId: _.get(product, 'id') }), 'label', '')
                            : _.split(_.get(finalResult, _.get(product, 'id')), ':')[1],
                    'General': _.get(product, 'attributes.general', ''),
                    'Flavor': _.get(product, 'attributes.flavor', ''),
                    'Effect': _.get(product, 'attributes.effect', ''),
                    'Ingredient': _.get(product, 'attributes.ingredient', ''),
                    'Internal': _.get(product, 'attributes.internal', ''),
                    'Product Barcodes': _.get(product, 'barcodes', ''),
                    'Descriptions': _.get(product, 'descriptions.main', ''),
                    'Hide From Menu': _.get(product, 'visible') === true ? 'NO' : 'YES',
                    'Image Id': JSON.stringify(_.get(product, 'images', '')),
                    'Relative Path': _.get(_.find(_.get(product, 'images'), 'url'), 'url', ''),
                };
            });

            const data = Papa.unparse({
                data: flattenProducts,
                fields: _.map(DEFAULT_EXPORT_COLUMNS),
            }, {
                header: enableHeader
            });

            enableHeader = false;
            response.write(data);
            response.write('\n');
        }
        response.status(StatusCodes.OK);
        response.end();

    }
    catch (error) {
        logger.error("Error searching on product api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /products/brands:
 *          get:
 *              summary: gets all brands
 *              tags:
 *                - /product
 *              responses:
 *                  202:
 *                      description: Brand Info
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: array
 *                                  items:
 *                                      type: object
 *                                      properties:
 *                                          ownerId:
 *                                              type: string
 *                                          brandName:
 *                                              type: string
 */
route.get('/brands',
async  (request: Request, response: Response) => {
    try {
        const result = await transaction( async () => {
            //Casting as string here, needs to be deprecated to return number
            return await connection.query(
                `SELECT DISTINCT ON ("ownerId") "ownerId" ::VARCHAR, "brandName" FROM products WHERE "ownerType" = 'brand'`,
                { model: Product }
            );
        });
        return response.json(result);
    }
    catch (error) {
        logger.error("Error getting brands on product api: ", error);

        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /products/distinct:
 *          get:
 *              summary: get list of products that match distinct values
 *              tags:
 *                  - /products
 *              parameters:
 *                - in: query
 *                  name: attributes
 *                  required: true
 *                  schema:
 *                      type: string
 *                  description: product attributes
 *                - in: query
 *                  name: catalogId
 *                  schema:
 *                      type: string
 *                  description: product catalogId
 *                - in: query
 *                  name: fields
 *                  schema:
 *                      type: string
 *                  description: product fields
 *                - in: query
 *                  name: productIds
 *                  schema:
 *                      type: string
 *                  description: list of product Id's
 *              responses:
 *                  200:
 *                      description: products that match the query
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: array
 *                                  items:
 *                                      $ref: '#/components/schemas/Product'
 */
route.get('/distinct',
async (request: Request, response: Response) => {
    const {
        attributes,
        catalogId,
        fields,
        productIds,
    } = request.query as {
        attributes : string,
        catalogId  : string,
        fields     : string,
        productIds : string,
    };

    try {
        if ( catalogId == null
                || ( fields == null && attributes == null) ) {
            throw new ErrorResponse(StatusCodes.BAD_REQUEST, `One or both of the required parameters catalogId and fields/attributes are missing!`);
        }

        const distinctCatalogProductValues = await getDistinctCatalogProductValues(
            attributes,
            catalogId,
            fields,
            productIds,
        );

        return response.json(distinctCatalogProductValues);
    }
    catch (error) {
        logger.error("Error searching on product api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *
 * /products/mergedProductIds:
 *   get:
 *     summary: Search for product ids
 *     parameters:
 *          - name: productIds
 *            in: query
 *            required: true
 *            schema:
 *              type: string
 *            description: a comma delimitered list of product Id's
 *     tags:
 *       - product
 *     responses:
 *          200:
 *              description: merged products that match product Id's
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: array
 *                          items:
 *                              $ref: '#/components/schemas/Product'
 */
/**
 * Get array of Products Ids: "productId" - "mergedTo" - "externalId". Filter by provided product id(s)
 */
route.get('/mergedProductIds',
checkPermissionsOnMultipleProducts('body', 'productIds', [[ReadCatalog]]),
async (request: Request, response: Response) => {
    const requestBody = request.body;
    if (requestBody == null || requestBody.productIds == null) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, "productIds are missing");
    }

    const productIdsArray = requestBody.productIds.map(Number);

    try {
        const productIdsInfo: Product[] = await Product.getProductIdsInfo(productIdsArray);
        return response.json(productIdsInfo);
    } catch (error) {
        logger.error("Error searching on product api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /products/{id}:
 *          get:
 *              summary: Gets Product Information by Id
 *              parameters:
 *                - name: id
 *                  in: path
 *                  required: true
 *                  schema:
 *                      type: number
 *                  description: id of product we want to update
 *                - name: catalogId
 *                  in: query
 *                  required: false
 *                  schema:
 *                      type: number
 *                  description: optional parameter to retrieve the presence a product has in a particular catalog with overrides and pricing applied
 *                - name: include
 *                  in: query
 *                  required: false
 *                  schema:
 *                      type: string
 *                      enum: [catalog]
 *                  description: related models to include
 *              responses:
 *                  200:
 *                      description: Product
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Product'
 */
route.get('/:id',
async (request: Request, response: Response) => {
    const {
        body,
        params: { id },
        query,
    } = request;

    const queryToUse = {
        ...query,
        ...body,
    };

    const {
        catalogId,
        include,
        orgId,
    }   = queryToUse as {
        catalogId?: number,
        include?: string,
        orgId?: string,
    }

    const excludeLinkedProduct = (queryToUse.searchType === SearchType.UnlinkedRetail || Boolean(queryToUse.excludeLinkedProduct));
    try {
        const product = await getProductWithOverrides(
            Number(id),
            {
                catalogId,
                include,
                orgId: Number(orgId),
                excludeLinkedProduct,
            }
        );

        response.status(StatusCodes.OK).json(product);
    }
    catch (error) {
        logger.error("Error getting product by id: ", error);
        return sendErrorResponse(response, error);
    }
});

export default route;
