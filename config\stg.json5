{
    auth: {
        hostname: 'ssm:/app/auth/env/prod/hostname',
        verify: {
            audience: 'ssm:/app/auth/env/prod/verify/audience',
            issuer: 'ssm:/app/auth/env/prod/verify/issuer',
            keySetUrl: 'ssm:/app/auth/env/prod/verify/keyseturl',
        }
    },
    db: {
        host:      'product-db-stg.treez.io',
        name:      'product-db-stg',
        namespace: 'product-db-stg',
        password:  'ssm:/apps/product/envs/stg/db/product-stg/password',
        username:  'product-stg',
    },
    logger: {
        name: 'product-api-stg',
        level: 'debug',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    secretToken: 'ssm:/apps/elastic/apm/stg/secret-token',
                    serverUrl:   'https://apm.stg.treez.io',
                    transactionIgnoreUrls: [ '/health-check-ping' ],
                    serviceName: 'product-api-stg',
                },
                enabled: true,
            },
        },
    },
}
