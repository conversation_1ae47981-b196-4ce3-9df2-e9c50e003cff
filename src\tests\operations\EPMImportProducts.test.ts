import { ErrorResponse } from "@treez/dev-pack/errors";
import Catalog from "../../models/Catalog";
import { uploadProducts } from "../../operations/EPMImportProducts";
import Product from "../../models/Product";
import { OwnerType, ProductType, UOM } from "@treez/commons/sharedTypings/product";
import CatalogProduct, { CatalogProductStatus } from "../../models/CatalogProduct";
import ExternalReference, { ExternalReferenceType } from "../../models/ExternalReference";


describe('Import New/Existing products to EPM and storeCatalog', () => {
    const userInfo = {
        sellTreezUserId: '<EMAIL>',
        userAuthId: '1234'
    };
    it("store catalog is not found", async () => {
        await Catalog.create({
            name: "Org Catalog",
            ownerType: 'organization',
            ownerId: 1
        });

        await expect(uploadProducts({
            catalogId: 0,
            productUploadList: []
        }, userInfo)).rejects.toThrow(ErrorResponse);

    });

    it("Org catalog is not found", async () => {
        const storeCatalog = await Catalog.create({
            name: "Store Catalog",
            ownerType: 'store',
            ownerId: 1
        });

        await expect(uploadProducts({
            catalogId: storeCatalog.id,
            productUploadList: []
        }, userInfo)).rejects.toThrow(ErrorResponse);

    });

    it("Should create product if not exists", async () => {
        const orgCatalog = await Catalog.create({
            name: "Org Catalog",
            ownerType: 'organization',
            ownerId: 1
        });
        const storeCatalog = await Catalog.create({
            name: "Store Catalog",
            ownerType: 'store',
            ownerId: 1,
            parentCatalogId: orgCatalog.id
        });

        await uploadProducts({
            catalogId: storeCatalog.id,
            productUploadList: [
                //@ts-ignore
                {
                    amount: 1,
                    brandName: 'API_AUTOMATION',
                    classification: 'SATIVA',
                    name: 'FORK444',
                    price: 20,
                    size: 'CONE',
                    status: CatalogProductStatus.ACTIVE,
                    type: ProductType.PILL,
                    uom: UOM.G,
                    visible: true,
                    cannabis: true,
                    attributes: {}
                }
            ]
        }, userInfo);

        const products = await Product.findAll();
        const externalReferences = await ExternalReference.findAll();
        expect(products).toHaveLength(1);
        expect(externalReferences).toHaveLength(1);
    });

    it("Should not create new product if already exist", async () => {

        const orgCatalog = await Catalog.create({
            name: "Org Catalog",
            ownerType: 'organization',
            ownerId: 1
        });
        const storeCatalog = await Catalog.create({
            name: "Store Catalog",
            ownerType: 'store',
            ownerId: 1,
            parentCatalogId: orgCatalog.id
        });

        const product = await Product.create({
            amount: null,
            brandName: "Brand Tester",
            eCommerceName: "Z0M&WHY?!",
            name: "ZOMGWHY?!",
            ownerId: 1,
            ownerType: OwnerType.ORG,
            msrp: "25.000000",
            size: "50 piece bucket",
            subtype: "Utter Nonsense",
            type: "Nonsense",
            uom: UOM.G,
        })

        await CatalogProduct.create({
            catalogId: storeCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.ACTIVE,
            price: 0.00,
        });

        await ExternalReference.create({
            type: ExternalReferenceType.SELL_TREEZ_ID,
            externalId: "61c4424a-5ccc-11eb-ae93-0242ac130002",
            productId: product.id
        })

        await uploadProducts({
            catalogId: storeCatalog.id,
            productUploadList: [
                //@ts-ignore
                {
                    amount: 1,
                    brandName: 'API_AUTOMATION',
                    classification: 'SATIVA',
                    name: 'FORK444',
                    price: 20,
                    size: 'CONE',
                    status: CatalogProductStatus.ACTIVE,
                    type: ProductType.PILL,
                    uom: UOM.G,
                    visible: true,
                    cannabis: true,
                    attributes: {},
                    uuid: "61c4424a-5ccc-11eb-ae93-0242ac130002"
                }
            ]
        }, userInfo);

        const catalogProducts = await CatalogProduct.findAll();
        expect(catalogProducts).toHaveLength(1);
    });
});