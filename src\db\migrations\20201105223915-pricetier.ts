import { QueryInterface }               from 'sequelize';
import {
    autoPK<PERSON>ield,
    createUpdate<PERSON>olumns,
    enumField,
    jsonField,
    notNull,
    stringField,
    booleanField,
    intField,
}                                           from '@treez/dev-pack/db/fieldTypes';
import { OwnerType }                        from '@treez/commons/sharedTypings/product';
import { 
    PriceTierMethod, 
    PriceTierThresholdType, 
    RangeMode,
}                                           from '@treez/commons/sharedTypings/priceTier';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.createTable('priceTiers', {
            id:                         notNull(autoPKField),
            label:                      notNull(stringField),
            isActive:                   notNull(booleanField),
            method:                     notNull(enumField(PriceTierMethod)),
            ownerId:                    notNull(intField),
            ownerType:                  notNull(enumField(OwnerType)),
            rangeMode:                  notNull(enumField(RangeMode)),
            thresholdType:              notNull(enumField(PriceTierThresholdType)),
            thresholds:                 notNull(jsonField),

            ...createUpdateColumns
        })
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.dropTable("priceTiers");
    }
}
