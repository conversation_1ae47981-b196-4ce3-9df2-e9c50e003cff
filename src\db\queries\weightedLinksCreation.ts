import { brandNameCorrelationScore } from "../../constants/products";

const weightedLinksCreationQuery = `
WITH brand_products AS (
    SELECT
        *,
    CASE
        WHEN "uom" IN ('g', 'oz', 'mg', 'lb', 'kg')
            THEN 'WEIGHT'
        WHEN "uom" IN ('l', 'ml', 'fl-oz', 'gal', 'pt', 'qt')
            THEN 'LIQUID'
        ELSE 'PACKAGED_GOOD'
    END AS "measure_type",
    CASE
        WHEN "uom" = 'each'
            THEN NULL
        WHEN "uom" = 'g'
            THEN "amount" * 1
        WHEN "uom" = 'oz'
            THEN "amount" * 28
        WHEN "uom" = 'mg'
            THEN "amount" / 1000
        WHEN "uom" = 'lb'
            THEN "amount" * 453.592
        WHEN "uom" = 'kg'
            THEN "amount" * 1000
        WHEN "uom" = 'l'
            THEN "amount" * 1
        WHEN "uom" = 'ml'
            THEN "amount" / 1000
        WHEN "uom" = 'fl-oz'
            THEN "amount" * .0295735
        WHEN "uom" = 'gal'
            THEN "amount" * 3.78541
        WHEN "uom" = 'pt'
            THEN "amount" * .473176249991928
        WHEN "uom" = 'qt'
            THEN "amount" * .94635249998385595482
        ELSE NULL
    END AS "measure",
    CASE
        WHEN "type" IN ('EDIBLE', 'PILL', 'TINCTURE', 'BEVERAGE')
            THEN 'EDIBLE'
        WHEN "type" IN ('MISC', 'MERCH')
            THEN 'MISC'
        ELSE "type"
    END AS "adjusted_type"
    FROM "products"
    WHERE "ownerType" = 'brand'
),

brand_suggested_links AS (
    SELECT
        "productToLinkId"
    FROM "suggestedLinks"
    WHERE "type" = 'BrandSuggested'
),

active_retail_product_ids AS (
    SELECT
        p.id
    FROM "catalogProducts" cp
    JOIN "products" p
        ON p.id = cp."productId"
    WHERE
        cp."status" = 'ACTIVE'
        AND p."ownerType" IN ('organization', 'store')
        AND p."linkedTo" IS NULL
        AND p."id" NOT IN (
            SELECT
                "productToLinkId"
            FROM brand_suggested_links
        )
    GROUP BY
        p.id
),

active_retail_products AS (
    SELECT
        CASE
            WHEN p."uom" IN ('g', 'oz', 'mg', 'lb', 'kg')
                THEN 'WEIGHT'
            WHEN p."uom" IN ('l', 'ml', 'fl-oz', 'g', 'pt', 'qt')
                THEN 'LIQUID'
            ELSE 'PACKAGED_GOOD'
        END AS "measure_type",
        CASE
            WHEN p."uom" = 'each'
                THEN NULL
            WHEN p."uom" = 'g'
                THEN "amount" * 1
            WHEN p."uom" = 'oz'
                THEN "amount" * 28
            WHEN p."uom" = 'mg'
                THEN "amount" / 1000
            WHEN p."uom" = 'lb'
                THEN "amount" * 453.592
            WHEN p."uom" = 'kg'
                THEN "amount" * 1000
            WHEN p."uom" = 'l'
                THEN "amount" * 1
            WHEN p."uom" = 'ml'
                THEN p."amount" / 1000
            WHEN p."uom" = 'fl-oz'
                THEN p."amount" * .0295735
            WHEN p."uom" = 'g'
                THEN p."amount" * 3.78541
            WHEN p."uom" = 'pt'
                THEN p."amount" * .473176249991928
            WHEN p."uom" = 'qt'
                THEN p."amount" * .94635249998385595482
            ELSE NULL
        END AS "measure",
        CASE
            WHEN p."type" IN ('EDIBLE', 'PILL', 'TINCTURE')
                THEN 'EDIBLE'
            ELSE p."type"
        END AS "adjusted_type",
        p.id,
        p.name,
        p."brandName",
        p.type,
        p.subtype,
        p.amount,
        p.uom
    FROM active_retail_product_ids a
    JOIN products p ON p.id = a.id
),

brand_matches AS (
    SELECT
        ap.id AS "retail_product_id",
        bp.id AS "brand_product_id",
        similarity(ap."brandName", bp."brandName") + similarity(ap."name", bp."name") AS compound_similarity,
        sl.id
    FROM brand_products bp
    LEFT JOIN active_retail_products ap
        ON
            bp.adjusted_type = ap.adjusted_type
        AND
            bp.measure_type = ap.measure_type
        AND 
            (ap.measure IS NULL
            OR
            bp.measure IS NOT NULL
                AND bp.measure BETWEEN ap.measure * .8 AND ap.measure * 1.1
            )
        AND SIMILARITY(bp."brandName", ap."brandName") > ${brandNameCorrelationScore}
        AND SIMILARITY(bp.name, ap.name) >= .4
    LEFT JOIN "suggestedLinks" sl
        ON sl."productToLinkId" = ap.id
        AND sl."brandProductId" = bp.id
        AND sl."type" = 'Rejected'
    WHERE
        ap."id" IS NOT NULL
    AND
        sl."id" IS NULL
)

INSERT INTO "suggestedLinks"(
    "productToLinkId",
    "brandProductId",
    "type",
    "rankWeight"
) (
    SELECT
        bm.retail_product_id,
        bm.brand_product_id,
        'Weighted',
        bm.compound_similarity
    FROM brand_matches bm
)
`

export default weightedLinksCreationQuery;