import {
    QueryInterface,
}                                       from 'sequelize';
import logger                           from '@treez/dev-pack/logger';

export = {

    up: async(_queryInterface: QueryInterface) => {
        logger.info(`Migration 20210513856903-foreignKeyAlignment.ts removed to prevent race problems`);
    },

    down: async(_queryInterface: QueryInterface) => {
        logger.info(`Migration 20210513856903-foreignKeyAlignment.ts removed to prevent race problems`);
    },

};
