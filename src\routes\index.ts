import { Router }                            from 'express';
import getMajorVersionPrefix                 from '@treez/dev-pack/lib/versionUtils';
import catalogs                              from './catalogs';
import catalogProducts                       from './catalogProducts';
import importRoutes                          from './import';
import operationRoutes                       from './operations';
import pricetier                             from './pricetiers';
import productRequirementsRoutes             from './productRequirements';
import products                              from './products';
import publicRoutes                          from './public';
import fileUpload                            from 'express-fileupload';

const routes = Router();

const majorVersionPrefix = getMajorVersionPrefix(process.env.npm_package_version);

routes.use(`/${majorVersionPrefix}`, fileUpload({
    limits: { fileSize: 2 * 1024 * 1024 * 1024 },
    useTempFiles: true,
    tempFileDir : '/tmp/'
}));
routes.use(`/${majorVersionPrefix}/catalog_products`, catalogProducts);
routes.use(`/${majorVersionPrefix}/catalogs`, catalogs);
routes.use(`/${majorVersionPrefix}/imports`, importRoutes);
routes.use(`/${majorVersionPrefix}/pricetier`, pricetier);
routes.use(`/${majorVersionPrefix}/productRequirements`, productRequirementsRoutes);
routes.use(`/${majorVersionPrefix}/products`, products);
routes.use(`/${majorVersionPrefix}/public`, publicRoutes);
routes.use(`/${majorVersionPrefix}/operations`, operationRoutes);

export default routes;

