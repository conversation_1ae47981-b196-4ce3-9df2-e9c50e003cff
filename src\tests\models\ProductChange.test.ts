import {
    OwnerType,
    ProductType,
    UOM,
}                                   from '@treez/commons/sharedTypings/product';
import _                            from 'lodash';
import { ProductAction }            from '../../lib/sharedInterfaces';
import Catalog                      from '../../models/Catalog';
import { addProductToCatalog }      from '../../models/CatalogProduct';
import { ExternalReferenceType }    from '../../models/ExternalReference';
import Product, {
    createProduct,
    linkProducts,
    mergeProductsToExistingProduct,
    unlinkProduct,
}                                   from '../../models/Product';
import ProductChange, {
    recordChange,
}                                   from '../../models/ProductChange';

const brandProduct = {
    amount:             1.00,
    attributes:         {
        flavor: ['pine', 'cedar', 'sandlewoood'],
        ingredient: ['alpha', 'chocolate'],
    },
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "ZOMGWHY?!",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    cannabis:           true,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               ProductType.FLOWER,
    uom:                UOM.EACH,
};

const orgProduct = {
    amount:             1.00,
    attributes:         {
        flavor: ['pine', 'cedar', 'sandlewoood'],
        ingredient: ['alpha', 'chocolate'],
    },
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "Some Store Product",
    ownerId:            200,
    ownerType:          OwnerType.ORG,
    cannabis:           true,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               ProductType.FLOWER,
    uom:                UOM.EACH,
};

const performMerge = async () => {
    const [
        productToMerge,
        productToMergeTo
    ] = await Promise.all(
        [
            createProduct(
                orgProduct,
                testUser,
            ),
            createProduct(
                orgProduct,
                testUser,
            ),
        ]
    );

    await productToMerge.addExternalReference(
        ExternalReferenceType.SELL_TREEZ_ID,
        "xyz1"
    );

    await productToMergeTo.addExternalReference(
        ExternalReferenceType.SELL_TREEZ_ID,
        "xyz2"
    );

    const orgCatalog = await Catalog.create({
        name     : 'Org Catalog',
        ownerId  : 200,
        ownerType: OwnerType.ORG,
    });

    await addProductToCatalog(orgCatalog, productToMerge.id, testUser);
    await addProductToCatalog(orgCatalog, productToMergeTo.id, testUser);

    await mergeProductsToExistingProduct(
        productToMergeTo.id,
        orgCatalog.id,
        [productToMerge.id],
        testUser,
    );

    return {
        productToMerge,
        productToMergeTo,
    }
}

const testUser = {
    userAuthId: '<EMAIL>',
};

describe('Product Change model', () => {

    it('logs product creation', async () => {
        const baseProduct = await Product.create(brandProduct);

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.CREATE,
            testUser,
            baseProduct,
            undefined,
        ) as ProductChange;

        expect(productChange.actionType).toBe(ProductAction.CREATE);
        expect(productChange.changes).toHaveLength(0);
        expect(productChange.newProduct).toBeDefined();
    });

    it('logs additions to subproperties (dot notation)', async () => {
        // GIVEN a product with no strain
        const baseProduct = await Product.create({
            ...brandProduct,
            details: undefined,
        });

        // WHEN a the strain subproperty change is recorded
        const baseProductData = baseProduct.get();
        const changedProduct: Product = new Product({
            ...baseProductData,
            details: {
                strain: 'blue velvet',
            },
        });

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            changedProduct,
            baseProduct,
        ) as ProductChange;

        // THEN a product change describing the property being set is generated
        expect(productChange.changes).toHaveLength(1);
        const change = productChange.changes?.[0];
        expect(change?.property).toBe('details.strain')
        expect(change?.newValue).toBe('blue velvet');
        expect(change?.oldValue).toBeFalsy();
        expect(change?.description).toBe(`Strain set to 'blue velvet'`);
    });

    it('logs changes made to array subproperties', async () => {
        const baseProduct = await Product.create({
            ...brandProduct,
            attributes: {
                effect: ['creativity', 'appetite'],
            }
        });

        const baseProductData = baseProduct.get();
        const changedProduct: Product = new Product({
            ...baseProductData,
            attributes: {
                ...baseProductData.attributes,
                effect: ['creativity', 'sleepiness'],
            },
        });

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            changedProduct,
            baseProduct,
        ) as ProductChange;

        expect(productChange.actionType).toBe(ProductAction.UPDATE);
        expect(productChange.changes).toHaveLength(1);

        const change = productChange.changes?.[0];
        expect(change?.oldValue).toEqual(['creativity', 'appetite']);
        expect(change?.newValue).toEqual(['creativity', 'sleepiness']);
        expect(change?.property).toBe('attributes.effect');
    });

    it('outputs human readable change description of numeric properties', async () => {
        const baseProduct = await Product.create(brandProduct);
        const changedProduct: Product = new Product({
            ...baseProduct.get(),
            amount: 2,
        });

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            changedProduct,
            baseProduct,
        ) as ProductChange;

        expect(productChange.changes).toHaveLength(1);
        expect(productChange.changes?.[0].description).toBe('Amount changed from 1 to 2');
    });

    it('outputs human readable change description of array properties', async () => {
        const baseProduct = await Product.create({
            ...brandProduct,
            attributes: {
                effect: ['creativity', 'appetite'],
            }
        });

        const baseProductData = baseProduct.get();
        const changedProduct: Product = new Product({
            ...baseProductData,
            attributes: {
                ...baseProductData.attributes,
                effect: ['creativity', 'relaxation'],
            },
        });

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            changedProduct,
            baseProduct,
        ) as ProductChange;

        expect(productChange.actionType).toBe(ProductAction.UPDATE);
        expect(productChange.changes).toHaveLength(1);

        const change = productChange.changes?.[0];
        expect(change?.property).toBe('attributes.effect');
        expect(change?.description).toBe(`Effects changed from 'creativity, appetite' to 'creativity, relaxation'`);
    });

    it('creates one description for each changed property in the same change operation', async () => {
        // GIVEN
        // a product with basic values
        const baseProduct = await Product.create({
            ...brandProduct,
            details: {
                strain: 'sativa',
            },
        });

        // WHEN
        // two properties are changed at the same time
        const baseProductData = baseProduct.get();
        const changedProduct: Product = new Product({
            ...baseProductData,
            eCommerceName: 'Plz buy',
            details: {
                strain: 'indica',
            },
        } as Partial<Product>);

        // THEN
        // one ProductChange is generated with two changesets
        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            changedProduct,
            baseProduct,
        ) as ProductChange;

        expect(productChange.changes).toHaveLength(2);
        const changes = productChange.changes as PropertyChange[];

        const strainChange = changes.find(c => c.property === 'details.strain');
        expect(strainChange).toBeDefined()
        expect(strainChange?.description).toBe(`Strain changed from 'sativa' to 'indica'`);

        const eCommerceNameChange = changes.find(c => c.property === 'eCommerceName');
        expect(eCommerceNameChange).toBeDefined();
        expect(eCommerceNameChange?.description).toBe(`Menu Title changed from 'Z0M&WHY?!' to 'Plz buy'`);
    });

    it(`skips logging update changes when products are equal`, async () => {
        // GIVEN an existing product
        const baseProduct = await Product.create(brandProduct);
        const previousProduct = baseProduct.toJSON();

        // WHEN an equal but not identical product is given for an update
        const falselyUpdatedProduct = await baseProduct.update({
            eCommerceName : baseProduct.eCommerceName,
        });

        const productChange = await recordChange(
            baseProduct.id,
            ProductAction.UPDATE,
            testUser,
            falselyUpdatedProduct,
            previousProduct,
        );

        // THEN no changes should be logged
        expect(productChange).toBeUndefined();
    });

    describe('Merged Products' , () => {
        it('records mergeIds when product is created from merge', async () => {

            const [
                productToMerge1,
                productToMerge2
            ] = await Promise.all(
                [
                    createProduct(
                        orgProduct,
                        testUser,
                    ),
                    createProduct(
                        orgProduct,
                        testUser,
                    ),
                ]
            );

            const mergedFromIds = [productToMerge1.id, productToMerge2.id];
            const productToMergeTo = await createProduct(
                orgProduct,
                testUser,
                mergedFromIds
            );

            const productChange = await ProductChange.findOne({
                where: {
                    actionType: ProductAction.CREATE,
                    productId: productToMergeTo.id
                }
            });

            expect(productChange).not.toBeNull();
            expect(productChange?.mergedFrom).not.toBeNull();
            expect(productChange?.mergedFrom).toMatchObject(mergedFromIds);
        });

        it('records mergedFrom on an existing product that was merged To', async () => {
            const {
                productToMerge,
                productToMergeTo,
            } = await performMerge();

            const productChange = await ProductChange.findOne({
                where: {
                    actionType: ProductAction.MERGE,
                    productId: productToMergeTo.id,
                }
            });

            expect(productChange).not.toBeNull();
            expect(productChange?.mergedFrom).not.toBeNull();
            expect(productChange?.mergedFrom).toMatchObject([productToMerge.id]);
        });
    });

    describe("Linked & UnLinked Products", () => {
        it('records a ProductChange of LINK when a product is linked', async () => {
            const [
                productToLink,
                brandProductToLinkTo,
            ] = await Promise.all(
                [
                    createProduct(
                        orgProduct,
                        testUser,
                    ),
                    createProduct(
                        brandProduct,
                        testUser,
                    ),
                ]
            );


            await linkProducts(productToLink.id, brandProductToLinkTo.id, testUser);

            const productChanges = await ProductChange.findAll({
                where: {
                    productId: productToLink.id
                }
            });

            const updateChange = productChanges.find(change => change.actionType === ProductAction.UPDATE);
            const linkedChange = productChanges.find(change => change.actionType === ProductAction.LINK);

            expect(updateChange).toBeUndefined();
            expect(linkedChange).not.toBeUndefined();

            expect(linkedChange?.newProduct).not.toBeUndefined();
            expect(linkedChange?.newProduct?.linkedTo).not.toBeNull();

            expect(linkedChange?.newProduct?.linkedTo).toBe(brandProductToLinkTo.id);
        });

        it(`records a ProductChange of UNLINK when a product is unlinked`, async () => {
            const [
                productToLink,
                brandProductToLinkTo,
            ] = await Promise.all(
                [
                    createProduct(
                        orgProduct,
                        testUser,
                    ),
                    createProduct(
                        brandProduct,
                        testUser,
                    ),
                ]
            );


            await linkProducts(productToLink.id, brandProductToLinkTo.id, testUser);
            await unlinkProduct(productToLink.id, testUser);

            const productChanges = await ProductChange.findAll({
                where: {
                    productId: productToLink.id
                }
            });

            const updateChange = productChanges.find(change => change.actionType === ProductAction.UPDATE);
            const unlinkedChange = productChanges.find(change => change.actionType === ProductAction.UNLINK);

            expect(updateChange).toBeUndefined();
            expect(unlinkedChange).not.toBeUndefined();

            expect(unlinkedChange?.newProduct).not.toBeUndefined();
            expect(unlinkedChange?.newProduct?.linkedTo).toBeNull();

            expect(unlinkedChange?.oldProduct).not.toBeUndefined();
            expect(unlinkedChange?.oldProduct?.linkedTo).toBe(brandProductToLinkTo.id);
        });
    })
});
