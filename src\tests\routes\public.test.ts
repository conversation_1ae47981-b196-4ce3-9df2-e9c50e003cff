import { StatusCodes }                      from 'http-status-codes';
import _                                    from 'lodash';
import supertest                            from 'supertest';
import {
    OwnerType,
    ProductType,
    UOM,
}                                           from '@treez/commons/sharedTypings/product';
import Product                              from '../../models/Product';
import app                                  from './testApp';

describe('/public endpoints', () => {
    beforeEach(async () => {
        await Product.bulkCreate([
            {
                amount:             "6",
                brandName:          "Bell's",
                eCommerceName:      "Bell's Two Hearted Ipa",
                name:               "Two Hearted",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                sku:                "SKUXOZEHXKS1",
                subtype:            "Beer",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            },
            {
                amount:             "1",
                brandName:          "Eden",
                eCommerceName:      "Eden Diamond Line",
                name:               "Diamond Vape",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                sku:                "SKUXOZEHXKS1",
                subtype:            "510 Thread",
                type:               ProductType.CARTRIDGE,
                uom:                UOM.EACH,
            },
            {
                amount:             "6",
                brandName:          "Green Revolution",
                eCommerceName:      "Green Revolution's Perfect J",
                name:               "Perfect J",
                ownerId:            3,
                ownerType:          OwnerType.BRAND,
                sku:                "SKUXOZEHXKS1",
                type:               ProductType.PREROLL,
                uom:                UOM.EACH,
            },
        ]);
    })

    it('gets all the brands in productAPI', async () => {
        const response = await supertest(app)
            .get('/public/brands');

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toHaveLength(3);
    });

    it('gets brand products', async () => {
        const response = await supertest(app)
            .get('/public/products');

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(3);
    });

    it('only returns brand products', async () => {
        await Product.create({
            amount:             "6",
            brandName:          "Green Revolution",
            eCommerceName:      "Green Revolution's Perfect J",
            name:               "Perfect J",
            ownerId:            3,
            ownerType:          OwnerType.ORG,
            sku:                "SKUXOZEHXKS1",
            type:               ProductType.PREROLL,
            uom:                UOM.EACH,
        });

        const response = await supertest(app)
            .get('/public/products');

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(3);
    });

    it('it allows to get products using query parameters', async () => {
        const response = await supertest(app)
            .get('/public/products')
            .query({
                query: JSON.stringify({
                    filters: [
                        {
                            values: [ProductType.BEVERAGE],
                            field: 'type',
                            fuzzy: false,
                        }
                    ]
                })
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0]).toMatchObject({
            amount:             "6.000000",
            brandName:          "Bell's",
            eCommerceName:      "Bell's Two Hearted Ipa",
            name:               "Two Hearted",
            ownerId:            1,
            ownerType:          OwnerType.BRAND,
            sku:                "SKUXOZEHXKS1",
            subtype:            "Beer",
            type:               ProductType.BEVERAGE,
            uom:                UOM.EACH,
        });
    });

    it(`Returns an error if it requests catalog level information for a product`, async () => {
        const response = await supertest(app)
            .get('/public/products')
            .query({
                query: JSON.stringify({
                    catalogId: 1
                })
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });
});
