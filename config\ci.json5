{
    db: {
        name:      'product-db-ci',
        namespace: 'product-db-ci',
        password:  'ssm:/apps/product/envs/ci/db/product-ci/password',
        replication: {
            read: [
                {
                    host: 'product-db-ci-ro.treez.io',
                },
            ],
            write: {
                host: 'product-db-ci.treez.io',
            },
        },
        username:  'product-ci',
    },
    logger: {
        level: 'debug',
        name:  'product-api-ci',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serviceName: 'product-api-ci',
                    logLevel: 'trace'
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        topic: {
            productUpdate: 'persistent://product-api-ci/product/productUpdate'
        }
    }
}
