import config                           from 'config';
import { unflatten }                    from 'flat';
import _                                from 'lodash';
import { connection }                   from '@treez/dev-pack/db';
import { ProductField }                 from '@treez/commons/sharedTypings/product';
import {
    applyOverrides,
    parseAttribute,
    parseDetail,
    SearchCriteria,
    SearchType,
    validateAndFormatCriteria,
}                                       from './searchUtils';
import generateRetailSearchQuery        from '../db/queries/searchRetailProducts';
import generateUnlinkedSearch           from '../db/queries/searchRetailProductsUnlinked';
import generateBrandProductsQuery       from '../db/queries/searchBrandProducts';
import Product, {
    searchForBrands,
}                                       from '../models/Product';
import { cleanTextForSQL }              from '../lib/db';

const ID                = "id";
const MAX_PAGE_SIZE     = _.get(config, 'search.maxPageSize', 500);
const UUID              = "uuid";
const PRICE_TIER_ID     = "priceTierId";
const SOURCE            = "source";
const POS               = "POS";
const UUID_WITH_BARCODE = "uuidWithBarcode"

const BOOLEAN_FIELDS = [
    ProductField.VISIBLE.toString(),
];

const NUMERIC_FIELDS = [
    ProductField.AMOUNT.toString(),
];

export interface BrandResult {
    brandName: string,
    id       : string,
}
export interface SearchResponse  {
    products: object[],
    total   : number,
    brands? : BrandResult[],
}

export const runSearchQuery = async (criteria: SearchCriteria): Promise<SearchResponse> => {
    const searcher = new SearchRunner(criteria);
    const productResults = await searcher.runSearch() as SearchResponse;

    if (criteria.searchBrands !== true) {
        return productResults
    } else {
        if (criteria.searchString) {
            const brands = await searchForBrands(criteria.searchString) as BrandResult[];

            return {
                ...productResults,
                brands,
            }
        }
        else {
            const result =  {
                ...productResults,
                brands: []
            }

            return result;
        }
    }
}

export class SearchRunner {
    replacements: _.Dictionary<string> = {};
    search: SearchCriteria;
    searchType: SearchType = SearchType.Retail;
    baseQuery = ``;
    dateRange = ``;
    filtering = ``;
    ordering = ``;
    paging = ``;
    searchQuery = ``;

    constructor(criteria: SearchCriteria) {
        this.search = validateAndFormatCriteria(criteria);

        if(this.search.catalogId == null) {
            this.searchType = SearchType.BrandTreez;
        }
        else if (this.search.excludeLinkedProduct) {
            this.searchType = SearchType.UnlinkedRetail;
        }
        else if (this.search.searchType == null) {
            this.searchType = SearchType.Retail;
        }
        else if (this.search.searchType) {
            this.searchType = this.search.searchType as SearchType;
        }
    }

    runSearch = async (): Promise<any> => {
        this.buildBaseQuery();

        this.buildDateRange();

        this.buildFilters();

        this.buildOrdering();

        this.buildPaging();

        this.buildSearchQuery();

        const queryString = `
        ${this.baseQuery}
        ${this.searchQuery}
        ${this.dateRange}
        ${this.filtering}
        ${this.ordering}
        ${this.paging}`;

        const products = await connection.query(queryString,
            {
                model: Product,
                replacements: this.replacements,
            },
        );

        let totalCount = 0;

        if (products.length) {
            totalCount = parseInt(_.get(
                _.get(products[0], 'dataValues'),
                'totalCount',
            ));
        }

        // If this is a catalog search, apply overrides
        let finalProducts = products;

        if (this.search.catalogId) {
            finalProducts = products.map((product: Product) => {
                return unflatten(applyOverrides(product), { overwrite: true })
            });
        }

        return {
            products:   finalProducts,
            total:      totalCount,
        }
    }

    /**
     * Below we have all the component functions that return various query components
     */
    buildBaseQuery = () => {
        const search = this.search;
        const uuidFilter = this.getFilterByName(search, UUID);

        const uuidFilterIsEmpty: boolean = _.isEmpty(uuidFilter);
        //If it is a general search where the sellTreezUUID is not explicitly requested, bring back the product once with the latest external reference.
        //Otherwise bring back a product record for each id explicitly requested.
        let uuidCondition : string;
        let uuidFilterValues: string [] = [];

        if (uuidFilterIsEmpty) {
            uuidCondition = `ORDER BY "createdAt" DESC LIMIT 1`;
        } else {
            const operator : string = uuidFilter[0].not ? 'NOT IN' : 'IN';

            uuidFilterValues = uuidFilter[0].values;
            uuidCondition = `AND "externalId" ${operator} (:uuidFilterValues)`
        }

        let statusFilter = `AND cp.status = 'ACTIVE' `;

        if (search.status && search.status.length > 0 ) {
            statusFilter = `AND cp.status in (:status) `;
        }

        if (this.search.catalogId != null) {
            switch(this.searchType) {
                case SearchType.Retail:
                    this.baseQuery = generateRetailSearchQuery(uuidCondition, statusFilter);
                    break;
                case SearchType.UnlinkedRetail:
                    this.baseQuery = generateUnlinkedSearch(uuidCondition, statusFilter);
                    break;
                case SearchType.BrandTreez:
                    this.baseQuery = generateBrandProductsQuery(this.search);
                    break;
                default:
                    this.baseQuery = generateRetailSearchQuery(uuidCondition, statusFilter, search.brandMatch);
              }

            Object.assign(this.replacements, {
                catalogId: search.catalogId,
                uuidFilterValues: uuidFilterValues,
                status: this.search.status
            });
        } else {
            this.baseQuery = generateBrandProductsQuery(this.search);
        }
    }

    getFilterByName = (search: SearchCriteria, filterName: String) => {
        return _.filter(search.filters, (filter) => {
            return filter.field === filterName
        });
    }

    buildDateRange = () => {
        const dateRange = this.search.dateRange;

        if (dateRange == null) {
            return;
        }
        this.dateRange = `AND ${this.search.catalogId ? '(' : ''} (
            extract(epoch from p."${cleanTextForSQL(dateRange.field)}") > :epochStart AND
            extract(epoch from p."${cleanTextForSQL(dateRange.field)}") < :epochEnd)`

        if(this.search.catalogId) {
            this.dateRange += ` OR (
                extract(epoch from cp."${cleanTextForSQL(dateRange.field)}") > :epochStart AND
                extract(epoch from cp."${cleanTextForSQL(dateRange.field)}") < :epochEnd))`
        }

        Object.assign(this.replacements, {
            epochStart:     dateRange.start,
            epochEnd:       dateRange.end,
        })
    }

    buildFilters = () => {
        const search = this.search;

        if (search.filters) {
            let filterArray: string[] = [];

            search.filters.forEach((searchFilter, index) => {
                //only needed for buildSearch()
                if (searchFilter.field === SOURCE || searchFilter.field === UUID_WITH_BARCODE) {
                    return;
                }

                let attributeFilter = parseAttribute(searchFilter);
                let detailFilter = parseDetail(searchFilter);

                const filterKey = `filterKey${index}`;

                let filterValue;

                if (searchFilter.fuzzy) {
                    if(attributeFilter || detailFilter) {
                        throw new Error("Attribute/detail filter can only be applied to exact searches")
                    }

                    if(searchFilter.not) {
                        throw new Error("Not filter cannot be applied with fuzzy filter.");
                    }

                    filterValue = `(${searchFilter.values.join('|')})%` as string;

                    if (search.catalogId == null) {
                        filterArray.push(`LOWER(p."${searchFilter.field}") SIMILAR TO LOWER(:${filterKey})`);
                    }
                    else {
                        filterArray.push(`LOWER(p."${searchFilter.field}") SIMILAR TO LOWER(:${filterKey})
                        OR LOWER(bt."${searchFilter.field}") SIMILAR TO LOWER(:${filterKey})`);
                    }
                }
                else {
                    filterValue = searchFilter.values as string[];

                    if (searchFilter.field === ID) {
                        if (search.catalogId == null) {
                            const operator = searchFilter.not ? 'NOT IN' : 'IN';
                            filterArray.push(`(p.${searchFilter.field} ${operator} (:${filterKey}))`);
                        } else {
                            if (searchFilter.not) {
                                /*
                                    'OR bt."id" IS NULL' is needed because nulls are not returned in NOT IN
                                    ex bt.id NOT IN ('100') will not return values where bt.id IS NULL
                                */
                                filterArray.push(
                                    `(p.${searchFilter.field} NOT IN (:${filterKey})
                                    AND (bt.${searchFilter.field} NOT IN (:${filterKey})
                                    OR bt."id" IS NULL))`
                                );
                            } else {
                                filterArray.push(
                                    `(p.${searchFilter.field} IN (:${filterKey})
                                    OR bt.${searchFilter.field} IN (:${filterKey}))`
                                );
                            }
                        }
                    } else if (searchFilter.field === PRICE_TIER_ID && search.catalogId) {
                        if (searchFilter.not) {
                            filterArray.push(`(cp."priceTierId" NOT IN (:${filterKey}) OR cp."priceTierId" IS NULL)`)
                        } else {
                            filterArray.push(`cp."priceTierId" IN (:${filterKey})`)
                        }
                    } else if (searchFilter.field === 'linkedTo') {
                        const operator = searchFilter.not ? 'NOT IN' : 'IN';
                        filterArray.push(`p."linkedTo" ${operator} (:${filterKey})`)
                    } else if(attributeFilter) {
                        const attributeKey = `attributeKey${index}`;
                        this.replacements[attributeKey] = attributeFilter;

                        if (search.catalogId == null) {
                            const operator = searchFilter.not ? 'NOT' : '';
                            filterArray.push(`${operator} (p.attributes-> :${attributeKey} ?| array[:${filterKey}])`);
                        } else {
                            if ( searchFilter.not ) {
                                /*
                                    'OR bt."id" IS NULL' is needed because nulls are not returned in NOT IN
                                    ex bt.id NOT IN ('100') will not return values where bt.id IS NULL
                                */
                                filterArray.push(
                                    `(NOT p.attributes-> :${attributeKey} ?| array[:${filterKey}] AND (NOT bt.attributes -> :${attributeKey} ?| array[:${filterKey}] OR bt.id IS NULL))`
                                );
                            } else {
                                filterArray.push(
                                    `(p.attributes-> :${attributeKey} ?| array[:${filterKey}] OR bt.attributes-> :${attributeKey} ?| array[:${filterKey}])`
                                );
                            }
                        }

                    } else if(detailFilter) {
                        const detailKey = `detailKey${index}`;
                        this.replacements[detailKey] = detailFilter;

                        if (search.catalogId == null) {
                            const operator = searchFilter.not ? 'NOT' : '';
                            filterArray.push(`${operator} (p.details-> :${detailKey} ?| array[:${filterKey}])`);
                        } else {
                            if ( searchFilter.not ) {
                                /*
                                    'OR bt."id" IS NULL' is needed because nulls are not returned in NOT IN
                                    ex bt.id NOT IN ('100') will not return values where bt.id IS NULL
                                */
                                filterArray.push(
                                    `(NOT p.details-> :${detailKey} ?| array[:${filterKey}]
                                        AND (NOT bt.details-> :${detailKey} ?| array[:${filterKey}] OR bt.id IS NULL))`
                                )
                            } else {
                                filterArray.push(
                                    `(p.details-> :${detailKey} ?| array[:${filterKey}]
                                        OR bt.details-> :${detailKey} ?| array[:${filterKey}])`
                                );
                            }
                        }
                    } else if (searchFilter.field === 'ownerId') {
                        const operator = searchFilter.not ? 'NOT' : '';
                        filterArray.push(
                            `(p."${searchFilter.field}" ${operator} IN (:${filterKey}))`
                        );
                    } else {
                        if (BOOLEAN_FIELDS.includes(searchFilter.field)) {
                            if (search.catalogId == null) {
                                const operator = searchFilter.not ? 'NOT IN' : 'IN';
                                filterArray.push(
                                    `p."${searchFilter.field}" ${operator} (:${filterKey})`
                                );
                            } else {
                                if ( searchFilter.not ) {
                                    /*
                                        'OR bt."id" IS NULL' is needed because nulls are not returned in NOT IN
                                        ex bt.id NOT IN ('100') will not return values where bt.id IS NULL
                                    */
                                    filterArray.push(
                                        `(p."${searchFilter.field}" NOT IN (:${filterKey})
                                        AND (bt."${searchFilter.field}" NOT IN (:${filterKey}) OR bt."id" IS NULL))`
                                    );
                                }
                                else {
                                    filterArray.push(
                                        `(p."${searchFilter.field}" IN (:${filterKey})
                                        OR bt."${searchFilter.field}" IN (:${filterKey}))`
                                    );
                                }
                            }
                        } else if (NUMERIC_FIELDS.includes(searchFilter.field)) {
                            const operator = searchFilter.not ? 'NOT' : '';
                            filterValue = filterValue.map(element => Number(element));
                            if (search.catalogId == null) {
                                filterArray.push(`${operator} p."${searchFilter.field}" = ANY (array[:${filterKey}])`);
                            } else {
                                filterArray.push(
                                    `(${operator} p."${searchFilter.field}" = ANY (array[:${filterKey}])
                                            OR ${operator} bt."${searchFilter.field}" = ANY (array[:${filterKey}]))`
                                );
                            }
                        } else {
                            if (search.catalogId == null) {
                                const operator = searchFilter.not ? 'NOT ILIKE' : 'ILIKE';
                                filterArray.push(`p."${searchFilter.field}" ${operator} ANY (array[:${filterKey}])`);
                            }
                            else {
                                if (searchFilter.field !== UUID) {
                                    if ( searchFilter.not ) {
                                        /*
                                            'OR bt."id" IS NULL' is needed because nulls are not returned in NOT IN
                                            ex bt.id NOT IN ('100') will not return values where bt.id IS NULL
                                        */
                                        filterArray.push(
                                            `(p."${searchFilter.field}" NOT ILIKE ANY (array[:${filterKey}])
                                            AND (bt."${searchFilter.field}" NOT ILIKE ANY (array[:${filterKey}]) OR bt."id" IS NULL))`
                                        );
                                    }
                                    else {
                                        filterArray.push(
                                            `(p."${searchFilter.field}" ILIKE ANY (array[:${filterKey}])
                                            OR bt."${searchFilter.field}" ILIKE ANY (array[:${filterKey}]))`
                                        );
                                    }
                                }
                            }
                        }
                    }
                }
                Object.assign(this.replacements, {
                    [filterKey]:    filterValue,
                })
            });

            this.filtering = `${filterArray.length ? ' AND ' : ''}${filterArray.join(' AND ')}`;
        }
    }

    buildOrdering = () => {
        const search = this.search;
        let orderBy : string[] = [];
        const DEFAULT_SECONDARY_SORT = {field: ID, order: 'asc'}

        if (search.sortBy) {
            let sortBy = Array.isArray(search.sortBy) ? search.sortBy : [search.sortBy, DEFAULT_SECONDARY_SORT];
            orderBy =  Array.from(sortBy, sortEntry => ` "${sortEntry.field}" ${sortEntry.order}`);
        }

        //if there is a search string, sort by relevance
        if (search.searchString && search.orderByRank) {
            orderBy.push(`ts_rank(:weights, p."searchVector", plainto_tsquery('english', :searchRankString)) DESC`);
            Object.assign(this.replacements, {
                searchRankString:       search.searchString,
                weights: search.weights || '{0.4, 0.5, 0.7, 1.0}'
            })
        }

        this.ordering = !orderBy.length ? '' : ` ORDER BY ${orderBy.join(',')}`;
    }

    buildPaging = () => {
        if(
            this.search.offset
            && this.search.page
        ) {
            throw new Error("Please specify one of 'offset' and 'page'");
        }

        const {
            page = 1,
            pageSize = 50,
        } = this.search;

        const actualPageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        const offset = this.search.offset || (page - 1) * actualPageSize;

        Object.assign(this.replacements, {
            offset,
            pageSize: actualPageSize,
        });

        this.paging = ` LIMIT :pageSize OFFSET :offset`;
    }

    buildSearchQuery = () => {
        const search = this.search;
        const uuidWithBarcodeFilter = this.getFilterByName(search, UUID_WITH_BARCODE);

        if (search.searchString) {
            const stringArray = `${_.replace(search.searchString, "'", `\\'`).trim()}:*`.match(/\S+/g);

            Object.assign(this.replacements, {
                searchString:       stringArray!.join(' & '),
                likeString:         `%${search.searchString.trim()}%`,
            });

           let baseSearchQuery = `
                p."searchVector" @@ to_tsquery('english', :searchString)
                OR p.name ILIKE :likeString
                OR p."brandName" ILIKE :likeString
                OR p.type ILIKE :likeString
                OR p.subtype ILIKE :likeString
           `;

            const source = this.getFilterByName(search, SOURCE);
            if (!_.isEmpty(uuidWithBarcodeFilter) && (_.isEmpty(source) || !source[0].values.includes(POS))) {
                baseSearchQuery = baseSearchQuery.concat(' OR extz."externalId" IN (:uuidWithBarcodeFilterValues)');
                Object.assign(this.replacements, {
                    uuidWithBarcodeFilterValues: uuidWithBarcodeFilter[0].values
                });
            }

           if (this.searchType !== SearchType.Retail) {
               this.searchQuery = `AND (${baseSearchQuery})`;

           } else {
            /*
                Using p."searchVector" in p."linkedTo" is NOT NULL block because search vectors accounts for
                brand and catalog overrides already.
            */
                this.searchQuery = ` AND
                (
                    (
                        (
                           ${baseSearchQuery}
                        )
                        AND p."linkedTo" is NULL
                    )
                OR (
                        (
                            p."searchVector" @@ to_tsquery('english', :searchString)
                            OR bt.name ILIKE :likeString
                            OR bt."brandName" ILIKE :likeString
                            OR bt.type ILIKE :likeString
                            OR bt.subtype ILIKE :likeString
                        )
                        AND p."linkedTo" is NOT NULL
                    )
                )`;
            }
        }
    }
}

export default runSearchQuery;
