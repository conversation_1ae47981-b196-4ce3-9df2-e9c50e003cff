import Ajv                                      from 'ajv';
import Big                                      from 'big.js';
import { StatusCodes }                          from 'http-status-codes';
import _, {
    Dictionary,
}                                               from 'lodash';
import {
    AfterCreate,
    AfterUpdate,
    AutoIncrement,
    BeforeCreate,
    BeforeUpdate,
    BelongsTo,
    BelongsToMany,
    Column,
    Default,
    ForeignKey,
    HasMany,
    Is,
    PrimaryKey,
    Table,
}                                               from 'sequelize-typescript';
import {
    FindOptions,
    IncludeOptions,
}                                               from 'sequelize/types';
import { v4 as uuidv4 }                         from 'uuid';
import { ImageAttributes }                      from '@treez/commons/sharedTypings';
import {
    ProductType,
    UOM,
    ProductData,
    OwnerType,
}                                               from '@treez/commons/sharedTypings/product';
import {
    booleanField,
    decimalField,
    enumField,
    intField,
    jsonField,
    notNull,
    preciseMonetaryField,
    stringField,
}                                               from '@treez/dev-pack/db/fieldTypes';
import { TreezModel }                           from '@treez/dev-pack/models/TreezBaseModel';
import {
    connection,
    transaction,
}                                               from '@treez/dev-pack/db';
import { ErrorResponse }                        from '@treez/dev-pack/errors';
import { isNilOrEmptyObject }                   from '../lib/commonFunctions';
import { cleanTextForSQL }                      from '../lib/db';
import {
    getProductFieldPaths,
    parseNumericFields,
}                                               from '../lib/product';
import { ProductAction }                        from '../lib/sharedInterfaces';
import { TypeOfProductChanges }                 from '../pulsar/producer';
import Catalog, {
    defaultCatalogAttributes,
    findCatalogById,
}                                               from './Catalog';
import CatalogProduct, {
    addProductToCatalog,
    addProductToEpm,
    CatalogProductRecord,
    CatalogProductStatus,
    findCatalogProduct,
    removeProductFromCatalog,
    updateCatalogProduct,
}                                               from './CatalogProduct';
import ExternalReference, {
    ExternalReferenceType,
}                                               from './ExternalReference';
import { recordChange }                         from './ProductChange';
import ProductRequirements, {
    validateProductAgainstRequirements
}                                               from './ProductRequirements';
import Assortment                               from './Assortment';
import AssortmentProduct                        from './AssortmentProduct';
import ProductUpdateOutbox                      from './ProductUpdateOutbox';
import { MatchType }                            from './SuggestedLink';

const {
    UPDATE,
    MERGE,
} = TypeOfProductChanges;

export interface ProductAdoption {
    productId               : number;
    linkedRetailCount       : number;
    brandSuggestedLinksCount: number;
    totalCount              : number;
    weightedMatches         : number;
    linkAdoption            : number;
    brandSuggestedAdoption  : number;
}

/**
 * @swagger
 * components:
 *  schemas:
 *      ProductDetails:
 *          properties:
 *              cbdMg:
 *                  title: "CBD (mg)"
 *                  type: number
 *              doseCbdMg:
 *                  title: "CBD per dose"
 *                  type: number
 *              doses:
 *                  title: "Doses"
 *                  type: number
 *              doseThcMg:
 *                  title: "THC per dose"
 *                  type: number
 *              extractionMethod:
 *                  title: "Extraction Method"
 *                  type: string
 *              strain:
 *                  title: "Strain"
 *                  type: string
 *              thcMg:
 *                  title: "THC (mg)"
 *                  type: number
 *              totalFlowerWeight:
 *                  title: "Total Flower Weight"
 *                  type: number
 *              totalConcentrateWeight:
 *                  title: "Total Concentrate Weight (G)"
 *                  type: number
 *
 *
 */
export interface ProductDetails {
    cbdMg?            : number,
    doseCbdMg?        : number,
    doses?            : number,
    doseThcMg?        : number,
    extractionMethod? : string,
    strain?           : string,
    thcMg?            : number,
    totalFlowerWeight?: number,
    totalConcentrateWeight?: number,
};

/**
 * @swagger
 * components:
 *  schemas:
 *      ProductAttributes:
 *          properties:
 *              effect:
 *                  title: "Effects"
 *                  type: array
 *                  items:
 *                      type: string
 *              flavor:
 *                  title: "Flavors"
 *                  type: array
 *                  items:
 *                      type: string
 *              general:
 *                  title: "General"
 *                  type: array
 *                  items:
 *                      type: string
 *              ingredient:
 *                  title: "Ingredients"
 *                  type: array
 *                  items:
 *                      type: string
 *              internal:
 *                  title: "Tags"
 *                  type: array
 *                  items:
 *                      type: string
 *
 */
export interface ProductAttributes {
    effect?     :string[],
    flavor?     :string[],
    general?    :string[],
    ingredient? :string[],
    internal?   :string[],
}

export interface ProductRecord {
    id               : number,
    amount?          : number,
    attributes?      : ProductAttributes,
    barcodes?        : Dictionary<string>[],
    brandName?       : string,
    cannabis?        : boolean,
    classification?  : string,
    descriptions?    : object,
    details?         : ProductDetails,
    eCommerceName?   : string,
    externalId?      : string,
    images?          : ImageAttributes,
    linkedTo?        : number,
    msrp?            : number,
    name             : string,
    ownerId          : number,
    ownerType        : OwnerType,
    productOverrides?: Partial<ProductRecord>,
    packageTracked?  : boolean,
    productShortCode?: string,
    size?            : string,
    sku?             : string,
    searchVector?    : string,
    subtype?         : string,
    tpc?             : string,
    type             : ProductType,
    uom              : UOM,
    upc?             : string,
    visible          : boolean,
}

export enum includableModels {
    CATALOGS = 'catalogs',
    ASSORTMENTS = 'assortments',
    CAPABILITIES = 'capabilities',
}

export const buildIncludes = (include: string[], orgId?: number) => {
    const includeOptions: IncludeOptions[] = [];
    for ( const includedModel of include ) {
        if ( includedModel == includableModels.CATALOGS ) {
            const includeCatalogs: IncludeOptions = {
                attributes: defaultCatalogAttributes,
                model: Catalog,
            };
            if ( orgId ) {
                includeCatalogs.include = [{
                    attributes: ['id', 'ownerId'],
                    as: 'parentCatalog',
                    model: Catalog,
                    where: {
                        ownerId: orgId,
                    },
                }]
            }
            includeOptions.push(includeCatalogs);
        }

        if( includedModel == includableModels.ASSORTMENTS ) {
            const includeAssortments: IncludeOptions = {
                attributes: Assortment.defaultAttributes,
                model: Assortment,
            };

            includeOptions.push(includeAssortments);
        }

        if (!includeOptions.length) {
            throw new ErrorResponse(StatusCodes.BAD_REQUEST, `Requested model for include ${includedModel} is not supported or is invalid!`);
        }
    }
    return includeOptions;
}

export interface ProductMatch extends ProductRecord {
    rankWeight: number;
}

const validProductDetails = (details: ProductDetails) => {
    if (details == null) {
        return true;
    }

    const productDetailsValidation = new Ajv({
        coerceTypes: true
    });
    const detailsValidity = productDetailsValidation.validate(
        Product.productDetailsSchema,
        details,
    );

    if (detailsValidity === false) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product details were an incorrect format ${productDetailsValidation.errorsText()}`
        );
    };

    return true;
}

const validProductAttributes = (attributes: ProductAttributes) => {
    if (attributes == null) {
        return true;
    }

    const productAttributesValidation = new Ajv({
        coerceTypes: true
    });
    const attributesValidity = productAttributesValidation.validate(
        Product.attributesSchema,
        attributes,
    );

    if (attributesValidity === false) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product attributes were an incorrect format ${productAttributesValidation.errorsText()}`
        );
    };

    return true;
}

const validUOM = (uom: string): true => {
    if (_.includes(Object.values(UOM), uom) === false) {
        throw new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            `Invalid enum value "${uom}" for uom!`
        );
    }
    else {
        return true;
    }
}

export interface ProductInformation extends ProductRecord, CatalogProductRecord {
    uuid?: string,
    weedMapsProductVariantId?: string,
}

export interface ProductInCatalog extends ProductInformation {
    id: number;
    catalogProductId: number,
}

/**
 * @swagger
 *  components:
 *      schemas:
 *          Product:
 *              properties:
 *                  id:
 *                      title: "Product Id"
 *                      type: integer
 *                  amount:
 *                      title: "Amount"
 *                      type: integer
 *                      nullable: true
 *                  attributes:
 *                      $ref: '#/components/schemas/ProductAttributes'
 *                  barcodes:
 *                      title: "Barcodes"
 *                      type: object
 *                      nullable: true
 *                  brandName:
 *                      title: "Brand"
 *                      type: string
 *                      nullable: true
 *                  cannabis:
 *                      title: "Cannabis"
 *                      type: boolean
 *                      nullable: true
 *                  classification:
 *                      title: "Classification"
 *                      type: string
 *                      nullable: true
 *                  displayName:
 *                      title: "Display Name"
 *                      type: string
 *                      nullable: true
 *                  descriptions:
 *                      title: "Descriptions"
 *                      type: object
 *                      nullable: true
 *                  details:
 *                      $ref: '#/components/schemas/ProductDetails'
 *                  eCommerceName:
 *                      title: "eCommerce Name"
 *                      type: string
 *                      nullable: true
 *                  images:
 *                      type: array
 *                      items:
 *                          type: object
 *                          properties:
 *                              default:
 *                                  type: boolean
 *                              url:
 *                                  type: string
 *                  msrp:
 *                      title: "MSRP"
 *                      type: number
 *                      nullable: true
 *                  price:
 *                      title: "Price"
 *                      type: number
 *                  name:
 *                      title: "Name"
 *                      type: string
 *                  packageTracked:
 *                      title: "Package Tracked"
 *                      type: boolean
 *                      nullable: true
 *                  productShortCode:
 *                      title: "Product Short Code"
 *                      type: string
 *                      nullable: true
 *                  size:
 *                      title: "Size"
 *                      type: string
 *                      nullable: true
 *                  sku:
 *                      title: "SKU"
 *                      type: string
 *                      nullable: true
 *                  subtype:
 *                      title: "Subtype"
 *                      type: string
 *                      nullable: true
 *                  tpc:
 *                      title: "TPC"
 *                      type: string
 *                      nullable: true
 *                  type:
 *                      title: "Type"
 *                      type: string
 *                  uom:
 *                      title: "Unit of Measure"
 *                      type: string
 *                  upc:
 *                      title: "UPC"
 *                      type: string
 */

@Table({tableName: 'products'})
export default class Product extends TreezModel<Product> {
    //Product Record Fields
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                             number;

    @Column(decimalField)
    amount:                         number | null;

    @Is('validProductAttributes', validProductAttributes)
    @Column(jsonField)
    attributes:                     ProductAttributes | null;

    @Column(jsonField)
    barcodes:                       object | null;

    @Column(stringField)
    brandName:                      string | null;

    @Column(booleanField)
    cannabis:                       boolean | null;

    @Column(stringField)
    classification:                 string | null;

    @Column(stringField)
    displayName:                    string | null;

    @Column(jsonField)
    descriptions:                   object | null;

    @Is('valid product details', validProductDetails)
    @Column(jsonField)
    details:                        ProductDetails | null;

    @Column(stringField)
    eCommerceName:                  string | null;

    @Column(stringField)
    externalId:                     string | null;

    @Column(jsonField)
    images:                         ImageAttributes[];

    @ForeignKey(() => Product)
    @Column(intField)
    linkedTo:                       number | null;

    @ForeignKey(() => Product)
    @Column(intField)
    mergedTo:                       number | null;

    @Column(preciseMonetaryField)
    msrp:                           number | null;

    @Column(notNull(stringField))
    name:                           string;

    @Column(notNull(intField))
    ownerId:                        number;

    @Is('isValidOwnerType', (ownerSubmitted: string) => Object.values(OwnerType).includes(ownerSubmitted as OwnerType))
    @Column(notNull(enumField(OwnerType)))
    ownerType:                      OwnerType;

    @Column(jsonField)
    productOverrides:               Partial<Product> | null;

    @Column(booleanField)
    packageTracked:                 boolean | null;

    @Column(stringField)
    productShortCode:               string | null;

    @Column(stringField)
    size:                           string | null;

    @Column(stringField)
    sku:                            string | null;

    @Column(stringField)
    searchVector:                   string | null;

    @Column(stringField)
    subtype:                        string | null;

    @Column(stringField)
    tpc:                            string | null;

    @Column(notNull(stringField))
    type:                           string;

    @Is('isValidUOM', validUOM)
    @Column(notNull(enumField(UOM)))
    uom:                            string;

    @Column(stringField)
    upc:                            string | null;

    @Default(true)
    @Column(booleanField)
    visible:                        boolean;

    //Relations
    @BelongsToMany(() => Catalog, () => CatalogProduct)
    catalogs:                       Catalog[];

    //Relations
    @BelongsToMany(() => Assortment, () => AssortmentProduct)
    assortments:                    Assortment[];

    @HasMany(() => ExternalReference)
    externalReferences:             ExternalReference[];

    @HasMany(() => Product)
    linkedProducts:                 Product[];

    @BelongsTo(() => Product)
    linkedBrandProduct:             Product;

    @BelongsTo(() => Product)
    mergedToProduct:                Product;

    @HasMany(() => CatalogProduct)
    catalogProducts:                CatalogProduct[];

    //Hooks
    @AfterCreate
    @AfterUpdate
    static async updateVector(instance: Product) {
        const productWithOverrides = await instance.applyOverrides();

        const {
            name,
            brandName,
            subtype,
            classification,
            size,
            type,
        } = productWithOverrides;

        await instance.sequelize.query(`
        UPDATE products SET "searchVector" =
        setweight(to_tsvector('english', coalesce(:name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(:brandName, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(:subtype, '')), 'C') ||
        setweight(to_tsvector('english', coalesce(:classification, '')), 'D') ||
        setweight(to_tsvector('english', coalesce(:size, '')), 'D') ||
        setweight(to_tsvector('english', coalesce(:type, '')), 'D') ||
        setweight(
            to_tsvector('english',
                COALESCE(
                    (
                        SELECT string_agg(value::text, ' ')
                        FROM jsonb_each_text(attributes)
                    ),
                    ''
                )
            ),
            'D'
        )
        WHERE id = :productId`,
        {
            replacements: {
                productId: instance.id,
                name,
                brandName,
                subtype,
                classification,
                size,
                type,
            },
            model: Product
        });
    }

    @BeforeCreate
    @BeforeUpdate
    static async trimFields(instance: Product) {
        const {
            brandName,
            displayName,
            eCommerceName,
            name,
            type,
        } = instance;

        if (brandName) {
            instance.set('brandName', brandName.trim());
        }

        if (displayName) {
            instance.set('displayName', displayName.trim());
        }

        if (eCommerceName) {
            instance.set('eCommerceName', eCommerceName.trim());
        }

        if (name) {
            instance.set('name', name.trim());
        }

        if (type === ProductType.MERCH) {
            instance.set('cannabis', false);
        }
    }

    /**
     * Deeply compares two products to determine if they're semantically equal,
     * coercing the type of known numeric fields and optionally considering nil and
     * empty values as equal.
     *
     * @param otherProduct product to compare to
     * @param nilIsEmpty whether `null` and `undefined` values are considered equal to empty arrays or objects
     *
     * @see lodash:isEqual
     * @see lib/util:isNilOrEmptyObject
     */
    public isEqual(otherProduct: Product, nilIsEmpty = false) {
        const productParsed = parseNumericFields(this);
        const otherProductParsed = parseNumericFields(otherProduct);

        if (nilIsEmpty) {
            return _.isEqualWith(productParsed, otherProductParsed, (objValue, othValue) => {
                if ((objValue === othValue) ||
                    (isNilOrEmptyObject(objValue) && isNilOrEmptyObject(othValue))
                ) {
                    return true;
                }
                return undefined; // default comparison logic
            });
        }

        return _.isEqual(productParsed, otherProductParsed)
    }

    public async applyOverrides() {
        const product = _.omit(this.get(), 'productOverrides');
        const productOverrides = this.productOverrides;

        if (product.linkedTo == null) {
            return product;
        }
        else {
            const linkedBrandProduct = await findProductById(product.linkedTo);
            const brandProductValues = linkedBrandProduct.getLinkableFields();

            const brandProductDetails = _.get(brandProductValues, 'details', {});
            const brandProductAttributes = _.get(brandProductValues, 'attributes', {});

            const overrideDetails = _.get(productOverrides, 'details', {});
            const overrideAttributes = _.get(productOverrides, 'attributes', {});

            const appliedAttributes = {
                ...brandProductAttributes,
                ...overrideAttributes,
            }

            const appliedDetails = {
                ...brandProductDetails,
                ...overrideDetails,
            }

            return {
                ...product,
                ...brandProductValues,
                ...productOverrides,
                attributes: _.isEmpty(appliedAttributes) ? null : appliedAttributes,
                details: _.isEmpty(appliedDetails) ? null : appliedDetails,
            }
        }
    }

    public addExternalReference = async (
        type       : ExternalReferenceType,
        externalId?: string
    ) => {
        const product: Product = this;

        const externalIdToUse = externalId || uuidv4();

        return await ExternalReference.create({
            type,
            productId: product.id,
            externalId: externalIdToUse,
        });
    }

    public updateExternalReference = async (
        type       : ExternalReferenceType,
        externalId?: string
    ) => {
        const product: Product = this;

        const externalReference = await ExternalReference.findOne({
            where: {
                productId: product.id,
                type,
            }
        });

        const externalIdToUse = externalId || uuidv4();

        if (!externalReference) {
            return await this.addExternalReference(type, externalIdToUse);
        }

        return await externalReference.update({
            externalId: externalIdToUse,
        });
    }

    public validateOwnerType = (ownerTypes: OwnerType[]): void => {
        const isValid = _.includes(ownerTypes, this.ownerType) ? true : false;

        if (!isValid) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Invalid owner type`
            )
        }
    }

    public deleteExternalReference = async (externalId: string) => {
        const product: Product = this;

        const {
            id: productId,
        } = product;

        const externalReference = await ExternalReference.findOne({
            where: {
                productId,
                externalId,
            }
        });

        if (!externalReference) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `External Reference "${externalId}" Not Found for product "${productId}"!`
            );
        }

        await externalReference.destroy();
    }

    public getExternalReferences = async (type?: ExternalReferenceType) => {
        const product: Product = this;

        if(type) {
            return await ExternalReference.findAll({
                where: {
                    productId: product.id,
                    type,
                }
            })
        }
        else {
            return await ExternalReference.findAll({
                where: {
                    productId: product.id,
                }
            })
        }
    }

    public getLinkableFields = () => {
        const product: Product = this;
        return _.pick(
            product.get(),
            Product.linkableBrandFields
        ) as Partial<Product>;
    }

    static getProductIdsLinkedToProduct = async (brandProductId: number): Promise<number[]> => {
        return _.map(await Product.findAll({
            where: {
                linkedTo: brandProductId
            },
            attributes: ['id']
        }), 'id') as number[];
    }

    static getProductIdsMergedToProduct = async (productId: number): Promise<number[]> => {
        return _.map(await Product.findAll({
            where: {
                mergedTo: productId
            },
            attributes: ['id']
        }), 'id') as number[];
    }

    static getProductIdsInfo = async (productIds: number[]): Promise<Product[]> => {
        let products: Product[] = await Product.getMergedProductsInfoByIds(productIds, "\"mergedTo\"");
        const extractedProductIds = products.map(p => p.id);
        const filteredProductIds = productIds.filter(id => !extractedProductIds.includes(id));

        if (filteredProductIds.length > 0) {
            products.push.apply(products, await Product.getMergedProductsInfoByIds(filteredProductIds, "id"));
        }

        return products;
    }

    static getMergedProductsInfoByIds = async (productIds: number[], columnName: string): Promise<Product[]> => {
        const query = `SELECT p.id, p."mergedTo", er."externalId" FROM products p
                INNER JOIN "externalReferences" er ON er."productId" = p.id
                WHERE er.type = '${ExternalReferenceType.SELL_TREEZ_ID}'
                AND p.${columnName} IN (${productIds})`;
        return connection.query(query, {model: Product});
    }

    public getBrandMatches = async (brandName?: string): Promise<ProductMatch[]> => {
        const brandNameCondition = brandName ? `AND
            p."brandName" = '${cleanTextForSQL(brandName)}'` : '';

        const queryResult = await connection.query(`
            SELECT
                ${Product.defaultAttributes.map((field) => {
                    return `p."${field}",`;
                }).join(`
                `)}
                similarity(p."brandName", '${cleanTextForSQL(this.brandName || '')}') AS "brand_name_score",
                similarity(p."name", '${cleanTextForSQL(this.name)}') AS "name_score"
            FROM "products" p
            WHERE
                p."ownerType" = 'brand'
            AND
                (
                    similarity(p."brandName", '${cleanTextForSQL(this.brandName || '')}')
                    + similarity(p."name", '${cleanTextForSQL(this.name)}')
                ) > 1
            ${brandNameCondition}
            ORDER BY (
                similarity(p."brandName", '${cleanTextForSQL(this.brandName || '')}')
                + similarity(p."name", '${cleanTextForSQL(this.name)}')
            );
        `);

        return queryResult[0] as ProductMatch[];
    };

    public getAdoption = async (): Promise<ProductAdoption> => {
        return await transaction(async () => {
            const [
                linkedRetailCount,
                brandSuggestedLinksCount,
                weightedMatches,
            ] = await Promise.all([
                this.getLinkedCatalogProductCount(),
                this.getBrandSuggestedLinksCount(),
                this.getWeightedMatches(),
            ]);

            const totalCount = new Big(0)
                .add(linkedRetailCount)
                .add(brandSuggestedLinksCount)
                .add(weightedMatches)
                .toNumber();

            let linkAdoption, brandSuggestedAdoption;

            if(totalCount === 0) {
                linkAdoption = 0;
                brandSuggestedAdoption = 0;
            }
            else {
                linkAdoption = Number(
                new Big(linkedRetailCount)
                    .div(totalCount)
                    .toFixed(2)
                );
                brandSuggestedAdoption = Number(
                new Big(
                    brandSuggestedLinksCount)
                    .div(totalCount)
                    .toFixed(2)
                );
            }

            return {
                productId: this.id,
                linkedRetailCount,
                brandSuggestedLinksCount,
                totalCount,
                weightedMatches,
                linkAdoption,
                brandSuggestedAdoption,
            } as ProductAdoption;
        });
    };

    static getProductsAdoptionMetrics = async (productIds: number[]) => {
        type ProductId = number;

        return await transaction (async () => {
            const products = await Product.findAll({
                where: {
                    id: productIds,
                }
            });

            const adoptions =  await Promise.all(products.map((product) => {
                return product.getAdoption();
            }));

            return adoptions.reduce((a, b) => {
                return {
                    ...a,
                    [b.productId]: b,
                }
            }, {}) as Record<ProductId, ProductAdoption>;
        })
    }

    public getLinkedCatalogProductCount = async () => {
        this.validateOwnerType([OwnerType.BRAND]);
        const queryResult = await connection.query(`
        SELECT
            COUNT(*) ::INTEGER as "adoptedProductCount"
        FROM "catalogProducts" cp
        JOIN "products" p ON p.id = cp."productId"
        WHERE p."linkedTo" = :id
        `, {type: "SELECT", replacements: { id: this.id }});

        return _.get(queryResult[0], 'adoptedProductCount', 0);
    }

    public getBrandSuggestedLinksCount = async () => {
        this.validateOwnerType([OwnerType.BRAND]);

        const queryResult = await connection.query(`
        SELECT
            COUNT(*) ::INTEGER AS "brandSuggestedLinkCount"
        FROM "suggestedLinks" sl
        LEFT JOIN "catalogProducts" cp ON  sl."productToLinkId" = cp."productId"
        WHERE sl."type" = '${MatchType.BrandSuggested}'
        AND sl."brandProductId" = ${this.id}
        `, {type: "SELECT"});

        return _.get(queryResult[0], 'brandSuggestedLinkCount', 0);
    }

    public getWeightedMatches = async () => {
        this.validateOwnerType([OwnerType.BRAND]);

        const queryResult =  await connection.query(`
        SELECT
            COUNT(*) ::INTEGER AS "treezWeightedMatchCount"
        FROM "catalogProducts" cp
        LEFT JOIN "suggestedLinks" sl
        ON  sl."productToLinkId" = cp."productId"
        WHERE sl."type" = '${MatchType.Weighted}'
        AND sl."brandProductId" = :id
        `, {type: "SELECT", replacements: {
            id: this.id
        }});

        return _.get(queryResult[0], 'treezWeightedMatchCount', 0);
    }

    public getSellTreezId = async () => {
        const product: Product = this;

        const reference = await ExternalReference.findOne({
            where: {
                productId: product.id,
                type: ExternalReferenceType.SELL_TREEZ_ID,
            },
            order: [['createdAt', 'DESC']]
        });

        const sellTreezId = _.get(reference, 'externalId', null);

        return sellTreezId;
    }

    public clearOverrides = async (fields : ProductData[]) => {
        const product: Product = this;

        const productFieldPaths = getProductFieldPaths(fields);

        let productOverrides = _.omit(product.productOverrides, productFieldPaths);

        if (_.isEmpty(productOverrides.details)) {
            productOverrides = _.omit(productOverrides, 'details');
        }
        if (_.isEmpty(productOverrides.attributes)) {
            productOverrides = _.omit(productOverrides, 'attributes');
        }

        await product.update({
            productOverrides,
        });
    }

    public setMergedTo = async (mergedTo : number) => {
        const product: Product = this;

        await product.update({
            mergedTo
        });
    }

    //Class Properties and Methods
    static defaultAttributes = [
        'id',
        'amount',
        'attributes',
        'barcodes',
        'brandName',
        'cannabis',
        'classification',
        'descriptions',
        'details',
        'displayName',
        'eCommerceName',
        'externalId',
        'images',
        'msrp',
        'name',
        'ownerId',
        'ownerType',
        'packageTracked',
        'size',
        'sku',
        'subtype',
        'tpc',
        'type',
        'uom',
        'upc',
        'visible',
        'createdAt',
        'updatedAt',
    ];

    static linkableBrandFields = [
        'attributes',
        'barcodes',
        'brandName',
        'cannabis',
        'classification',
        'descriptions',
        'details',
        'displayName',
        'eCommerceName',
        'images',
        'msrp',
        'name',
        'packageTracked',
        'productShortCode',
        'size',
        'sku',
        'subtype',
        'tpc',
        'type',
        'upc',
    ];

    static unlinkableFields = [
        'id',
        'amount',
        'createdAt',
        'externalId',
        'linkedTo',
        'ownerId',
        'ownerType',
        'productOverrides',
        'uom',
        'visible',
        'updatedAt',
        'mergedTo',
    ]

    static validPatchFields = [
        'amount',
        'attributes',
        'barcodes',
        'brandName',
        'cannabis',
        'classification',
        'descriptions',
        'details',
        'eCommerceName',
        'externalId',
        'images',
        'linkedTo',
        'msrp',
        'name',
        'packageTracked',
        'productShortCode',
        'priceTierId',
        'size',
        'sku',
        'subtype',
        'type',
        'uom',
        'visible',
    ];

    static validAttributeFields = [
        'effect',
        'flavor',
        'general',
        'ingredient',
        'internal',
    ];

    //JSON SCHEMAS FOR VALIDATION
    static attributesSchema = {
        type: "object",
        properties: {
            effect: {
                items: {
                    type: "string",
                }
            },
            flavor: {
                items: {
                    type: "string",
                }
            },
            general: {
                items: {
                    type: "string",
                }
            },
            ingredient: {
                items: {
                    type: "string",
                }
            },
            internal: {
                items: {
                    type: "string",
                }
            },
        },
        propertyNames: {
            enum: Product.validAttributeFields
        }
    }

    static productDetailsSchema = {
        type: "object",
        properties: {
            cbdMg: {
                type: ["number", "null"],
                minimum: 0,
            },
            doseCbdMg: {
                type: ["number", "null"],
                minimum: 0,
            },
            doses: {
                type: ["number", "null"],
                minimum: 0,
            },
            doseThcMg: {
                type: ["number", "null"],
                minimum: 0,
            },
            extractionMethod: {
                type: "string",
            },
            strain: {
                type: "string",
            },
            thcMg: {
                type: ["number", "null"],
                minimum: 0,
            },
            totalFlowerWeight: {
                type: ["number", "null"],
                minimum: 0,
            },
            totalConcentrateWeight: {
                type: ["number", "null"],
                minimum: 0,
            }
        }
    }
}

export const findProductsByExternalIdsAndCatalog = async (
    externalIds: string[],
    catalogId: number
) => {
    const productIds = await ExternalReference.findProductIds(externalIds);
    const catalog = await findCatalogById(catalogId);
    const ownerIds = [catalog.ownerId];
    const ownerTypes = [catalog.ownerType];

    if (catalog.parentCatalogId) {
        const parentCatalog = await findCatalogById(catalog.parentCatalogId);
        ownerIds.push(parentCatalog.ownerId);
        ownerTypes.push(parentCatalog.ownerType);
    }

    const products = await Product.findAll({
        where: {
            id        : productIds,
            ownerId   : ownerIds,
            ownerType : ownerTypes,
        }
    });

    return products;
}

export const addBrandTreezProductToCatalog = async (
    catalogId: number,
    brandTreezProductId: number,
    userInfo: UserInfo,
    catalogInformation: Partial<CatalogProduct> = {},
) => {
    const [
        catalog,
        brandProduct,
    ] = await Promise.all([
        findCatalogById(catalogId),
        findProductById(brandTreezProductId),
    ]);

    const productValues = {
        ..._.omit(
            _.get(brandProduct, 'dataValues'),
            [
                'id',
                'ownerType',
                'ownerId',
                'createdAt',
                'updatedAt',
                'visible',
                'linkedTo',
                'externalId'
            ]
        ),
        ownerId: catalog.ownerId,
        ownerType: catalog.ownerType,
        ..._.omit(
            _.get(catalogInformation, 'catalogOverrides'),
            [
                'id',
                'ownerType',
                'ownerId',
            ]
        )
    } as Partial<ProductInformation>

    const product = await createProductForCatalog(catalogId, productValues, userInfo, false);

    const newProductId: number = _.get(product, 'id');

    return await linkProducts(newProductId, brandProduct.id, userInfo);
}

export const findProductById = async (
    productId: number,
    options: {
        includeBrandProduct?: boolean,
        includeOptions?: IncludeOptions[],
    } = {}
) => {
    const {
        includeBrandProduct,
        includeOptions = []
    } = options;

    if (includeBrandProduct) {
        includeOptions.push(
            {
                model: Product,
                as: 'linkedBrandProduct',
                attributes: Product.defaultAttributes
            }
        )
    }

    const product = await Product.findByPk(productId, {
        include: includeOptions
    });

    if (product == null) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Product with the id of ${productId} could not be found`
        )
    }
    return product;
}

export const getProduct = async (options: FindOptions) => {
    const queryOptions = {
        attributes: Product.defaultAttributes,
        ...options,
    }

    const product = await Product.findOne(queryOptions);

    if ( product == null) {
        throw new ErrorResponse(StatusCodes.NOT_FOUND, `Requested product does not exist.`);
    }

    return product;
};

export const getProductWithOverrides = async (
    productId: number,
    options: {
        include?: string,
        catalogId?: number,
        orgId?: number,
        excludeLinkedProduct?: boolean,
    } = {}
) => {
    const {
        catalogId,
        excludeLinkedProduct,
        include,
        orgId,
    } = options;

    const modelsToInclude = include ? buildIncludes(_.split(include, ','), orgId) : []

    if (catalogId) {
        const catalogProduct = await findCatalogProduct(productId, catalogId);
        return catalogProduct.getProductCatalogPresence(excludeLinkedProduct);
    }
    else {
        const product = await findProductById(
            productId,
            {
                includeBrandProduct: true,
                includeOptions: modelsToInclude,
            }
        );
        if (excludeLinkedProduct === true) {
            return product.get();
        } else {
            return product.applyOverrides();
        }
    }
}

export const createProduct = async (
    productData: Partial<ProductRecord>,
    userInfo: UserInfo,
    mergedFromProductIds?: number[],
) => {
    const newProduct = await Product.create(productData)

    await recordChange(
        newProduct.id,
        ProductAction.CREATE,
        userInfo,
        newProduct,
        undefined,
        mergedFromProductIds
    );

    return newProduct;
}

export const createProductForCatalog = async (
    catalogId: number,
    productData: Partial<ProductInformation>,
    userInfo: UserInfo,
    isEpmProductFlag: boolean,
): Promise<Partial<ProductInCatalog> | Partial<ProductInCatalog>[]> => {
    return await transaction(async () => {
        const catalog = await Catalog.findByPk(catalogId, {
            include: [
                {
                    model: ProductRequirements,
                    as: "productRequirements"
                }
            ]
        });

        if (catalog == null) {
            throw new ErrorResponse(StatusCodes.NOT_FOUND, `Catalog for ID ${catalogId} not found`);
        }

        if (_.isArray(productData)) {
            return await Promise.all(
                _.map(productData, async (product) => {
                    return await saveProductForCatalog(product, catalog, userInfo, isEpmProductFlag)
                })
            )
        }

        return await saveProductForCatalog(productData, catalog, userInfo, isEpmProductFlag)
    });
}

export const capitalizeProductAttributes = (attributes: ProductAttributes) => {
    if (attributes == null) return attributes;

    const upperCaseArray = (arr?: string[]) => arr?.map((item) => item.toUpperCase());

    return {
        effect: upperCaseArray(attributes.effect),
        flavor: upperCaseArray(attributes.flavor),
        general: upperCaseArray(attributes.general),
        ingredient: upperCaseArray(attributes.ingredient),
        internal: upperCaseArray(attributes.internal),
    };
}

export const saveProductForCatalog = async (
    productData: Partial<ProductInformation>,
    catalog: Catalog,
    userInfo: UserInfo,
    isEpmProductFlag: boolean,
): Promise<Partial<ProductInCatalog>> => {
    let isOrgCatalogExist = false;
    let { ownerId } = catalog;
    if (isEpmProductFlag) {
        const parentCatalog = await Catalog.findOne({
            where: {
                id: catalog.parentCatalogId,
            }
        });
        if (parentCatalog) {
            ownerId = parentCatalog.ownerId;
            isOrgCatalogExist = true;
        }
    }
    let ownerType = isEpmProductFlag ? OwnerType.ORG : catalog.ownerType;
    const productToAddToCatalog = {
        ...productData,
        ownerId: ownerId,
        ownerType: ownerType,
    }
    const productIsValidPerCatalogRequirements = await validateProductAgainstRequirements(productToAddToCatalog, catalog);
    let shouldFindExistingProduct = isEpmProductFlag && productToAddToCatalog?.id;
    if (productIsValidPerCatalogRequirements) {
        if (shouldFindExistingProduct) {
            const existingProduct = await Product.findOne({
                where: {
                    id: productToAddToCatalog.id
                }
            });
            if (existingProduct) {
                return productData;
            }
        }
        if (productToAddToCatalog.attributes) {
            productToAddToCatalog.attributes = capitalizeProductAttributes(productToAddToCatalog.attributes);
        }
        const product = await createProduct(productToAddToCatalog, userInfo);
        let catalogProduct;
        if (isEpmProductFlag && isOrgCatalogExist) {
            catalogProduct = await addProductToEpm(
                catalog,
                product.id,
                userInfo,
                {
                    price: productToAddToCatalog.price,
                    status: productToAddToCatalog.status || CatalogProductStatus.ACTIVE,
                    priceTierId: productToAddToCatalog.priceTierId,
                },
            );
        }
        else {
            catalogProduct = await addProductToCatalog(
                catalog,
                product.id,
                userInfo,
                {
                    price: productToAddToCatalog.price,
                    status: productToAddToCatalog.status || CatalogProductStatus.ACTIVE,
                    priceTierId: productToAddToCatalog.priceTierId,
                },
            );
        }

        const productInformation = await catalogProduct.getProductCatalogPresence();

        const externalReference = await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            productData.uuid,
        );

        if (productData.weedMapsProductVariantId) {
            await product.addExternalReference(
                ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID,
                productData.weedMapsProductVariantId
            );
        }

        return {
            ...productInformation,
            uuid: externalReference.externalId,
        };
    }
    else {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product has failed catalog validation for product requirements`
        );
    }
}

export const linkProducts = async (
    productToLinkId: number,
    brandProductToLinkToId: number,
    userInfo: UserInfo,
    productOverrides: Partial<Product> = {},
) => {
    const [
        productToLink,
        brandProductToLinkTo,
    ] = await Promise.all([
        findProductById(productToLinkId),
        findProductById(brandProductToLinkToId)
    ]);

    if (brandProductToLinkTo.ownerType !== OwnerType.BRAND) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product you are trying to link to is not a brand product: product id ${brandProductToLinkToId}`
        );
    }

    if (productToLink.ownerType === OwnerType.BRAND) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product you are trying to to link is a Brand Product and can not be linked to another Brand Product`
        );
    }

    return await transaction(async () => {
        await updateProduct(
            productToLink.id,
            {
                linkedTo: brandProductToLinkToId,
                productOverrides,
            },
            userInfo,
            ProductAction.LINK,
        );

        await ProductUpdateOutbox.createRecord(productToLink.id, productToLink.ownerType, UPDATE);

        //Need to record product change here in the future.

        return await getProductWithOverrides(productToLinkId);
    });
}

type CatalogId = number;
export type CatalogProductInformation = Record<CatalogId, Partial<CatalogProduct>>;

export const mergeProductsToExistingProduct = async (
    productToMergeToId: number,
    targetCatalogId: number,
    productIdsToMerge: number[],
    userInfo: UserInfo,
    catalogProductInformation: CatalogProductInformation = {},
) => {
    const [
        productToMergeTo,
        targetCatalog,
    ] = await Promise.all([
        findProductById(productToMergeToId),
        findCatalogById(targetCatalogId)
    ]);

    const productToMergeToValues = _.omit(
        _.get(productToMergeTo, 'dataValues'),
        [
            'id',
            'ownerType',
            'ownerId'
        ]
    );

    const result = await transaction(async () => {
        if (productToMergeTo.ownerType === targetCatalog.ownerType
            && productToMergeTo.ownerId === targetCatalog.ownerId) {
                return await mergeProducts(
                    productToMergeTo,
                    targetCatalog,
                    productIdsToMerge,
                    userInfo,
                    catalogProductInformation,
                );
        }
        else if (productToMergeTo.ownerType === OwnerType.BRAND) {
            const newlyMergedProduct = await mergeProductsToNewProduct(
                productToMergeToValues,
                targetCatalogId,
                productIdsToMerge,
                userInfo,
                catalogProductInformation,
            );

            return await linkProducts(newlyMergedProduct.id, productToMergeToId, userInfo);
        }
        else {
            const productHomeCatalog = await Catalog.findOne({
                where: {
                    ownerId: productToMergeTo.ownerId,
                    ownerType: productToMergeTo.ownerType
                }
            });
            if (productHomeCatalog == null
                || _.get(productHomeCatalog, 'parentCatalogId') !== targetCatalogId) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    `Product You are trying to merge to does not belong to the organization or store`
                );
            }
            else {
                return await mergeProductsToNewProduct(
                    productToMergeToValues,
                    targetCatalogId,
                    [...productIdsToMerge, productToMergeTo.id],
                    userInfo,
                    catalogProductInformation
                );
            }
        }
    });

    return result;
}

export const mergeProductsToNewProduct = async (
    product: Partial<ProductRecord>,
    centralCatalogId: number,
    productIdsToMerge: number[],
    userInfo: UserInfo,
    catalogProductInformation: CatalogProductInformation = {},
): Promise<ProductInCatalog> => {
    const result = await transaction(async() => {
        const centralCatalog = await findCatalogById(centralCatalogId)

        const newProductToMergeTo = await createProduct(
            {
                ...product,
                ownerType: centralCatalog.ownerType,
                ownerId: centralCatalog.ownerId,
            },
            userInfo,
            productIdsToMerge,
        );

        if (newProductToMergeTo.ownerType != OwnerType.BRAND) {
            await newProductToMergeTo.addExternalReference(
                ExternalReferenceType.SELL_TREEZ_ID,
            );
        }

        await addProductToCatalog(
            centralCatalog,
            newProductToMergeTo.id,
            userInfo,
            { status: CatalogProductStatus.ACTIVE }
        );

        return await mergeProducts(
            newProductToMergeTo,
            centralCatalog,
            productIdsToMerge,
            userInfo,
            catalogProductInformation,
        );
    })

    return result
}

export const mergeProducts = async (
    productToMergeTo: Product,
    targetCatalog: Catalog,
    productIdsToMerge: number[],
    userInfo: UserInfo,
    catalogProductInformation: CatalogProductInformation = {},
): Promise<ProductInCatalog> => {
    //userAuthId will be used once tracking catalogProducts history is added;
    const [
        catalogProducts,
        productsToMerge,
    ] = await Promise.all([
        CatalogProduct.findAll({
            where: {
                productId: productIdsToMerge
            },
            include: [
                {
                    model: Product,
                    as: 'product',
                },
                {
                    model: Catalog,
                    as: 'catalog',
                }
            ]
        }),
        Product.findAll({
            where: {
                id: productIdsToMerge
            }
        })
    ]);


    const catalogIds = _.uniq(
        (catalogProducts.map(({catalog}) => {
            return catalog.id;
        }))
    );

    const invalidCatalogProducts = _.filter(catalogProducts, (catalogProduct) => {
        const parentCatalogId = _.get(catalogProduct, 'catalog.parentCatalogId', 0) as number;
        return catalogProduct.catalogId !== targetCatalog.id && parentCatalogId !== targetCatalog.id;
    });

    if (_.size(invalidCatalogProducts)) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Trying to merge products from different catalogs into a shared product in a catalog that is not of a shared relation with all catalogs`
        );
    }

    if (targetCatalog.ownerType === OwnerType.STORE) {
        _.forEach(catalogProducts, (catalogProduct) => {
            if (catalogProduct.product.ownerType !== OwnerType.STORE
                || catalogProduct.product.ownerId !== targetCatalog.ownerId
            ) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    `Trying to merge products in a store catalog that are from the organization`
                );
            }
        });
    }

    const productToMergeToSellTreezId = await productToMergeTo.getSellTreezId();

    if (productToMergeTo.ownerType != OwnerType.BRAND && !productToMergeToSellTreezId) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Unable to find external reference for newly merged product.`
        );
    }

    const result = await transaction(async () => {
        await Promise.all([
            ..._.map(catalogProducts, async (catalogProduct) => {
                //remove the product from the catalog
                await removeProductFromCatalog(
                    catalogProduct.catalogId,
                    catalogProduct.productId,
                    userInfo,
                );
            }),
            ..._.map(productsToMerge, async (productToMerge) => {
                await setProductMergedTo(productToMerge, productToMergeTo.id, userInfo);
            }),
            ..._.map(catalogIds, async (id) => {
                const catalogToAddTo = await findCatalogById(id);

                await addProductToCatalog(
                    catalogToAddTo,
                    productToMergeTo.id,
                    userInfo,
                    catalogProductInformation[id],
                );

            })
        ]);

        // record on the product merged to that there was a merge
        await recordChange(
            productToMergeTo.id,
            ProductAction.MERGE,
            userInfo,
            productToMergeTo,
            undefined,
            productIdsToMerge
        );

        await ProductUpdateOutbox.createRecord(productToMergeTo.id, productToMergeTo.ownerType, MERGE);

        const updatedMasterCatalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: targetCatalog.id,
                productId: productToMergeTo.id
            }
        });

        if (updatedMasterCatalogProduct == null) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Merged Product failed to save to master catalog`
            )
        }

        return updatedMasterCatalogProduct.getProductCatalogPresence();
    });

    return result;
}

export const setProductMergedTo = async (productToMerge: Product, mergeToProductId: number, userInfo: UserInfo,) => {
    const oldProduct = productToMerge.toJSON() as Product;

    await productToMerge.setMergedTo(mergeToProductId);
    const updatedProduct  = await productToMerge.reload();

    await recordChange(
        oldProduct.id,
        ProductAction.MERGE,
        userInfo,
        updatedProduct,
        oldProduct,
    );
}

export const updateProductInCatalog = async (
    productId: number,
    catalogId: number,
    productChanges: Partial<ProductInCatalog>,
    userInfo: UserInfo,
) => {
    const [
        catalog,
        product,
    ] = await Promise.all([
        findCatalogById(catalogId),
        findProductById(productId)
    ]);

    const productsNativeCatalog = await Catalog.findOne({
        where: {
            ownerType: product.ownerType,
            ownerId: product.ownerId,
        }
    });

    const productChangesWithoutUndefinedValues = _.pickBy(productChanges, (value) => {
        return value !== undefined;
    });

    const productUpdates = _.pick(
        productChangesWithoutUndefinedValues,
        Product.validPatchFields,
    );

    const catalogProductUpdates = _.pick(
        productChangesWithoutUndefinedValues,
        [
            'status',
            'price',
            'priceTierId'
        ]
    ) as Partial<CatalogProductRecord>;

    const productIsOwnedByRequestingCatalog = product.ownerType === catalog.ownerType && product.ownerId === catalog.ownerId;
    const catalogIsCentralCatalog = productsNativeCatalog && catalog.id === productsNativeCatalog.parentCatalogId;

    return await transaction(async () => {
        if(productIsOwnedByRequestingCatalog
        || catalogIsCentralCatalog) {
            await updateProduct (
                product.id,
                productUpdates,
                userInfo
            );

            const updatedCatalogProduct = await updateCatalogProduct(
                catalog.id,
                product.id,
                userInfo,
                catalogProductUpdates
            );

            if (productChanges.weedMapsProductVariantId) {
                await product.updateExternalReference(
                    ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID,
                    productChanges.weedMapsProductVariantId,
                );
            }

            await ProductUpdateOutbox.createRecord(product.id, product.ownerType, UPDATE);

            return await updatedCatalogProduct.getProductCatalogPresence();
        }
        else {
            const updatedCatalogProduct = await updateCatalogProduct(
                catalog.id,
                product.id,
                userInfo,
                {
                    ...catalogProductUpdates,
                    catalogOverrides: productUpdates
                }
            );

            await ProductUpdateOutbox.createRecord(product.id, product.ownerType, UPDATE);

            return updatedCatalogProduct.getProductCatalogPresence()
        }
    });
}

export const updateProduct = async (
    productId: number,
    updates: Partial<Product>,
    userInfo: UserInfo,
    actionType = ProductAction.UPDATE,
) => {
    const oldProduct = await findProductById(productId);

    const whitelistedUpdates =  _.omit(updates, ['id']);

    const details = mergeDetails(oldProduct.details, whitelistedUpdates.details);

    const attributes = {
        ...oldProduct.attributes,
        ...whitelistedUpdates.attributes,
    }

    const capitalizedAttributes = attributes ? capitalizeProductAttributes(attributes) : attributes;

    const updatesWithJsonFieldsMerged = {
        ...whitelistedUpdates,
        attributes: capitalizedAttributes,
        details,
    }

    // do not deep merge
    const newProduct : Product = new Product({
        ...oldProduct.get(),
        ...updatesWithJsonFieldsMerged,
    });

    if (oldProduct.isEqual(newProduct, true)) {
        return oldProduct;
    }


    const oldValues = oldProduct.toJSON();

    const finalUpdates = whitelistedUpdates.attributes ? {
        ...updatesWithJsonFieldsMerged,
        attributes: capitalizeProductAttributes(updatesWithJsonFieldsMerged.attributes),
    } : updatesWithJsonFieldsMerged;

    if (oldProduct.linkedTo == null) {
        await oldProduct.update(finalUpdates);
    }
    else {
        if (updatesWithJsonFieldsMerged.linkedTo === null) {
            await oldProduct.update({
                ...finalUpdates,
                linkedTo: null,
                productOverrides: null
            });
        }
        else {
            const details = {
                ..._.get(oldProduct, 'productOverrides.details'),
                ...whitelistedUpdates.details,
            }

            const attributes = {
                ..._.get(oldProduct, 'productOverrides.attributes'),
                ...whitelistedUpdates.attributes,
            }

            const capitalizedOverrideAttributes = attributes && !_.isEmpty(attributes) ? capitalizeProductAttributes(attributes) : attributes;

            await oldProduct.update({
                ...finalUpdates,
                productOverrides: {
                    ...oldProduct.productOverrides,
                    ...whitelistedUpdates,
                    attributes: _.isEmpty(capitalizedOverrideAttributes) ? undefined : capitalizedOverrideAttributes,
                    details   : _.isEmpty(details) ? undefined: details,
                }
            });
        }
    }

    const updatedProduct = await oldProduct.reload();

    await recordChange(
        productId,
        actionType,
        userInfo,
        updatedProduct,
        oldValues,
    );

    return updatedProduct;
}

export const unlinkProduct = async (productId: number, userInfo: UserInfo) => {
    return await transaction(async () => {
        const updatedProduct =  await updateProduct(
            productId,
            { linkedTo: null },
            userInfo,
            ProductAction.UNLINK,
        );

        await ProductUpdateOutbox.createRecord(productId, updatedProduct.ownerType, UPDATE);

        return updatedProduct;
    });
}

/* Temporary until Directory API can be integrated to search by brand name and alias */

export const searchForBrands = async (searchQuery: string) => {
    let searchStringToUse: string = searchQuery;

    if ( searchQuery.trim().length === 0 ) {
        searchStringToUse = '';
    }
    else {
        // Translation: Put a slash in front of any characters that would break the query
        searchStringToUse =  cleanTextForSQL(searchStringToUse)
    }

    const brandResponse =  await connection.query(
        `SELECT DISTINCT ON ("brandName")
            "brandName",
            "ownerId"
        FROM products WHERE
            "ownerType" = 'brand'
            AND "brandName" ILike '%${searchStringToUse}%'
        ;`
    );

    const brands = brandResponse[0];
    return brands;
}

export const findAndDeleteExternalReference = async (
    productId: number,
    externalReferenceId: string,
) => {
    const product = await findProductById(productId)

    return await product.deleteExternalReference(
        externalReferenceId
    );
}

/**
 * Merges two objects respecting null values, but for new fields adding only non-null.
 * E.g.: obj1 = { a: 1, b: 2,    c: 3,                x: null, y: null, z: null }
 *       obj2 = {       b: null, c: 4, d: null, e: 5, x: null, y: 6 }
 * Result:      { a: 1, b: null, c: 4,          e: 5, x: null, y: 6,    z: null }
 * 
 * @param obj1 existing product details
 * @param obj2 new product details
 * @returns object merged from arguments
 */
export const mergeDetails = (
    obj1: Partial<ProductDetails> | null,
    obj2: Partial<ProductDetails> | null | undefined
) => {
    if (!obj1) {
        return obj2 ? _.pickBy(obj2, (value) => { return value !== null; }) : null;
    }
    if (!obj2) {
        return obj1;
    }
    return {
        ...obj1,
        ...(_.pickBy(obj2, (value, key) => { return value !== null || key in obj1; }))
    };
}
