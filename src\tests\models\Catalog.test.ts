import {
    OwnerType,
    ProductType,
    UOM,
}                               from '@treez/commons/sharedTypings/product';
import _                        from 'lodash';
import Catalog                  from '../../models/Catalog';
import { addProductToCatalog }  from '../../models/CatalogProduct';
import Product, {
    linkProducts,
}                               from '../../models/Product';
import SuggestedLink, {
    MatchType,
}                               from '../../models/SuggestedLink';

const {
    STORE,
    ORG,
    BRAND
} = OwnerType;

const {
    G
} = UOM;

const {
    FLOWER,
} = ProductType;

const userInfo = {
    userAuthId: 'auth0|123',
    sellTreezId: '<EMAIL>'
} as UserInfo;

describe('Catalog Model and Class', () => {
    it(`uses getCatalogsWithBrandPresence method gets other catalogs that carry brand products`, async () => {
        const [
            brandCatalog,
            orgCatalog,
            brandProduct,
            orgProduct,
            store1Product,
            store2Product,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Canndescent',
                ownerId  : 200,
                ownerType: BRAND,  
            }),
            Catalog.create({
                name     : 'Sparc Organization',
                ownerId  : 1000,
                ownerType: ORG,    
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndescent',
                name     : 'Calm 101',
                ownerId  : 200,
                ownerType: BRAND,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Cannde-scent',
                name     : 'Calm 102',
                ownerId  : 1000,
                ownerType: ORG,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 3.5 g',
                ownerId  : 777,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 half 8th',
                ownerId  : 999,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
        ] = await Promise.all([
            Catalog.create({
                name           : 'Sparc Santa Rosa',
                ownerId        : 777,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
            Catalog.create({
                name           : 'Sparc Mission',
                ownerId        : 999,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
        ])

        await Promise.all([
            addProductToCatalog(brandCatalog, brandProduct.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct.id, userInfo),
            addProductToCatalog(store1Catalog, store1Product.id, userInfo),
            addProductToCatalog(store2Catalog, store2Product.id, userInfo),
        ]);

        const catalogsThatCarryProduct = await brandCatalog.getCatalogsWithBrandPresence();

        const sortedCatalogMatches = catalogsThatCarryProduct.sort((a,b) => {
            return a.catalogName > b.catalogName ? 1 : -1
        });

        expect(sortedCatalogMatches).toMatchObject([
            {
                catalogId: store2Catalog.id,
                catalogName: 'Sparc Mission',
                catalogOwnerType: OwnerType.STORE,
            },
            {
                catalogId: orgCatalog.id,
                catalogName: 'Sparc Organization',
                catalogOwnerType: OwnerType.ORG,
            },
            {
                catalogId: store1Catalog.id,
                catalogName: 'Sparc Santa Rosa',
                catalogOwnerType: OwnerType.STORE,
            },
        ]);
        expect(sortedCatalogMatches[0].brandProducts).toHaveLength(1);
        expect(sortedCatalogMatches[1].brandProducts).toHaveLength(3);
        expect(sortedCatalogMatches[2].brandProducts).toHaveLength(1);
    });

    it(`getCatalogsWithBrandPresence lowers the number of products present in the catalog if the products are linked`, async () => {
        const [
            brandCatalog,
            orgCatalog,
            brandProduct,
            orgProduct,
            store1Product,
            store2Product,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Canndescent',
                ownerId  : 200,
                ownerType: BRAND,  
            }),
            Catalog.create({
                name     : 'Sparc Organization',
                ownerId  : 1000,
                ownerType: ORG,    
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndescent',
                name     : 'Calm 101',
                ownerId  : 200,
                ownerType: BRAND,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Cannde-scent',
                name     : 'Calm 102',
                ownerId  : 1000,
                ownerType: ORG,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 3.5 g',
                ownerId  : 777,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 half 8th',
                ownerId  : 999,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
        ] = await Promise.all([
            Catalog.create({
                name           : 'Sparc Santa Rosa',
                ownerId        : 777,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
            Catalog.create({
                name           : 'Sparc Mission',
                ownerId        : 999,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
        ])

        await Promise.all([
            addProductToCatalog(brandCatalog, brandProduct.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct.id, userInfo),
        ]);

        await Promise.all([
            addProductToCatalog(store1Catalog, store1Product.id, userInfo),
            addProductToCatalog(store2Catalog, store2Product.id, userInfo),
        ]);

        await Promise.all([
            linkProducts(store1Product.id, brandProduct.id, userInfo),
            linkProducts(store2Product.id, brandProduct.id, userInfo),
        ])

        const catalogsThatCarryProduct = await brandCatalog.getCatalogsWithBrandPresence();

        const sortedCatalogMatches = catalogsThatCarryProduct.sort((a,b) => {
            return a.catalogName > b.catalogName ? 1 : -1
        });

        expect(sortedCatalogMatches).toMatchObject([
            {
                catalogId: store2Catalog.id,
                catalogName: 'Sparc Mission',
                catalogOwnerType: OwnerType.STORE,
            },
            {
                catalogId: orgCatalog.id,
                catalogName: 'Sparc Organization',
                catalogOwnerType: OwnerType.ORG,
            },
            {
                catalogId: store1Catalog.id,
                catalogName: 'Sparc Santa Rosa',
                catalogOwnerType: OwnerType.STORE,
            },
        ]);
        expect(sortedCatalogMatches[0].brandProducts).toHaveLength(1);
        expect(sortedCatalogMatches[1].brandProducts).toHaveLength(2);
        expect(sortedCatalogMatches[2].brandProducts).toHaveLength(1);
    });

    it(`getCatalogsWithBrandPresence lowers the number of products present in the catalog if the products have a brand suggested link`, async () => {
        const [
            brandCatalog,
            orgCatalog,
            brandProduct,
            orgProduct,
            store1Product,
            store2Product,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Canndescent',
                ownerId  : 200,
                ownerType: BRAND,  
            }),
            Catalog.create({
                name     : 'Sparc Organization',
                ownerId  : 1000,
                ownerType: ORG,    
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndescent',
                name     : 'Calm 101',
                ownerId  : 200,
                ownerType: BRAND,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Cannde-scent',
                name     : 'Calm 102',
                ownerId  : 1000,
                ownerType: ORG,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 3.5 g',
                ownerId  : 777,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
            Product.create({
                amount   : 3.5,
                brandName: 'Canndecent',
                name     : 'Calm 101 half 8th',
                ownerId  : 999,
                ownerType: STORE,
                type     : FLOWER,
                uom      : G,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
        ] = await Promise.all([
            Catalog.create({
                name           : 'Sparc Santa Rosa',
                ownerId        : 777,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
            Catalog.create({
                name           : 'Sparc Mission',
                ownerId        : 999,
                ownerType      : STORE,
                parentCatalogId: orgCatalog.id,
            }),
        ])

        await Promise.all([
            addProductToCatalog(brandCatalog, brandProduct.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct.id, userInfo),
        ]);

        await Promise.all([
            addProductToCatalog(store1Catalog, store1Product.id, userInfo),
            addProductToCatalog(store2Catalog, store2Product.id, userInfo),
        ]);

        await Promise.all([
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: store1Product.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: store2Product.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct.id,
                type: MatchType.BrandSuggested,
            }),
        ])
    
        const catalogsThatCarryProduct = await brandCatalog.getCatalogsWithBrandPresence();

        const sortedCatalogMatches = catalogsThatCarryProduct.sort((a,b) => {
            return a.catalogName > b.catalogName ? 1 : -1
        });

        expect(sortedCatalogMatches).toMatchObject([
            {
                catalogId        : store2Catalog.id,
                catalogName      : 'Sparc Mission',
                catalogOwnerType : OwnerType.STORE,
            },
            {
                catalogId        : orgCatalog.id,
                catalogName      : 'Sparc Organization',
                catalogOwnerType : OwnerType.ORG,
            },
            {
                catalogId        : store1Catalog.id,
                catalogName      : 'Sparc Santa Rosa',
                catalogOwnerType : OwnerType.STORE,
            },
        ]);
        expect(sortedCatalogMatches[0].brandProducts).toHaveLength(1);
        expect(sortedCatalogMatches[1].brandProducts).toHaveLength(1);
        expect(sortedCatalogMatches[2].brandProducts).toHaveLength(1);
    });
});