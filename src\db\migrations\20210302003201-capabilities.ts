import {
    QueryInterface,
    Transaction
}                                       from 'sequelize';
import {
    autoPK<PERSON>ield,
    jsonField,
    notNull,
    stringField,
    createUpdateColumns,
}                                       from '@treez/dev-pack/db/fieldTypes';


const capabilities              = 'capabilities';
const id                        = 'id';
const type                      = 'type';
const subtype                   = 'subtype';
const capability                = 'capability';
const rules                     = 'rules';
const createdAt                 = 'createdAt';
const updatedAt                 = 'updatedAt';

export = {
    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(
            { isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE },
            async (transaction) => {
                await queryInterface.createTable(capabilities, {
                    [id]:                     notNull(autoPKField),
                    [type]:                   notNull(stringField),
                    [subtype]:                notNull(stringField),
                    [capability]:             notNull(stringField),
                    [rules]:                  jsonField,

                    ...createUpdateColumns
                });

                await queryInterface.sequelize.query(`
                    INSERT INTO "${capabilities}" (
                        "${type}",
                        "${subtype}",
                        "${capability}",
                        "${rules}",
                        "${createdAt}",
                        "${updatedAt}"
                    ) VALUES
                    ('FLOWER', 'BULK FLOWER', 'packaging', '{"toTypes":{"PREROLL":["FLOWER","SHAKE"],"FLOWER":["PRE-PACK","PRE-PACK SMALLS","SHAKE","STRAIN SPECIFIC SHAKE"]}}', NOW(), NOW()),
                    ('FLOWER', 'BULK FLOWER', 'deli', null, NOW(), NOW()),
                    ('EXTRACT', 'BULK EXTRACT', 'packaging', '{"toTypes":{"EXTRACT":["ISOLATE","RSO","FSO","DIAMONDS","CAVIAR","MOON ROCKS","POWDER HASH","SORBET","TEMPLE BALL","BADDER","BUDDER","CRUMBLE","CRYSTALLINE","CURED RESIN","DISTILLATE","DRY SIFT","HASH","HASH ROSIN","ICE WATER HASH","ICE WAX","JELLY","LIVE RESIN","LIVE RESIN BADDER","LIVE RESIN BUDDER","LIVE RESIN SUGAR","LIVE ROSIN","OIL","OTHER","ROSIN BUDDER","SAUCE","SHATTER","SIFT ROSIN","SUGAR","THC-A","WAX","HASH ROSIN"]}}', NOW(), NOW()),
                    ('FLOWER', 'STRAIN SPECIFIC SHAKE', 'deli', null, NOW(), NOW()),
                    ('FLOWER', 'SHAKE', 'deli', null, NOW(), NOW());
                `);
        });
    },
    down: async(queryInterface: QueryInterface) => {
        await queryInterface.dropTable("capabilities");
    }
}
