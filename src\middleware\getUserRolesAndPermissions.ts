import {
    Response,
    NextFunction,
}                                       from 'express';
import { AuthenticatedRequest }         from "@treez/dev-pack/auth";
import { getUserPermissions }           from '@treez/dev-pack/auth/permissions';

declare module 'http' {
    interface IncomingHttpHeaders {
        "x-permissions"?: string
    }
};

export const getUserRolesAndPermissions = (
    request: AuthenticatedRequest,
    response: Response,
    next: NextFunction,
) => {

    try {
        const permissions = request?.headers?.['x-permissions'] || '';
        request.userPermissions = permissions ? getUserPermissions(permissions) : [];
    } catch (error) {
        // Invalid auth header or opaque token from SellTreez.
        request.userPermissions = [];
    }

    return next();
};

export default getUserRolesAndPermissions;
