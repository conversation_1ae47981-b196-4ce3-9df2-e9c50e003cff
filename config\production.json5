{
    auth: {
        hostname: 'ssm:/app/auth/env/prod/hostname',
        verify: {
            audience: 'ssm:/app/auth/env/prod/verify/audience',
            issuer: 'ssm:/app/auth/env/prod/verify/issuer',
            keySetUrl: 'ssm:/app/auth/env/prod/verify/keyseturl',
        }
    },
    db: {
        name:      'product_prod',
        password:  'ssm:/apps/product-api/envs/prod/db/password',
        pool: {
            max: 30,
            min: 5,
            acquire: 30000,
            idle: 10000,
        },
        replication: {
            read: [
                {
                    host: 'product-db-prod-ro.treez.io',
                },
            ],
            write: {
                host: 'product-db-prod.treez.io',
            },
        },
        username:  'product_prod',
    },
    logger: {
        level: 'warn',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serverUrl:   'https://apm.prod.treez.io',
                    serviceName: 'product-api-prod',
                    transactionSampleRate: 0.2,
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        serviceUrl: 'pulsar://pulsar-broker-prod.treez.io:6650',
        topic: {
            productUpdate: 'persistent://product-api-prod/product/productUpdate'
        }
    },
    search: {
        maxPageSize: 5000,
    },
    server: {
        origin: 'https://product-api.treez.io',
    },
}
