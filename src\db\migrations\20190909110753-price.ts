import { monetaryField }                from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn('products', 'price', monetaryField);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn('products', 'price');
    },
}
