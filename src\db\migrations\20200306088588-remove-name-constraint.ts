import { QueryInterface }               from 'sequelize';
import { stringField }                  from '@treez/dev-pack/db/fieldTypes';

const DISPLAY_NAME  = "displayName";
const PRODUCTS      = "products";

export = {
    up: async function( queryInterface: QueryInterface) {
        queryInterface.changeColumn(PRODUCTS, DISPLAY_NAME, {
            ...stringField,
            allowNull: true,
        });
    },

    down: async function ( queryInterface: QueryInterface) {
        queryInterface.changeColumn(PRODUCTS, DISPLAY_NAME, {
            ...stringField,
            allowNull: false,
        });
    },
}
