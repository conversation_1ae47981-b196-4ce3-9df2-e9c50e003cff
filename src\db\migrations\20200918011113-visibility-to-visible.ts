import { QueryInterface }                from 'sequelize';
import {
    booleanField,
    stringField,
}                                        from '@treez/dev-pack/db/fieldTypes';

const PRODUCT          = 'products';

const VISIBLE          = 'visible';
const VISIBILITY       = 'visibility';    

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.addColumn(PRODUCT, VISIBLE, booleanField);
        
        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET "visible" = false
            WHERE "products"."visibility" = 'hidden';
        `);
        
        await queryInterface.removeColumn(PRODUCT, VISIBILITY);
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.addColumn(PRODUCT, VISIBILITY, stringField);
        
        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET "visibility" = CASE WHEN "products"."visible" THEN 'visible' ELSE 'hidden' END;
        `);
        
        await queryInterface.removeColumn(PRODUCT, VISIBLE);
    },
}
