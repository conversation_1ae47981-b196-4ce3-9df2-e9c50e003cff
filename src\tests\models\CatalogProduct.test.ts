import _                            from 'lodash';
import {
    OwnerType,
    UOM,
}                                   from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    RangeMode
}                                   from '@treez/commons/sharedTypings/priceTier';
import { CatalogProductAction }     from '../../lib/sharedInterfaces';
import Catalog                      from '../../models/Catalog';
import CatalogProduct, {
    addProductToCatalog,
    updateCatalogProduct,
    CatalogProductStatus,
    removeProductFromCatalog,
}                                   from '../../models/CatalogProduct';
import CatalogProductChange         from '../../models/CatalogProductChange';
import Product                      from '../../models/Product';
import PriceTier                    from '../../models/PriceTier';
import { sleep }                    from '../../tests/testHelpers/util';

const {
    ORG,
} = OwnerType;

const testUser = {
    userAuthId: '<EMAIL>',
};

describe('Catalog Product model', () => {
    it(`Records a change in a catalog product upon creation`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);

        const catalogProduct = await addProductToCatalog(orgCatalog, product.id, { userAuthId: '<EMAIL>' });

        const recordedChanges = await CatalogProductChange.findAll({
            where: {
                catalogProductId: catalogProduct.id
            }
        });

        expect(recordedChanges).toBeDefined();
        expect(recordedChanges).toHaveLength(1);

        const [catalogChange] = recordedChanges;
        expect(catalogChange.actionType).toBe(CatalogProductAction.CREATE);
        expect(catalogChange.changes).toHaveLength(0);
        expect(catalogChange.changes).toHaveLength(0);
        expect(catalogChange.userAuthId).toBe('<EMAIL>');

        expect(catalogChange.newCatalogProduct).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: null,
            status: CatalogProductStatus.ACTIVE,
        });
    });

    it(`Records a change in a catalog product upon edit with status change`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await addProductToCatalog(orgCatalog, product.id, testUser, {
            price: 25.000000,
        });

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            {
                price: 42.000000,
                status: CatalogProductStatus.DEACTIVATED,
            }
        );

        const recordedChanges = await CatalogProductChange.findAll({
            where: {
                catalogProductId: catalogProduct.id,
            },
            order: [['createdAt', 'ASC']]
        });

        expect(recordedChanges).toHaveLength(2);
        expect(recordedChanges[1].actionType).toBe(CatalogProductAction.DEACTIVATE);
        expect(recordedChanges[1].oldCatalogProduct).toMatchObject({
            price: "25.000000",
            status: CatalogProductStatus.ACTIVE,
            productId: product.id,
        });
        expect(recordedChanges[1].newCatalogProduct).toMatchObject({
            price: "42.000000",
            status: CatalogProductStatus.DEACTIVATED,
            productId: product.id,
        });

        const changes = recordedChanges[1].changes;
        expect(changes).toHaveLength(2);

        const priceChange = changes.find(c => c.property === 'price');
        expect(priceChange).toMatchObject({
            description: 'Price in catalog changed from 25 to 42',
            oldValue: '25.000000',
            newValue: '42.000000',
        });

        const statusChange = changes.find(c => c.property === 'status');
        expect(statusChange).toMatchObject({
            description: 'Product deactivated in catalog',
            oldValue: CatalogProductStatus.ACTIVE,
            newValue: CatalogProductStatus.DEACTIVATED,
        });
    });

    it(`Contextualizes a change made to the overrides`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await addProductToCatalog(orgCatalog, product.id, testUser, {
            price: 25.000000,
        });

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    details: {
                        cbdMg: 10,
                    }
                }
            }
        );

        const latestChange = await CatalogProductChange.findOne({
            where: {
                catalogProductId: catalogProduct.id,
            },
            order: [['createdAt', 'DESC']]
        }) as CatalogProductChange;

        expect(latestChange.actionType).toBe(CatalogProductAction.UPDATE);
        expect(latestChange.changes).toHaveLength(1);
        expect(latestChange.changes[0].description).toBe(`Milligrams CBD in catalog set to 10`);
    });

    it(`Records a catalog product change upon deletion`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);

        const catalogProduct = await addProductToCatalog(orgCatalog, product.id, testUser);

        await removeProductFromCatalog(orgCatalog.id, product.id, testUser);

        const recordedChanges = await CatalogProductChange.findAll({
            where: {
                catalogProductId: catalogProduct.id,
            },
            order: [['createdAt', 'ASC']],
        });

        expect(recordedChanges).toBeDefined();
        expect(recordedChanges).toHaveLength(2);

        const deletionChange = recordedChanges[1];
        expect(deletionChange.actionType).toBe(CatalogProductAction.DELETE);
        expect(deletionChange.oldCatalogProduct).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.ACTIVE,
        });
        expect(deletionChange.newCatalogProduct).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.DELETED,
        });
    });

    it(`Correctly versions changes in a catalog Product`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const catalogProduct = await addProductToCatalog(orgCatalog, product.id, testUser);

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            {
                price: 25.000000,
                status: CatalogProductStatus.DEACTIVATED
            }
        );

        await removeProductFromCatalog(
            orgCatalog.id,
            product.id,
            testUser,
        );

        const recordedChanges = await CatalogProductChange.findAll({
            where: {
                catalogProductId: catalogProduct.id,
            },
            order: [['createdAt', 'ASC']]
        });

        expect(recordedChanges).toBeDefined();
        expect(recordedChanges).toHaveLength(3);
        expect(recordedChanges[0].version).toBe(1);
        expect(recordedChanges[0].actionType).toBe(CatalogProductAction.CREATE);
        expect(recordedChanges[1].version).toBe(2);
        expect(recordedChanges[1].actionType).toBe(CatalogProductAction.DEACTIVATE);
        expect(recordedChanges[2].version).toBe(3);
        expect(recordedChanges[2].actionType).toBe(CatalogProductAction.DELETE);
    });

    it(`Trims spaces in the catalogOverrides object on create`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const catalogProduct = await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    brandName:     "   Hastings' Herb    ",
                    eCommerceName: "   Wade's Wonder Weed   ",
                }
            },
        );

        expect(_.get(catalogProduct, "catalogOverrides.brandName")).toEqual("Hastings' Herb");
        expect(_.get(catalogProduct, "catalogOverrides.eCommerceName")).toEqual("Wade's Wonder Weed");
    });

    it(`Trims spaces in the catalogOverrides object on update`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
        );

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    brandName:     "   Hastings' Herb    ",
                    eCommerceName: "   Wade's Wonder Weed   ",
                }
            }
        );

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });

        expect(_.get(catalogProduct, "catalogOverrides.brandName")).toEqual("Hastings' Herb");
        expect(_.get(catalogProduct, "catalogOverrides.eCommerceName")).toEqual("Wade's Wonder Weed");
    });

    it(`updateCatalogProduct catalogOverrides merges JSON columns`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const storeCatalog = await Catalog.create({
            name:               "Test Store Catalog",
            ownerId:            123,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    orgCatalog.id,
        });

        await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
        );

        await addProductToCatalog(
            storeCatalog,
            product.id,
            testUser,
        );

        await updateCatalogProduct(
            storeCatalog.id,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    details: {
                        cbdMg: 10,
                    }
                }
            }
        );

        await updateCatalogProduct(
            storeCatalog.id,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    details: {
                        thcMg: 20,
                    }
                }
            }
        );

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: storeCatalog.id,
                productId: product.id,
            }
        });

        expect(_.get(catalogProduct, "catalogOverrides.details.cbdMg")).toEqual(10);
        expect(_.get(catalogProduct, "catalogOverrides.details.thcMg")).toEqual(20);
    });

    it('updates a parent catalog product to inactive if all children catalogs with that product have a status of inactive', async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
            store3Catalog,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Store Catalog 1",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 2",
                ownerId:            2,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 3",
                ownerId:            3,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
        ]);

        await Promise.all([
            CatalogProduct.create({
                productId: product.id,
                catalogId: orgCatalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store1Catalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store2Catalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store3Catalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            })
        ]);

        await updateCatalogProduct(store3Catalog.id, product.id, testUser, {status: CatalogProductStatus.DEACTIVATED});

        const centralCatalogProduct = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });


        expect(centralCatalogProduct).toHaveLength(1);
        expect(_.head(centralCatalogProduct)).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.DEACTIVATED,
        });
    });

    it('updates a parent catalog product to active if one of the childrens catalogs has the product active in their catalog and it was listed as deactivated in the central catalog', async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog again",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
            store3Catalog,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Store Catalog 1",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 2",
                ownerId:            2,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 3",
                ownerId:            3,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
        ]);

        await Promise.all([
            CatalogProduct.create({
                productId: product.id,
                catalogId: orgCatalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store1Catalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store2Catalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store3Catalog.id,
                status   : CatalogProductStatus.DEACTIVATED,
                price    : 0.00,
            })
        ]);

        await updateCatalogProduct(store3Catalog.id, product.id, testUser, {status: CatalogProductStatus.ACTIVE});

        const centralCatalogProduct = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });

        expect(centralCatalogProduct).toHaveLength(1);
        expect(_.head(centralCatalogProduct)).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.ACTIVE,
        });
    });

    it(`it doesn't change the parent catalog product if just one of the children catalog products becomes inactive`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog again",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const [
            store1Catalog,
            store2Catalog,
            store3Catalog,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Store Catalog 1",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 2",
                ownerId:            2,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
            Catalog.create({
                name:               "Test Store Catalog 3",
                ownerId:            3,
                ownerType:          OwnerType.STORE,
                parentCatalogId:    orgCatalog.id,
            }),
        ]);

        await Promise.all([
            CatalogProduct.create({
                productId: product.id,
                catalogId: orgCatalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store1Catalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store2Catalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            }),
            CatalogProduct.create({
                productId: product.id,
                catalogId: store3Catalog.id,
                status   : CatalogProductStatus.ACTIVE,
                price    : 0.00,
            })
        ]);

        await updateCatalogProduct(store3Catalog.id, product.id, testUser, {status: CatalogProductStatus.DEACTIVATED});

        const centralCatalogProduct = await CatalogProduct.findAll({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });

        expect(centralCatalogProduct).toHaveLength(1);
        expect(_.head(centralCatalogProduct)).toMatchObject({
            catalogId: orgCatalog.id,
            productId: product.id,
            status: CatalogProductStatus.ACTIVE,
        });
    });

    it(`updateCatalogProduct skips update when there are no real changes`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const originalCatalogProduct = await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
            { price: 42.000000 },
        );
        const originalTimestamp = JSON.stringify(originalCatalogProduct.updatedAt);

        await sleep(1); // 1ms

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            { price: 42.000000 },
        );

        const updatedCatalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });
        const updatedTimestamp = JSON.stringify(updatedCatalogProduct?.updatedAt);

        expect(updatedTimestamp).toBeDefined();
        expect(updatedTimestamp).toBe(originalTimestamp);
    });

    it(`updateCatalogProduct skips update when catalog override changes nil with empty`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            123,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const originalCatalogProduct = await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    eCommerceName: 'We Shall Remain',
                    details: undefined,
                }
            },
        );
        const originalTimestamp = JSON.stringify(originalCatalogProduct.updatedAt);

        await sleep(1); // 1ms

        await updateCatalogProduct(
            orgCatalog.id,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    details: {},
                },
            },
        );

        const updatedCatalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: orgCatalog.id,
                productId: product.id,
            }
        });
        const updatedTimestamp = JSON.stringify(updatedCatalogProduct?.updatedAt);

        expect(updatedTimestamp).toBeDefined();
        expect(updatedTimestamp).toBe(originalTimestamp);
    });

    it(`can tell catalog products are identical with isEqual`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);

        const catalogProduct = await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    eCommerceName: 'We Shall Remain',
                    details: undefined,
                }
            },
        );

        expect(catalogProduct.isEqual(catalogProduct)).toBe(true);
    });

    it(`can tell catalog products are different with isEqual`, async () => {
        const [
            product,
            orgCatalog,
            brandCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Brand Catalog",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
            }),
        ]);

        const orgProduct = await addProductToCatalog(
            orgCatalog,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    eCommerceName: 'We Shall Remain',
                    details: undefined,
                }
            },
        );

        const brandProduct = await addProductToCatalog(
            brandCatalog,
            product.id,
            testUser,
            {
                catalogOverrides: {
                    eCommerceName: 'We Shall Remain',
                    details: undefined,
                }
            },
        );

        expect(orgProduct.isEqual(brandProduct)).toBe(false);
    });

    it(`isEqual correctly determines equality with instance cloned using spread operator`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await CatalogProduct.build({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {
                eCommerceName: 'We Shall Remain',
                details: undefined,
            },
        }).save();

        const clonedCatalogProduct = new CatalogProduct({
            ...catalogProduct.get(),
            price: '24.00',
        });

        expect(catalogProduct.isEqual(clonedCatalogProduct)).toBe(true);
    });

    it(`isEqual correctly detects null and empty objects as different`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await CatalogProduct.build({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        const clonedCatalogProduct = new CatalogProduct({
            ...catalogProduct.get(),
            catalogOverrides: null,
        });

        // this is only to make sure we're comparing null with empty object and the test has value
        expect(catalogProduct.catalogOverrides).toMatchObject({});
        expect(clonedCatalogProduct.catalogOverrides).toBeNull();

        expect(catalogProduct.isEqual(clonedCatalogProduct)).toBe(false);
    });

    it(`isEqual correctly detects null and empty objects as equal`, async () => {
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await CatalogProduct.build({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        const clonedCatalogProduct = new CatalogProduct({
            ...catalogProduct.get(),
            catalogOverrides: null,
        });

        // this is only to make sure we're comparing null with empty object and the test has value
        expect(catalogProduct.catalogOverrides).toMatchObject({});
        expect(clonedCatalogProduct.catalogOverrides).toBeNull();

        expect(catalogProduct.isEqual(clonedCatalogProduct, true)).toBe(true);
    });

    it(`addProductToCatalog properly saves priceTierId`, async () => {
        const [
            product,
            catalog,
            priceTier,
        ] = await Promise.all([
            Product.create({
                amount:             "15",
                brandName:          "TERPENSTEIN",
                classification:     "CBD",
                displayName:        "FLOWERIUKAS",
                eCommerceName:      "FLOWERIUKAS",
                name:               "FLOWERIUKAS",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "Dime Baggie",
                subtype:            "BAG",
                type:               "FLOWER",
                uom:                UOM.G,
                visible:            true,
                cannabis:           true,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            PriceTier.create({
                label: "First Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.UNIT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                }],
            })
        ]);

        const productToAddToCatalog = {
            ...product,
            price: priceTier.thresholds[0].value,
            priceTierId: priceTier.id,
            status: CatalogProductStatus.ACTIVE,
            ownerId: catalog.ownerId,
            ownerType: catalog.ownerType
        };

        const catalogProduct = await addProductToCatalog(
            catalog,
            product.id,
            testUser,
            {
                price:  productToAddToCatalog.price,
                status: productToAddToCatalog.status || CatalogProductStatus.ACTIVE,
                priceTierId: productToAddToCatalog.priceTierId,
            },
        );

        //parseFloat for trailing zeros drop
        expect(parseFloat(catalogProduct.price.toString())).toEqual(priceTier.thresholds[0].value);
        expect(catalogProduct.priceTierId).toEqual(priceTier.id);
    });

});
