import {
    Response,
    Router,
}                                           from 'express';
import { StatusCodes }                      from 'http-status-codes';
import _                                    from 'lodash';
import { AuthenticatedRequest }             from '@treez/dev-pack/auth';
import {
    connection,
    transaction,
}                                          from '@treez/dev-pack/db';
import {
    sendErrorResponse,
    ErrorResponse,
}                                           from '@treez/dev-pack/errors';
import logger                               from '@treez/dev-pack/logger';
import { requestorIsTreez }                 from '../middleware';
import Catalog, {
    findCatalogById,
}                                           from '../models/Catalog';
import PriceTier, {
    validatePriceTier
}                                           from '../models/PriceTier';
import { ExternalReferenceType }            from '../models/ExternalReference';
import CatalogProduct                       from '../models/CatalogProduct';
import CatalogPriceTier                     from '../models/CatalogPriceTier';

const route = Router();
export interface MigratePriceTierPayload {
    priceTier : PriceTier,
    productIds: string[],
}

route.post(`/catalogs/:catalogId/pricetier`,
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
    const {
        catalogId,
    } = request.params;

    const migratePriceTierPayloads = request.body as MigratePriceTierPayload[];

    try {
        const catalog : Catalog = await findCatalogById(catalogId);
        let parentCatalog : Catalog;
        if (catalog.parentCatalogId) {
            parentCatalog = await findCatalogById(catalog.parentCatalogId);
        }

        const priceTiers : PriceTier[] = await transaction(async() => {
            const createdPriceTiers : PriceTier[] = await Promise.all(
                migratePriceTierPayloads.map(async (migratePriceTierPayload) => {

                    const {
                        priceTier: priceTierRequest,
                        productIds: sellTreezIdsAssignedToTier,
                    } = migratePriceTierPayload;

                    const priceTier : Partial<PriceTier> = {
                        ...priceTierRequest,
                        ownerId  : catalog.ownerId,
                        ownerType: catalog.ownerType,
                    }

                    validatePriceTier(priceTier);

                    const createdPriceTier : PriceTier = await PriceTier.create(priceTier);

                    await CatalogPriceTier.create({
                        catalogId  : catalog.id,
                        priceTierId: createdPriceTier.id,
                    })

                    if (parentCatalog) {
                        await CatalogPriceTier.create({
                            catalogId  : parentCatalog.id,
                            priceTierId: createdPriceTier.id,
                        })
                    }

                    if (!_.isEmpty(sellTreezIdsAssignedToTier)) {
                        const catalogProducts : CatalogProduct[] = await connection.query(
                            `SELECT "catalogProducts".*
                                FROM "catalogProducts"
                                JOIN "externalReferences"
                                    ON "externalReferences"."productId" = "catalogProducts"."productId"
                                    AND "externalReferences"."type" = :type
                                WHERE "catalogProducts"."catalogId" = :catalogId
                                    AND "externalReferences"."externalId" IN (:sellTreezIds);
                            `,
                            {
                                model: CatalogProduct,
                                replacements: {
                                    catalogId   : catalog.id,
                                    sellTreezIds: sellTreezIdsAssignedToTier,
                                    type        : ExternalReferenceType.SELL_TREEZ_ID,
                                }
                            },
                        );

                        if (!_.isEmpty(catalogProducts)) {
                            await Promise.all(
                                catalogProducts.map(async catalogProduct => {
                                    return await catalogProduct.update({
                                        priceTierId: createdPriceTier.id
                                    })
                                })
                            )
                        }
                    }

                    return createdPriceTier;
                })
            );

            if (migratePriceTierPayloads.length !== createdPriceTiers.length) {
                throw new ErrorResponse(
                    StatusCodes.CONFLICT,
                    `Number of PriceTiers (${migratePriceTierPayloads.length}) requested for migrate does not equal the number of PriceTiers created (${createdPriceTiers.length}).`
                )
            }

            return createdPriceTiers;
        })

        return response.status(StatusCodes.CREATED).json(priceTiers);
    }
    catch (error) {
        logger.error(`Error bulk importing PriceTier data into ProductAPI`, error);
        return sendErrorResponse(response, error);
    }
});

export default route;
