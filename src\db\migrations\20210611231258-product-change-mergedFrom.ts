import { QueryInterface }               from 'sequelize';
import { jsonField }                    from '@treez/dev-pack/db/fieldTypes';

const productChanges    = 'productChanges';
const mergedFrom        = 'mergedFrom';

export = {
    up: async function( queryInterface: QueryInterface ) {
        await queryInterface.addColumn(productChanges, mergedFrom, jsonField);
    },
    down: async function( queryInterface: QueryInterface ) {
        await queryInterface.removeColumn(productChanges, mergedFrom);
    }
};
