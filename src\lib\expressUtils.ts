import {
    ReasonPhrases,
    StatusCodes,
}                                   from 'http-status-codes';
import _                            from 'lodash';
import { AuthenticatedRequest }     from '@treez/dev-pack/auth';
import { ErrorResponse }            from '@treez/dev-pack/errors';


export const getUserInfo = (request: AuthenticatedRequest): UserInfo => {
    const { requestor } = request;
    const authId = requestor?.id ?? request.headers?.userauthid as string ??  'treez';
    const stUserId = request.headers['st-userid'] as string | undefined

    return {
        userAuthId: authId,
        sellTreezUserId: stUserId,
    };
}

export const getValueFromRequestBody = <T> (
    bodyKey: string,
    request: AuthenticatedRequest,
    defaultValue: T,
): T => request.body[bodyKey] ?? defaultValue;

export const getIdsFromRequestQuery = (
    paramKey: string,
    request: AuthenticatedRequest,
): number[] => {
    const pathValue = request.query[paramKey] as string | undefined;

    return pathValue ? pathValue.split(',').map(Number) : [];
}

export const getIdFromRequestPath = (
    pathKey: string,
    request: AuthenticatedRequest,
): number => {
    const pathValue = request.params[pathKey]

    const value = Number(pathValue);

    if (Number.isNaN(value)) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, ReasonPhrases.BAD_REQUEST)
    }

    return value;
}
