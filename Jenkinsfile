#!/usr/bin/env groovy

APP_NAME = "product-api"
APP_PORT = 8303 // must match port in config and Dockerfile

REGISTRY = "228276746220.dkr.ecr.us-west-2.amazonaws.com"

podTemplate(
    containers: [
        containerTemplate(name: "aws",      image: "amazon/aws-cli:2.0.30", ttyEnabled: true, command: "cat"),
        containerTemplate(name: "docker",   image: "docker:latest",         ttyEnabled: true, command: "cat"),
        containerTemplate(name: "node",     image: "node:12-alpine3.12",    ttyEnabled: true, command: "cat"),
        containerTemplate(name: "postgres", image: "circleci/postgres:10.6-alpine-ram", alwaysPullImage: true, envVars: [
            envVar(key: "POSTGRES_DB",   value: "testing"),
            envVar(key: "POSTGRES_USER", value: "postgres")
        ])
    ],
    nodeSelector: "node=jenkins-agents",
    volumes: [
        hostPathVolume(hostPath: "/var/run/docker.sock", mountPath: "/var/run/docker.sock")
    ]
) {
    node(POD_LABEL) {

        stage("Survey") {
            sh "env | sort"
        }

        stage("Checkout") {
            checkout scm
        }

        def branch = env.BRANCH_NAME

        if (branch in [ "master", "pilot", "demo", "qa", "auth0" ]) {

            def cleanBranch     = branch.replaceAll("[^A-Za-z0-9]", "_")
            def commitHash      = (sh(script: "git rev-parse HEAD", returnStdout: true)).trim()
            def today           = (new Date()).format("yyyyMMdd")

            def dockerTag = "${cleanBranch}-${today}-${env.BUILD_NUMBER}-${commitHash}-${APP_PORT}"
            def dockerUri = "${REGISTRY}/${APP_NAME}:${dockerTag}"

            stage("Docker Image") {

                def ecrPassword = ""

                container("aws") {
                    ecrPassword = sh(script: "aws --region us-west-2 ecr get-login-password", returnStdout: true)
                }

                container("docker") {
                    withCredentials([string(variable: "npmToken", credentialsId: "npmToken")]) {
                        sh """
                            docker build .                           \
                                --network   host                     \
                                --tag       "${dockerUri}"           \
                                --build-arg "npmToken=${npmToken}"   \
                        """
                    }

                    sh """
                        set +x
                        echo '${ecrPassword}' | docker login --username AWS --password-stdin ${REGISTRY}
                        set -x

                        docker push ${dockerUri}
                    """
                }

            }

            stage("Deploy to CI") {

                def commitEmail = sh(script: "git log -n 1 --format=%ae ${commitHash}", returnStdout: true)
                def commitUser  = sh(script: "git log -n 1 --format=%an ${commitHash}", returnStdout: true)

                build job: "/deploy/deploy-api", parameters: [
                    string(name: "COMMIT_EMAIL", value: commitEmail),
                    string(name: "COMMIT_USER",  value: commitUser),
                    string(name: "ENVIRONMENT",  value: (branch == "master") ? "ci" : branch),
                    string(name: "IMAGE",        value: dockerUri)
                ]
            }

        }

    }
}
