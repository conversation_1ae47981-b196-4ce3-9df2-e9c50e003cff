import { QueryInterface }   from 'sequelize';
import { stringField}       from '@treez/dev-pack/db/fieldTypes';


const catalogProducts                     = 'catalogProducts';
const externalId                          = "externalId";

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.removeColumn(catalogProducts, externalId);
    },

    down: async function(queryInterface: QueryInterface) {
        await queryInterface.addColumn(catalogProducts, externalId, stringField);
    },
};
