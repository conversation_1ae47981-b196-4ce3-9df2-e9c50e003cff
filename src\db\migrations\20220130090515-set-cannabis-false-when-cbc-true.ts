import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const PRODUCTS = 'products';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.bulkUpdate(PRODUCTS, { cannabis: false, }, { cannabis: true, type:'CBD' },);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
    },
}
