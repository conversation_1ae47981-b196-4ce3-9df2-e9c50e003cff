import _                                    from 'lodash';
import { StatusCodes }                      from 'http-status-codes';
import supertest                            from 'supertest';
import {
    OwnerType,
}                                           from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode
}                                           from '@treez/commons/sharedTypings/priceTier';
import Catalog                              from '../../models/Catalog';
import CatalogProduct                       from '../../models/CatalogProduct';
import CatalogPriceTier                     from '../../models/CatalogPriceTier';
import PriceTier                            from '../../models/PriceTier';
import Product                              from '../../models/Product';
import { addProductToCatalog }              from '../../models/CatalogProduct';
import { ExternalReferenceType }            from '../../models/ExternalReference';
import {mockTreezAccess}                    from '../../tests/testHelpers/util';
import {
    MigratePriceTierPayload_MOCK_SINGLE,
    MigratePriceTierPayloads_MOCK_MULTIPLE
}                                           from '../testHelpers/data';
import app                                  from './testApp';

const testUser = {userAuthId: '<EMAIL>'};

describe('import endpoints', () => {
    beforeEach(() => {
        mockTreezAccess()
    });

    it(`Imports multiple PriceTiers for a catalog ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const catalog = await Catalog.create({
            name     : "store catalog",
            ownerId  : 1,
            ownerType: OwnerType.STORE,
        });

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayloads_MOCK_MULTIPLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(10);
        expect(priceTiers).toHaveLength(10);
    })

    it(`Imports PriceTiers for a catalog and fills in PriceTier correctly ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const catalog = await Catalog.create({
            name     : "store catalog",
            ownerId  : 1,
            ownerType: OwnerType.STORE,
        });

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayload_MOCK_SINGLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(1);
        expect(priceTiers).toHaveLength(1);
        expect(priceTiers[0].label).toBe("60");
        expect(priceTiers[0].ownerId).toBe(catalog.ownerId);
        expect(priceTiers[0].ownerType).toBe(catalog.ownerType);
        expect(priceTiers[0].isActive).toBe(true);
        expect(priceTiers[0].method).toBe(PriceTierMethod.WEIGHT);
        expect(priceTiers[0].thresholdType).toBe(PriceTierThresholdType.FLAT);
        expect(priceTiers[0].rangeMode).toBe(RangeMode.RANGE);
        expect(priceTiers[0].thresholds).toEqual(
            [
                {
                    "value": 16.475973,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.475973,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
            ]
        );
    });

    it(`Imports multiple PriceTiers for a catalog and create CatalogPriceTier for each ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const catalog = await Catalog.create({
            name     : "store catalog",
            ownerId  : 1,
            ownerType: OwnerType.STORE,
        });

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayloads_MOCK_MULTIPLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        const catalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: catalog.id,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(10);
        expect(priceTiers).toHaveLength(10);
        expect(catalogPriceTiers).toHaveLength(10);
    })

    it(`Imports multiple PriceTiers for a catalog and creates CatalogPriceTier for parent catalog ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const parentCatalog = await Catalog.create({
            name     : "parent catalog",
            ownerId  : 1,
            ownerType: OwnerType.ORG,
        });

        const catalog = await Catalog.create({
            name           : "store catalog",
            ownerId        : 1,
            ownerType      : OwnerType.STORE,
            parentCatalogId: parentCatalog.id,
        });

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayloads_MOCK_MULTIPLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        const parentCatalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: parentCatalog.id,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(10);
        expect(priceTiers).toHaveLength(10);
        expect(parentCatalogPriceTiers).toHaveLength(10);
    })

    it(`Imports PriceTiers for a catalog and fills in CatalogPriceTier correctly ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const catalog = await Catalog.create({
            name     : "store catalog",
            ownerId  : 1,
            ownerType: OwnerType.STORE,
        });

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayload_MOCK_SINGLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        const catalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: catalog.id,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toHaveLength(1);
        expect(priceTiers).toHaveLength(1);
        expect(catalogPriceTiers).toHaveLength(1);

        expect(catalogPriceTiers[0].catalogId).toBe(catalog.id);
        expect(catalogPriceTiers[0].priceTierId).toBe(priceTiers[0].id);
    })

    it(`Imports PriceTiers for a catalog and assigns existing CatalogProducts its tier_id ---> POST request to /catalogs/:catalogId/pricetier `, async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store Product 1',
                name         : 'Store Product 1',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Catalog.create({
                name     : "store catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
        ]);

        await Promise.all([
            addProductToCatalog(catalog, product.id, testUser),
            product.addExternalReference(
                ExternalReferenceType.SELL_TREEZ_ID,
                MigratePriceTierPayload_MOCK_SINGLE[0].productIds[0]
            )
        ]);

        const response = await supertest(app)
            .post(`/imports/catalogs/${catalog.id}/pricetier`)
            .send(MigratePriceTierPayload_MOCK_SINGLE);

        const priceTiers = await PriceTier.findAll({
            where: {
                ownerId: catalog.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        const catalogProducts = await CatalogProduct.findAll({
            where: {
                catalogId: catalog.id,
            }
        });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(priceTiers).toHaveLength(1);
        expect(catalogProducts).toHaveLength(1);
        expect(catalogProducts[0].priceTierId).toBe(priceTiers[0].id);
    });
});
