import { StatusCodes }          from 'http-status-codes';
import Sequelize                from 'sequelize';
import _                        from 'lodash';
import { ErrorResponse }        from '@treez/dev-pack/errors';
import { OwnerType }            from '@treez/commons/sharedTypings/product';
import Product                  from '../models/Product';
import ProductUpdateOutbox      from '../models/ProductUpdateOutbox';
import ExternalReference, {
    ExternalReferenceType
}                               from '../models/ExternalReference';
import CatalogProduct           from '../models/CatalogProduct';
import { TypeOfProductChanges } from '../pulsar/producer';

export const performExternalReferenceMergeCleanup = async (productIds : number[]) => {
    const products : Product[] = await Product.findAll({
        where: {
            id: productIds,
        }
    });

    validateProductsFound(products, productIds);

    return createMergeProductUpdateOutboxes(products);
}

const validateProductsFound = (products : Product[], productIds : number[]) => {
    if (productIds.length !== products.length) {
        const productIdsFound = _.map(products, (p) => p.id);

        const productIdsNotFound = _.without(productIds, ...productIdsFound);

        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Product with the id(s) of ${productIdsNotFound} could not be found`
        )
    }
}

export const createMergeProductUpdateOutboxes = async (products : Product[]) => {
    return await Promise.all(
        products.map(product => createMergeProductUpdateOutbox(product))
    );
}

export const createMergeProductUpdateOutbox = async (product : Product) => {

    const activeSellTreezId = await product.getSellTreezId();

    if (!activeSellTreezId) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `SellTreezId not found for product: ${product.id}`
        );
    }

    const affectedSellTreezIds = await getAffectedSellTreezIds(product.id, product.ownerType, activeSellTreezId);

    const affectedCatalogIds = await getAffectedCatalogIds(product.id);

    const payload = {
        changedProductId: product.id,
        affectedCatalogIds,
        affectedSellTreezIds,
        changeType: TypeOfProductChanges.MERGE,
    }

    const productUpdateOutbox = await ProductUpdateOutbox.create({
        payload,
        published: false,
    })

    return productUpdateOutbox;
}

/*
    externalReferenceStyleMerge: products merged update their externalReferences to point to new product
    mergeToStyleMerge          : products merged set their mergedTo to the new product
*/
const getAffectedSellTreezIds = async (productId : number, ownerType: OwnerType, activeSellTreezId : string) => {
    const mergedToStyleMergeProductIds = await ProductUpdateOutbox.getAffectedProductIds(productId, ownerType, TypeOfProductChanges.MERGE);

    return _.chain(await ExternalReference.findAll({
        where: {
            productId: {
                [Sequelize.Op.or]: [productId, ...mergedToStyleMergeProductIds]
            },
            externalId: {
                [Sequelize.Op.not]: activeSellTreezId,
            },
            type: ExternalReferenceType.SELL_TREEZ_ID,
        },
        attributes: ['externalId'],
    }))
    .map('externalId')
    .uniq()
    .value();
}

const getAffectedCatalogIds = async (productId : number) => {
    return _.chain(await CatalogProduct.findAll({
        where: {
            productId,
        },
        attributes: ['catalogId']
    }))
    .map('catalogId')
    .uniq()
    .value();
}
