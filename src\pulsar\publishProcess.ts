import { initDb }                   from '@treez/dev-pack/db';
import config                       from 'config';
import ProductUpdateOutbox          from '../models/ProductUpdateOutbox';

const scheduleCheckOnUnpublishRecords = async () => {

    await ProductUpdateOutbox.publishUnpublishedMessages();

    setTimeout(scheduleCheckOnUnpublishRecords, config.pulsar.unpublishedMessagesDelay);
}

(async () => {
    await initDb();

    scheduleCheckOnUnpublishRecords();
})();
