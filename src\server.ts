import {
    bootstrapApp,
    createApp,
}                                   from '@treez/dev-pack/app';
import { setSSM }                   from '@treez/dev-pack/aws';
import { initDb }                   from '@treez/dev-pack/db';
import getMajorVersionPrefix        from '@treez/dev-pack/lib/versionUtils';
import { authenticateRequest }      from '@treez/dev-pack/middleware/authenticateRequest';
import config                       from 'config';
import apm                          from 'elastic-apm-node';
import sourceMapSupport             from 'source-map-support';
import swaggerUi                    from 'swagger-ui-express';
import swaggerSpec                  from './openapi.json';
import routes                       from './routes';
import {
    getUserRolesAndPermissions,
    middlewareErrorHandler,
}                                   from './middleware';
import { initPulsar }               from "./pulsar/producer";

(async () => {
    sourceMapSupport.install();

    const hydratedConfig = await setSSM(config);
    Object.assign(config, hydratedConfig);

    if (config?.monitoring?.elastic?.apm?.enabled) {
        const apmConfig = config?.monitoring?.elastic?.apm?.config;

        if (apmConfig != null) {
            apm.start(apmConfig);
        }
    }

    await initDb();
    await initPulsar();

    const app = await createApp();

    app.use(authenticateRequest);
    app.use(getUserRolesAndPermissions);
    app.use(routes);
    app.use(middlewareErrorHandler);

    await bootstrapApp(app);
    app.use(`/${getMajorVersionPrefix(process.env.npm_package_version)}/public/docs`, swaggerUi.serve, swaggerUi.setup(swaggerSpec))

})();