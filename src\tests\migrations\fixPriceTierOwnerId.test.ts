import PriceTier from "../../models/PriceTier";
import Catalog from "../../models/Catalog";
import CatalogPriceTier from "../../models/CatalogPriceTier";
import { connection } from "@treez/dev-pack/db";

describe(`Fix PriceTier ownerId bug`, () => {
    it('should only update price tiers with ownerType store and an ownerId that wrongly matches the catalogId', async () => {
        const thresholds = [
            {
                value: 4,
                start: 1,
                end: 3.5
            },
            {
                value: 3,
                start: 3.5,
                end: 5
            },
            {
                value: 1.5,
                start: 5,
            }
        ];

        const [
            catalog1,
            catalog2,
            priceTier1,
            priceTier2,
            priceTier3,
            priceTier4,
        ] = await Promise.all([
            Catalog.create({
                name: 'Catalog 1',
                ownerId: 101,
                ownerType: 'store',
            }),
            Catalog.create({
                name: 'Catalog 2',
                ownerId: 201,
                ownerType: 'store',
            }),
            PriceTier.create({
                name: 'Price Tier 1',
                ownerId: 101,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 1',
            }),
            PriceTier.create({
                name: 'Price Tier 2',
                ownerId: 201,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 2',
            }),
            PriceTier.create({
                name: 'Price Tier 3',
                ownerId: 101,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 3',
            }),
            PriceTier.create({
                name: 'Price Tier 4',
                ownerId: 201,
                ownerType: 'store',
                method: 'fixed',
                thresholds,
                label: 'price tier 4',
            }),
        ]);

        await Promise.all([
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier1.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier2.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier3.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier4.id,
            }),
        ])

        const [
            invalidPriceTier1,
            invalidPriceTier2,
        ] = await Promise.all([
            priceTier1.update({ownerId: catalog1.id}),
            priceTier2.update({ownerId: catalog2.id}),
        ]);

        //the records successfully save with curropted ownerId
        expect(invalidPriceTier1.ownerId).toEqual(catalog1.id);
        expect(invalidPriceTier2.ownerId).toEqual(catalog2.id);

        const result = await connection.query(`
            UPDATE "priceTiers" pt
            SET "ownerId" = c."ownerId"
            FROM "catalogPriceTiers" cpt
            JOIN "catalogs" c ON c.id = cpt."catalogId"
            WHERE pt.id = cpt."priceTierId"
            AND pt."ownerId" <> c."ownerId"
            AND c.id = pt."ownerId";
        `) as [[], {
            command: string,
            rowCount: number,
        }];

        //it updates only those two records. 
        expect(result).toHaveLength(2);
        expect(result[1]).toBeDefined();
        expect(result[1].rowCount).toEqual(2);

        const [
            updatedInvalidPriceTier1,
            updatedInvalidPriceTier2
        ] = await Promise.all([
            invalidPriceTier1.reload(),
            invalidPriceTier2.reload(),
        ]);

        //the records are updated with the correct ownerId
        expect(updatedInvalidPriceTier1.ownerId).toEqual(101);
        expect(updatedInvalidPriceTier2.ownerId).toEqual(201);
        expect(priceTier3.ownerId).toEqual(101);
        expect(priceTier4.ownerId).toEqual(201);
    });

    it('does not update price tiers shared from one store to another store in a larger organization', async () => {
        const thresholds = [
            {
                value: 4,
                start: 1,
                end: 3.5
            },
            {
                value: 3,
                start: 3.5,
                end: 5
            },
            {
                value: 1.5,
                start: 5,
            }
        ];

        const [
            catalog1,
            catalog2,
            priceTier1,
            priceTier2,
            priceTier3,
            priceTier4,
        ] = await Promise.all([
            Catalog.create({
                name: 'Catalog 1',
                ownerId: 101,
                ownerType: 'store',
            }),
            Catalog.create({
                name: 'Catalog 2',
                ownerId: 201,
                ownerType: 'store',
            }),
            PriceTier.create({
                name: 'Price Tier 1',
                ownerId: 101,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 1',
            }),
            PriceTier.create({
                name: 'Price Tier 2',
                ownerId: 201,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 2',
            }),
            PriceTier.create({
                name: 'Price Tier 3',
                ownerId: 101,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 3',
            }),
            PriceTier.create({
                name: 'Price Tier 4',
                ownerId: 201,
                ownerType: 'store',
                method: 'fixed',
                thresholds,
                label: 'price tier 4',
            }),
        ]);

        await Promise.all([
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier1.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier2.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier3.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier4.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier1.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier2.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier3.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier4.id,
            }),
        ])

        const [
            invalidPriceTier1,
            invalidPriceTier2,
        ] = await Promise.all([
            priceTier1.update({ownerId: catalog1.id}),
            priceTier2.update({ownerId: catalog2.id}),
        ]);

        //the records successfully save with curropted ownerId
        expect(invalidPriceTier1.ownerId).toEqual(catalog1.id);
        expect(invalidPriceTier2.ownerId).toEqual(catalog2.id);

        const result = await connection.query(`
            UPDATE "priceTiers" pt
            SET "ownerId" = c."ownerId"
            FROM "catalogPriceTiers" cpt
            JOIN "catalogs" c ON c.id = cpt."catalogId"
            WHERE pt.id = cpt."priceTierId"
            AND pt."ownerId" <> c."ownerId"
            AND c.id = pt."ownerId";
        `) as [[], {
            command: string,
            rowCount: number,
        }];

        //it updates only those two records. 
        expect(result).toHaveLength(2);
        expect(result[1]).toBeDefined();
        expect(result[1].rowCount).toEqual(2);

        const [
            updatedInvalidPriceTier1,
            updatedInvalidPriceTier2
        ] = await Promise.all([
            invalidPriceTier1.reload(),
            invalidPriceTier2.reload(),
        ]);

        //the records are updated with the correct ownerId
        expect(updatedInvalidPriceTier1.ownerId).toEqual(101);
        expect(updatedInvalidPriceTier2.ownerId).toEqual(201);
    });

    it('does not update price tiers shared from an organization to its children stores', async () => {
        const thresholds = [
            {
                value: 4,
                start: 1,
                end: 3.5
            },
            {
                value: 3,
                start: 3.5,
                end: 5
            },
            {
                value: 1.5,
                start: 5,
            }
        ];

        const orgCatalog = await Catalog.create({
            name: 'Org Catalog',
            ownerId: 3003,
            ownerType: 'organization',
        });
    
        const [
            catalog1,
            catalog2,
            priceTier1,
            priceTier2,
            priceTier3,
            priceTier4,
        ] = await Promise.all([
            Catalog.create({
                name: 'Catalog 1',
                ownerId: 101,
                ownerType: 'store',
                parentCatalogId: orgCatalog.id,
            }),
            Catalog.create({
                name: 'Catalog 2',
                ownerId: 201,
                ownerType: 'store',
                parentCatalogId: orgCatalog.id,
            }),
            PriceTier.create({
                name: 'Price Tier 1',
                ownerId: 101,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 1',
            }),
            PriceTier.create({
                name: 'Price Tier 2',
                ownerId: 201,
                ownerType: 'store',
                thresholds,
                method: 'fixed',
                label: 'price tier 2',
            }),
            PriceTier.create({
                name: 'Price Tier 3',
                ownerId: 3003,
                ownerType: 'organization',
                thresholds,
                method: 'fixed',
                label: 'price tier 3',
            }),
            PriceTier.create({
                name: 'Price Tier 4',
                ownerId: 3003,
                ownerType: 'organization',
                method: 'fixed',
                thresholds,
                label: 'price tier 4',
            }),
        ]);

        await Promise.all([
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier1.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier2.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier3.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier4.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier1.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier2.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog2.id,
                priceTierId: priceTier3.id,
            }),
            CatalogPriceTier.create({
                catalogId: catalog1.id,
                priceTierId: priceTier4.id,
            }),
        ])

        const [
            invalidPriceTier1,
            invalidPriceTier2,
        ] = await Promise.all([
            priceTier1.update({ownerId: catalog1.id}),
            priceTier2.update({ownerId: catalog2.id}),
        ]);

        //the records successfully save with curropted ownerId
        expect(invalidPriceTier1.ownerId).toEqual(catalog1.id);
        expect(invalidPriceTier2.ownerId).toEqual(catalog2.id);

        const result = await connection.query(`
            UPDATE "priceTiers" pt
            SET "ownerId" = c."ownerId"
            FROM "catalogPriceTiers" cpt
            JOIN "catalogs" c ON c.id = cpt."catalogId"
            WHERE pt.id = cpt."priceTierId"
            AND pt."ownerId" <> c."ownerId"
            AND c.id = pt."ownerId";
        `) as [[], {
            command: string,
            rowCount: number,
        }];

        //it updates only those two records. 
        expect(result).toHaveLength(2);
        expect(result[1]).toBeDefined();
        expect(result[1].rowCount).toEqual(2);

        const [
            updatedInvalidPriceTier1,
            updatedInvalidPriceTier2
        ] = await Promise.all([
            invalidPriceTier1.reload(),
            invalidPriceTier2.reload(),
        ]);

        //the records are updated with the correct ownerId
        expect(updatedInvalidPriceTier1.ownerId).toEqual(101);
        expect(updatedInvalidPriceTier2.ownerId).toEqual(201);
    })
});