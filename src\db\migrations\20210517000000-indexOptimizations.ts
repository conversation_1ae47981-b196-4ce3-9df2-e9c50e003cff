import {
    QueryInterface,
} from 'sequelize';

const mergedIndex = 'products_merged_idx';
const brandOwnerIndex = 'products_brandOwner_idx';

export = {

    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.query(`
        CREATE INDEX ${mergedIndex} ON "products" ("mergedTo") WHERE "mergedTo" IS NOT NULL;

        CREATE INDEX ${brandOwnerIndex} ON "products" ("ownerType") WHERE "ownerType" = 'brand';
        `);
    },

    down: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.query(`
        DROP INDEX ${mergedIndex};
        DROP INDEX ${brandOwnerIndex};
        `);
    },
};
