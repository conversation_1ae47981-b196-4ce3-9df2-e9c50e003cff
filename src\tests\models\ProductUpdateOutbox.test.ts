import _                        from 'lodash';
import {
    CatalogProductStatus,
    OwnerType,
    ProductType,
    UOM,
}                               from '@treez/commons/sharedTypings/product';
import Catalog                  from '../../models/Catalog';
import { addProductToCatalog }  from '../../models/CatalogProduct';
import Product, {
    updateProductInCatalog
}                               from '../../models/Product';
import ProductUpdateOutbox      from '../../models/ProductUpdateOutbox';
import * as PublishToPulsar     from '../../pulsar/producer';

const testUser = {
    userAuthId: '<EMAIL>',
};

describe('Product Update Outbox model', () => {

    const mockedPublishCalls = jest.spyOn(PublishToPulsar, 'sendProductUpdateMessage').mockImplementation(
        (payload, messageId) => {
            return Promise.resolve();
        }
    );

    afterEach(() => {
        jest.clearAllMocks();
    })


    it('Creates a record on product update', async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             1.000000,
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:      "ZOMG",
                externalId:         "8675309",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                packageTracked:     false,
                cannabis:           true,
                msrp:               25.000000,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ])

        await addProductToCatalog(catalog, product.id, testUser);

        await updateProductInCatalog(
            catalog.id,
            product.id,
            {
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            },
            testUser,
        );

        const outbox = await ProductUpdateOutbox.findAll();

        expect(outbox).toHaveLength(1);
        expect(_.head(outbox)).toMatchObject({
            id: 1,
            published: true,
        });
    });

    it('Calls the publish message function for each update that is made to a product', async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             1.000000,
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:      "ZOMG",
                externalId:         "8675309",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                packageTracked:     false,
                cannabis:           true,
                msrp:               25.000000,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ])

        await addProductToCatalog(catalog, product.id, testUser);

        //Update One
        await updateProductInCatalog(
            catalog.id,
            product.id,
            {
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            },
            testUser
        );

        //Update Two
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { name: "wowowowow Such Goody Candy" },
            testUser,
        );

        //Update Three
        await updateProductInCatalog(
            catalog.id,
            product.id,
            {
                price: 10.0000
            },
            testUser,
        );

        //Update Four
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { brandName: "Ignacio Sample Snacks" },
            testUser,
        );

        //Update Five
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { cannabis: false },
            testUser,
        );

        //Update Six
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { sku: 'ABC123'},
            testUser,
        );

        //Update Seven
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { size: 'big' },
            testUser,
        );

        //Update Eight
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { subtype: 'Promo Preroll' },
            testUser,
        );

        const outbox = await ProductUpdateOutbox.findAll();

        expect(outbox).toHaveLength(8);
        expect(mockedPublishCalls).toHaveBeenCalledTimes(8);
    });

    it('Creates a record for each product update and marks the record true after publishing', async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             1.000000,
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:      "ZOMG",
                externalId:         "8675309",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                packageTracked:     false,
                cannabis:           true,
                msrp:               25.000000,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ])

        await addProductToCatalog(catalog, product.id, testUser);

        //Update One
        await updateProductInCatalog(
            catalog.id,
            product.id,
            {
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            },
            testUser,
        );

        //Update Two
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { name: "wowowowow Such Goody Candy" },
            testUser,
        );

        //Update Three
        await updateProductInCatalog(
            catalog.id,
            product.id,
            {
                price: 10.0000
            },
            testUser,
        );

        //Update Four
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { brandName: "Ignacio Sample Snacks" },
            testUser,
        );

        //Update Five
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { cannabis: false },
            testUser,
        );

        //Update Six
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { sku: 'ABC123'},
            testUser,
        );

        //Update Seven
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { size: 'big' },
            testUser,
        );

        //Update Eight
        await updateProductInCatalog(
            catalog.id,
            product.id,
            { subtype: 'Promo Preroll' },
            testUser,
        );

        const outbox = await ProductUpdateOutbox.findAll();
        const publishedStatuses = _.map(outbox, 'published');

        expect(outbox).toHaveLength(8);
        expect(_.includes(publishedStatuses, false)).toBe(false);
    });
})
