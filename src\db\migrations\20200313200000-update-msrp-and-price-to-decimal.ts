import { QueryInterface }   from 'sequelize';
import { 
    monetaryField,
    preciseMonetaryField,
}                           from '@treez/dev-pack/db/fieldTypes';

const MSRP          = "msrp";
const PRICE         = "price";
const PRODUCTS      = "products";

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.changeColumn(PRODUCTS, MSRP, preciseMonetaryField);
        await queryInterface.changeColumn(PRODUCTS, PRICE, preciseMonetaryField);
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.changeColumn(PRODUCTS, MSRP, monetaryField);
        await queryInterface.changeColumn(PRODUCTS, PRICE, monetaryField);
    },
}
