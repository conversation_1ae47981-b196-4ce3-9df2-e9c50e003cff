module.exports = {
    testTimeout: 30000,
    coverageDirectory: 'reports/coverage/',
    coverageReporters: [
        'cobertura',
        'lcov',
        'text',
    ],

    globals: {
        'ts-jest': {
            diagnostics: {
                // Ignore unused imports, variables. Please keep commented
                // Uncomment if you are doing some local unit test work and don't want to be bothered with overly restrictive compile errors
                // ignoreCodes: [6133, 6138, 6192, 6196]
            },
            tsConfig: 'tsconfig.json',
        },
    },
    moduleFileExtensions: [ 'ts', 'js' ],
    reporters: [
        'default',
        ['jest-junit',
        {
            outputDirectory: 'reports/unit/',
            addFileAttribute: true,
            suiteNameTemplate: '{filepath}',
        }]
    ],
    setupFilesAfterEnv: [
        '<rootDir>/src/tests/testHelpers/init.ts',
    ],
    testEnvironment: 'node',
    testMatch: [
        '**/*.test.ts'
    ],
    transform: {
        '^.+\\.ts$': 'ts-jest'
    },
    resetMocks: true,
};
