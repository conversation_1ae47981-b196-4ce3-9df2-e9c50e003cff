import {
    NextFunction,
    Response,
}                                       from "express";
import { createApp }                    from "@treez/dev-pack/app";
import {
    AuthenticatedRequest,
}                                       from "@treez/dev-pack/auth";
import { authenticateRequest }          from "@treez/dev-pack/middleware/authenticateRequest";
import * as resolveUserFunctions        from '@treez/dev-pack/auth/resolveAuthToken';
import {
    middlewareErrorHandler,
}                                       from "../../middleware";
import testRoutes                           from "./testRoutes";

export const resolveUserSpy = jest.spyOn(resolveUserFunctions, 'resolveAuthToken');


/**
 * @param request
 * @param response
 * @param next
 * Middleware to act as stub for testing. 
 */
const stubInTokenMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    req.headers.authorization = 'Bearer StubbedInTest';

    return next();
};

const testApp = createApp();
testApp.use(stubInTokenMiddleware);
testApp.use(authenticateRequest);
testApp.use(testRoutes);
testApp.use(middlewareErrorHandler);

export default testApp;
