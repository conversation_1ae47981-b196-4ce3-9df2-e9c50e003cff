import {
    Request,
    Response,
    Router,
}                                               from 'express';
import { StatusCodes }                          from 'http-status-codes';
import _                                        from 'lodash';
import {
    PriceTierSearchParams,
    PriceTierWithHasProductsFlag,
}                                               from '@treez/commons/sharedTypings/priceTier';
import { AuthenticatedRequest }                 from '@treez/dev-pack/auth';
import { transaction }                          from '@treez/dev-pack/db';
import {
    sendErrorResponse,
    ErrorResponse
}                                               from '@treez/dev-pack/errors';
import logger                                   from '@treez/dev-pack/logger';
import { getUserInfo }                          from '../lib/expressUtils';
import { ProductPermission }                    from '../lib/permissions';
import PriceTier, {
    validatePriceTier
}                                               from '../models/PriceTier';
import CatalogPriceTier, {
    getPriceTierWithHasActiveProductsFlag,
    hasDuplicatePriceTierWithLabel,
}                                               from '../models/CatalogPriceTier';
import Catalog, {
    findCatalogById
}                                               from '../models/Catalog';
import {
    getPriceTierCatalogPresence,
    getProductsAssignedToPriceTier
}                                               from '../models/CatalogProduct';
import {
    checkCatalogPermissions,
    checkPermissionsOnMultipleCatalogs,
    checkPermissionsOnMultiplePriceTiers,
    checkPriceTiersPermissions,
}                                               from '../middleware';

const route = Router();

const {
    ReadCatalog,
    UpdateCatalog,
} = ProductPermission;

route.post('/search',
checkPermissionsOnMultipleCatalogs('body', 'catalogId', [[ReadCatalog]]),
async (request: Request<{},{}, PriceTierSearchParams>, response: Response) => {
    const {
        body: searchParams
    } = request;
    try{
        const {
            searchString,
            catalogId,
            filters = [],
        } = searchParams;

        if(catalogId){
            const data = await CatalogPriceTier.searchPriceTiersByCatalogId(
                catalogId,
                {
                    searchString,
                    filters,
                }
            );

            return response.status(StatusCodes.OK).json({ data });
        }
        else {
            const data = await PriceTier.searchForPriceTiers(searchParams);
            return response.status(StatusCodes.OK).json({ data });
        }
    }
    catch(error){
        logger.error("Error POST /pricetiers/search - searching for price tiers: ", error);
        return sendErrorResponse(response, error);
    }
});

route.get('/:id/catalog/:catalogId',
checkCatalogPermissions('catalogId', [[ReadCatalog]]),
async (request: Request, response: Response) => {
    const {
        params,
    } = request;

    const {
        catalogId,
        id,
    } = params;

    try{
        const priceTierId = Number(id);
        if(_.isNaN(priceTierId)){
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Invalid Parameter id: ${id}`
            );
        };

        const catalog : Catalog = await findCatalogById(catalogId);

        if (!catalog) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find catalog for id: ${catalogId}`
            );
        }

        const catalogPriceTier = await CatalogPriceTier.findOne({
            where: {
                catalogId: catalogId,
                priceTierId: id,
            },
            include: [{
                model: PriceTier,
            }]
        });

        if (!catalogPriceTier) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find PriceTier in catalog for id: ${id}`
            );
        }

        let priceTierWithHasActiveProductsFlag = await getPriceTierWithHasActiveProductsFlag(catalogPriceTier.priceTier, catalog.id);

        priceTierWithHasActiveProductsFlag = Object.assign({}, priceTierWithHasActiveProductsFlag, catalogPriceTier.priceTierOverrides);

        if(priceTierWithHasActiveProductsFlag == null){
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find PriceTier with id: ${id}`
            );
        }

        return response.status(StatusCodes.OK).json({ data : priceTierWithHasActiveProductsFlag });
    }
    catch(error){
        logger.error("Error GET /pricetiers/:id/catalog/:catalogId - finding priceTier by Id: ", error);
        return sendErrorResponse(response, error);
    }
});

/** Find the presence of a specific set of price tier id's in catalogs.
 * returns  {
    [priceTier.id]: catalogIds[]
}*/
route.get('/catalogs',
checkPermissionsOnMultiplePriceTiers('query', 'priceTierIds', [[ReadCatalog]]),
async (request: Request<{},{},{},{ priceTierIds : string }>, response: Response) => {
    const {
        priceTierIds
    } = request.query;

    let priceTierIdsToNumbers: number[] = [];
    if (priceTierIds.length > 0) {
        priceTierIdsToNumbers = priceTierIds.split(',').map(id => Number(id));
    }

    try {
        const priceTierPresenceInCatalog = await getPriceTierCatalogPresence(priceTierIdsToNumbers);
        const aggregatedCatalogPresence : Record<number, number[]> = {};
        priceTierIdsToNumbers.forEach(id => {
            aggregatedCatalogPresence[id] = priceTierPresenceInCatalog[id] || [];
        });

        return response.status(StatusCodes.OK).json({ data : aggregatedCatalogPresence });
    }
    catch(error) {
        logger.error("Error GET /pricetiers/catalogs - finding price tier presence in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
})

route.post('/catalog/:catalogId',
checkCatalogPermissions('catalogId', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body: newPriceTier,
        params,
    } = request;

    const {
        catalogId,
    } = params;

    const userInfo = getUserInfo(request);

    try{
        // if this the body is invalid this will throw an error
        validatePriceTier(newPriceTier);

        if (
            await hasDuplicatePriceTierWithLabel(
                newPriceTier.label,
                catalogId
            )
        ) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Duplicate Price Tier Label'
            );
        }

        const catalog : Catalog = await findCatalogById(catalogId);

        if (!catalog) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find catalog for id: ${catalogId}`
            );
        }

        const data: PriceTierWithHasProductsFlag = await transaction( async () => {
            const priceTier = await PriceTier.createPriceTier(
                newPriceTier,
                catalog,
                userInfo
            );

            const priceTierWithHasActiveProductsFlag = await getPriceTierWithHasActiveProductsFlag(priceTier, catalog.id);

            return priceTierWithHasActiveProductsFlag;
        });

        return response.status(StatusCodes.CREATED).json({ data });
    }
    catch(error){
        logger.error(`Error POST /pricetiers/catalog/${catalogId}`, error);
        return sendErrorResponse(response, error);
    }
});

route.put('/:id/catalog/:catalogId',
checkCatalogPermissions('catalogId', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        params: { id, catalogId },
        body: newlyEditedPriceTier
    } = request;

    const userInfo = getUserInfo(request);

    try{
        // if this the body is invalid this will throw an error
        validatePriceTier(newlyEditedPriceTier);

        const catalog : Catalog = await findCatalogById(catalogId);

        if (!catalog) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find catalog for id: ${catalogId}`
            );
        }

        const priceTierId = Number(id);
        if(_.isNaN(priceTierId)){
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Invalid Parameter id: ${id}`
            );
        };

        const priceTier = await PriceTier.findOne({
            where: {
                id: priceTierId
            }
        });

        if (priceTier === null) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find price tier for id: ${priceTierId}`
            );
        }

        if (
                priceTier.label !== newlyEditedPriceTier.label
            &&  await hasDuplicatePriceTierWithLabel(
                    newlyEditedPriceTier.label,
                    catalogId,
                    priceTierId,
            )
        ) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Duplicate Price Tier Label'
            );
        }

        const data : PriceTierWithHasProductsFlag = await transaction(async () => {
            const priceTier = await PriceTier.updatePriceTier(
                priceTierId,
                catalog.id,
                request.body,
                userInfo
            );

            const priceTierWithHasActiveProductsFlag : PriceTierWithHasProductsFlag = await getPriceTierWithHasActiveProductsFlag(priceTier, catalog.id);

            return priceTierWithHasActiveProductsFlag;
        });

        return response.status(StatusCodes.ACCEPTED).json({ data });

    }
    catch(error){
        logger.error("Error PUT /pricetiers/:id/catalog/:catalogId - updating priceTier: ", error);
        return sendErrorResponse(response, error);
    }
});

route.get('/:id/products',
checkPriceTiersPermissions('id', [[ReadCatalog]]),
async (request: Request<{ id : string}, {}, {}, { orgCatalogId: string }>, response: Response) => {
    const {
        params: {
            id
        },
        query: {
            orgCatalogId
        }
    } = request;

    try {
        const priceTierId = Number(id);
        if (_.isNaN(priceTierId) || priceTierId <= 0) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid priceTierId supplied. Please supply a valid priceTierId'
            );
        }

        const orgCatalogIdAsNumber = Number(orgCatalogId);
        if (_.isNaN(orgCatalogIdAsNumber)) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Valid orgCatalogId required to query for products with price tier assigned'
            );
        }

        const products = await getProductsAssignedToPriceTier(
            priceTierId,
            orgCatalogIdAsNumber
        );

        return response.status(StatusCodes.OK).json({ data : products });
    }
    catch (error) {
        logger.error("Error GET /pricetiers/:id/products - finding products assigned to a PriceTier: ", error);
        return sendErrorResponse(response, error);
    }
})

export default route;
