import {
    AutoIncrement,
    Column,
    PrimaryKey,
    Table,
    ForeignKey,
    BelongsTo,
}                                       from 'sequelize-typescript';
import { Op }                           from 'sequelize';
import _                                from 'lodash';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import { jsonField }                    from '@treez/dev-pack/db/fieldTypes';
import { CatalogProductStatus }         from '@treez/commons/sharedTypings/product';
import {
    PriceTierSearchParams,
    PriceTierWithHasProductsFlag,
}                                       from '@treez/commons/sharedTypings/priceTier';
import Catalog, {
    findChildCatalogIds,
}                                       from './Catalog';
import PriceTier                        from './PriceTier';
import CatalogProduct                   from './CatalogProduct';

@Table({tableName: 'catalogPriceTiers'})
export default class CatalogPriceTier extends TreezModel<CatalogPriceTier> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                 number;

    @ForeignKey(() => Catalog)
    @Column
    catalogId:          number;

    @ForeignKey(() => PriceTier)
    @Column
    priceTierId:        number;

    @Column(jsonField)
    priceTierOverrides: Partial<PriceTier>;

    @BelongsTo(() => Catalog)
    catalog:            Catalog;

    @BelongsTo(() => PriceTier)
    priceTier:          PriceTier;

    static async searchPriceTiersByCatalogId(
        catalogId: number,
        searchParams: Omit<PriceTierSearchParams,'catalogId'>
        ) : Promise<PriceTierWithHasProductsFlag[]>
    {
        const {
            searchString,
            filters,
        } = searchParams;

        let whereClause = {
            catalogId
        };

        const [ labelFilter ] = _.remove(filters, filter => filter.key === 'label');

        let includeWhere = {};

        if (labelFilter){
            if (!_.isEmpty(searchString)){
                // If labelfilter && searchString
                includeWhere = {
                    ...includeWhere,
                    [Op.and]:[
                        {
                            'label':{
                                [Op.iLike]: `%${searchString}%`
                            }
                        },
                        {
                            'label': {
                                [Op.in] : labelFilter.values
                            }
                        }
                    ]
                }
            }
            else {
                // only labelFilter
                includeWhere = {
                    ...includeWhere,
                    'label': {
                        [Op.in] : labelFilter.values
                    }
                }
            }
        }
        else if (!_.isEmpty(searchString)) {
            // only searchString
            includeWhere = {
                ...includeWhere,
                'label':{
                    [Op.iLike]: `%${searchString}%`
                }
            };
        }

        if (!_.isEmpty(filters)) {
            filters.forEach(filter => {
                includeWhere = {
                    ...includeWhere,
                    [filter.key] : {
                        [Op.in]: filter.values
                    }
                }
            });
        }

        const catalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                ...whereClause
            },
            include: [
                {
                    model: PriceTier,
                    where: includeWhere
                }
            ],
        })

        const priceTiers : PriceTier[] = catalogPriceTiers.map(catalogPriceTier => {
            const {
                priceTier,
                priceTierOverrides
            } = catalogPriceTier;

            if(priceTierOverrides){
                return Object.assign(priceTier, priceTierOverrides);
            }

            return priceTier;
        });

        const priceTiersWithHasActiveProducts = getPriceTiersWithHasActiveProductsFlag(priceTiers, catalogId);

        return priceTiersWithHasActiveProducts;
    }

}
/**
 * Populates hasProducts field on a price tier for a singular price tier
 */
export const getPriceTierWithHasActiveProductsFlag = async (priceTier : PriceTier, catalogId : number) : Promise<PriceTierWithHasProductsFlag> => {
    const priceTiers = await getPriceTiersWithHasActiveProductsFlag([priceTier], catalogId);

    return priceTiers[0];
}

/**
 * Populates hasProducts field on a price tier for multiple price tiers
 */
export const getPriceTiersWithHasActiveProductsFlag = async (priceTiers : PriceTier[], catalogId : number) : Promise<PriceTierWithHasProductsFlag[]> => {
    const priceTierIds = priceTiers.map(p => p.id);

    const childCatalogIds = await findChildCatalogIds(String(catalogId));

    const tierIdsWithProducts = _.chain(await CatalogProduct.findAll({
        where: {
            catalogId: {
                [Op.in]: [catalogId, ...childCatalogIds],
            },
            priceTierId: {
                [Op.in]: priceTierIds,
            },
            status: {
                [Op.not]: CatalogProductStatus.DELETED,
            }
        },
        attributes: ['priceTierId']
    }))
    .map('priceTierId')
    .value();

    const tierIdsWithProductsSet = new Set(tierIdsWithProducts);

    const priceTierWithHasProductsFlag = priceTiers.map(priceTier => {
        if (tierIdsWithProductsSet.has(priceTier.id)) {
            return {
                ...priceTier.get(),
                hasProducts: true,
            } as unknown as PriceTierWithHasProductsFlag;
        }

        return {
            ...priceTier.get(),
            hasProducts: false,
        } as unknown as PriceTierWithHasProductsFlag;
    })

    return priceTierWithHasProductsFlag;
}

export const hasDuplicatePriceTierWithLabel = async (
    label: string,
    catalogId: string,
    priceTierId?: number,
): Promise<boolean> => {

    let whereClause : any = {}

    if (priceTierId) {
        whereClause = {
            id: {
                [Op.not]: priceTierId,
            },
            label: {
                [Op.iLike] : label,
            },
        }
    }
    else {
        whereClause = {
            label: {
                [Op.iLike] : label,
            },
        }
    }

    const existingPriceTier = await CatalogPriceTier.findOne({
        where: {
            catalogId
        },
        include : [
            {
                model: PriceTier,
                where: {
                    ...whereClause,
                },
            }
        ]
    });

    return existingPriceTier !== null;
}
