version: 2.1

commands:
  install_pulsar:
    description: "Install Pulsar"
    steps:
      - run:
          name: "Set Pulsar version"
          command: |
            echo $PULSAR_VERSION >> /tmp/pulsar

      - restore_cache:
          keys:
            - pulsar-{{ checksum "/tmp/pulsar" }}

      - run:
          name: "Download Pulsar"
          command: |
            if [[ ! -f /tmp/apache-pulsar-client.deb && ! -f /tmp/apache-pulsar-client-dev.deb ]]; then
              wget -O /tmp/apache-pulsar-client.deb https://downloads.apache.org/pulsar/pulsar-${PULSAR_VERSION}/DEB/apache-pulsar-client.deb
              wget -O /tmp/apache-pulsar-client-dev.deb https://downloads.apache.org/pulsar/pulsar-${PULSAR_VERSION}/DEB/apache-pulsar-client-dev.deb
            fi

      - save_cache:
          key: pulsar-{{ checksum "/tmp/pulsar" }}
          paths:
            - /tmp/apache-pulsar-client.deb
            - /tmp/apache-pulsar-client-dev.deb

      - run:
          name: "Install Pulsar"
          command: |
            sudo apt-get -y install /tmp/apache-pulsar-client.deb /tmp/apache-pulsar-client-dev.deb

jobs:
  test:
    parallelism: 8
    docker:
      - image: cimg/node:12.22

      - image: circleci/postgres:10-alpine-ram
        environment:
          POSTGRES_USER: ubuntu
          POSTGRES_DB: testing
        command: postgres

    environment:
      TZ: "America/Los_Angeles"
      PULSAR_VERSION: "2.9.1"

    steps:
      - checkout

      - install_pulsar

      - restore_cache:
          keys:
            - product-api-deps-{{ checksum "package-lock.json" }}

      - run:
          name: Install dependencies
          command: npm install

      - save_cache:
          key: product-api-deps-{{ checksum "package-lock.json" }}
          paths:
            - node_modules

      - run:
          name: Wait for PostgreSQL
          command: dockerize -wait tcp://localhost:5432 -timeout 1m

      - run:
          environment:
            JEST_JUNIT_OUTPUT: ./reports/unit/junit.xml
            POSTGRES_USER: ubuntu
          name: Unit Tests
          command: |
                    TESTFILES=$(circleci tests glob src/**/*.test.ts | circleci tests split --split-by=timings)
                    npm run test $TESTFILES

      - store_test_results:
          path: ./reports/unit

      - store_artifacts:
          path: ./reports

workflows:
    product-api:
        jobs:
          - test
