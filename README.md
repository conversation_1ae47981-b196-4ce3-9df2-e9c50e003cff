# Product API

[![Build Status](https://jenkins-www.build.treez.io/buildStatus/icon?job=apps%2Fproduct-api%2Fmaster&subject=master)](https://jenkins.build.treez.io/job/apps/job/product-api/job/master/)
## Setup
Ensure you have cloned the repo and check out the `master` branch. 

1. Install dev dependencies/packages

    a) If you don't have libpulsar, install it:

    - Macs with M1 chip refer to [libpulsar M1 chip instructions below.](#libpulsar-for-macs-with-m1-chip)

    - Macs without M1 chip run
        ```
        $ brew install libpulsar
        ```

    b) Update package/dependencies if needed, otherwise just install.

         $ npm install

2. Run Postgresql locally on your machine.

If you do not have postgres

    $ brew install postgresql

To start automatically.

    $ brew services start postgresql

To start manually.

    $ pg_ctl -D /usr/local/var/postgres start

To stop manually.

    $ pg_ctl -D /usr/local/var/postgres stop

Make sure your username && database in config/default.json5 matches to what you will be using (if you are using a username and pw for postgres, by default this module is set up with no username or pw), default name of database in postgres is "product-api". For help or trouble shooting postgresql locally on your machine, see links below.

* see below for creating a new user if your administrator has been set up instead of postgres.
https://stackoverflow.com/questions/16973018/createuser-could-not-connect-to-database-postgres-fatal-role-tom-does-not-e/16974197#16974197

* see below for postgres commands on creating a new users, and db for setting up postgres locally.
https://www.codementor.io/engineerapart/getting-started-with-postgresql-on-mac-osx-are8jcopb

3. For the tests to run properly all you need to have running is postgres, the test suite is set up to add/drop database and run migrations before every single test. It also seeds test data for testing.

    $ npm run test

To check for line coverage

    $ jest --coverage --runInBand

* All tests will should be passing, any changes made to module should run original test suite to check for consistency.

* Debugging tests through VS Code is supported through the 'Debug Jest Tests' launch configuration, but make sure to build the project before running the tests.

4. To run unit or manual tests, or simply to run a local instance start by creating the database "product-api".

a) Log into postgres.
in a bash shell at comman line, run:

    $ psql postgres

b) You can check your list of databases.

    $ \l


c) Create your database.

    $ CREATE DATABASE "product-api";

d)Check to make sure your database was created.

    $ \l

   should return something like the below

                       List of databases
    Name     | Owner | Encoding | Collate | Ctype | Access privileges
-------------+-------+----------+---------+-------+-------------------
 postgres    | oana  | UTF8     | C       | C     |
 product-api | oana  | UTF8     | C       | C     |
....................

5. Exit the postgres database and psql by:
    $ \q

6 Download seed data from https://drive.google.com/open?id=1uMtnMQtCzUFh3-0BwQIlFGfh62vjy1uX

7. Load the seed data.
    - in a bash shell at the command line
    - got to the path where you have downloaded the seed data
    - run the following commnand:
        $ psql product-api < product-api-seed.sql

8. Initialize extensions and run migrations and check to make sure tables were created.

    Initialize extensions that are used. This is a manual step because we can't automate it in RDS via migrations.
    - in a bash shell at the command line run:
        $ psql -c "CREATE EXTENSION IF NOT EXISTS pg_trgm;" product-api

9.Run the migrations command to create your tables.

    a) Truncate outdated products tables.
        a.1) psql product-api
        a.2) truncate table "products" CASCADE;
        a.3) \q
    b) got to the folder where you have the code for the product-api and run
        $ npm run migrations
    c) check that the migrations fully run by, in a bash shell run:
        c.1) psql product-api
        c.2) select * from "SequelizeMeta";

        verify that 58 rows are returned. NOTE that number may not be up to date. If things do not work, reach out to the team.

10.Log into postgres to double check that your tables were created in terminal.
    a) in a bash shell at the command line run:
        $ psql product-api

    b)Show all tables:
        $ \dt

                                       List of relations
             Schema |             Name              | Type  | Owner
            --------+-------------------------------+-------+-------
             public | SequelizeMeta                 | table | oana
             public | assortmentProducts            | table | oana
             public | assortments                   | table | oana
             public | catalogPriceTiers             | table | oana
             public | catalogProductChanges         | table | oana
             public | catalogProducts               | table | oana
             public | catalogs                      | table | oana
             public | externalReferences            | table | oana
             public | permissions                   | table | oana
             public | priceTiers                    | table | oana
             public | productChanges                | table | oana
             public | productRequirements           | table | oana
             public | productRequirementsToCatalogs | table | oana
             public | productUpdateOutbox           | table | oana
             public | products                      | table | oana
             public | roles                         | table | oana
             public | usersToRoles                  | table | oana
            (16 rows)

    c) verify that you have a row for you catalog
        c.1) select * from catalogs;
        c.2) insert a row if none exists
            insert into catalogs("id","name","ownerId","ownerType") values (2,'Treez Demo Org Catalog',2, 'organization');
            the id of the catalog should match what you have in the inventory.tz_configuration, where CODE='CATALOG_ID'
    c) Exit.
        $ \q

11. Now spin up the server/api product-api.

Run in dev mode for testing/development.

    $ npm run dev

Test with this address.

    http://localhost.treez.io:8303/health-check-ping

    * Should see a result similar to below in a browser window.

    {"pong":1562865114698}
 12.if you have Inventory running on your machine, then check that in inventory schema:
    This address should match what you have in inventory.tz_configuration, where CODE='PRODUCT_API_URL'

## Data Model
![Alt text](./docs/data-model-6-1-21.png?raw=true "ERD as of June 1st 2021, RBAC excluded")

https://lucid.app/lucidchart/ee984dae-5526-4d6a-aea4-7d2f53df332f/edit

## Products
1. Product Definition
2. PlanTreez: Retail and Organizational Management of Products
3. BrandTreez: Brand Products and Linking Retailer Products to BrandTreez
4. CRUD Operations
5. Linking Products To Brand Treez
6. Merging Products

### Product Definition
A Product is a record that records the general information of a product such as its name, attributes, and brand.  It dooes not have inventory specific information such as batchs, lab results, or vintage.

***Price and Status of a Product in a context of a store, brand, or organization can be found in Catalog Products***


### PlanTreez: Retail and Organizational Management of Products
Retailers stock products in their stores to sell to their customers and that product is represented by a record in their PoS to be referenced during the invoicing of the customer and any retail management that is done either in accounts payable, customer management, inventory, reports, or hand ful of other instances. A retailers record can with be of ownerType "Store" or "Org".  A Store's catalog can have their own products or they can be assigned products from their parent organization.

### BrandTreez: Brand Products
Like a retailer, each brand or distributor maintains a digital representation of their products that they sell or promote. By creating a Brand Product in BrandTreez, the brand is able to disseminate and control content of their products in retailers by offering the retailers the ability to "link" to their products and continuously receive brand provided information and media for each product.


### CRUD Operations for Products
1. Create a Product
2. Read a Product
3. Update a Product
4. Destroy a Product


### Linking to A BrandTreez Product
1. Linking to a BrandTreez Product
2. Editing a Linked Product
3. Adding a BrandTreez Product to Catalog



### Merging Products
1. [Merge to New Product](#merge-to-a-new-product)
2. [Merging To Existing Product](#merge-to-an-existing-product)
3. [Merge to BrandTreez Product](#merge-to-a-brandtreez-product)

Merging products, or deduping, is taking two or more products either in one catalog or across an organization's collection of catalogs and merging the various different records to a central record. Each new record is a product record just as the ones it replaced, but it will have the consensus of correct attributes across the products it is merging.

Merging can be done either by creating a new product centrally or by choosing one record to be the singular representation for multiple products that represent duplications in catalogs.

Merging can also be done by selecting a BrandTreez product to merge to. This is an option that is currently not available via UI/UX constraints.

Requests to merge products have two endpoints depending on if the product exists or if it is a new product that is being created as a result of the merge.

#### Merge To a New Product
*Endpoint: 'catalogs/:catalogId/products/merge'*

| Field                  | Description       | Data Type |
|:---------------------: | :---------------- | :-------: |
| product    | the product you wish to merge to | Partial<Product> |
| productIds | the ids of the products you're hoping to merge | number[] |
| catalogIds | the ids of the catalogs you're to replace those products in | number[] |
| catalogInformation | any price, status, or overrides you would like for each catalog| Record<CatalogId, Partial<Product&CatalogProduct>> |

Merging to a new product creates a new product at the target catalog level, identified in the parameters. All products in catalogs identified in the body must either be of the same catalog or be in catalogs that are children catalogs to the target catalog.

It will create a new product, remove all identified products from all identified catalogs, and assign the new product with any additional information that can be found in the catalogInformation object passed in the request body if any, otherwise it will default to active with a null price or 0 if it is a store.

#### Merging To an Existing Product

##### Endpoint: 'catalogs/:catalogId/products/merge/:productId'

##### Parameters

**catalogId**: the id of the highest level of catalog in the request- if it is an organization merging products across stores it should be the orgs Id. If it is a store just merging locally than it can be that store's catalog id.

**productId**: the id of the product to merge to

##### Request Body

| Field                  | Description       | Data Type |
|:---------------------: | :---------------- | :-------: |
| productIds | the ids of the products you're hoping to merge | number[] |
| catalogIds | the ids of the catalogs you're to replace those products in | number [] |
| catalogInformation | any price, status, or overrides you would like for each catalog| Record<CatalogId, Partial<Product&CatalogProduct>>|

If you are merging to an existing product, the API will collect the products you are looking to merge and the product you are merging to. It will replace the products that are to be merged to the "true" product with the selected "true" product.

If the product selected to merge to is a store owned product and the merge is happening at the organization level, it will create a copy of that product at the organization level and retire all the duplicate products as well as the product to merge to and replace all with the clone at the organization level.

#### Merge to a BrandTreez Product



## Catalogs
Catalogs are a grouping of products sold under one particular context.  Each store has a catalog, a main catalog that accounts for its normal product selection and sales, and can have secondary catalogs as well. An organization has a central catalog, which is an accumulation of products it has across all stores. It likewise can have secondary catalogs.

## Catalog Products
Catalog Products is a through-table that establishes the relationship of products to catalogs, or which products are assigned to which catalogs. These records also contain fields specific to the product being present in that catalog, principally status and price.


## PriceTiers
PriceTiers are pricing tables for products where it is possible to set prices in thresholds based on the amount purchased.

## CatalogPriceTiers
CatalogPriceTiers is a through-table that establishes the relationship of price tiers to catalogs. Defines which priceTiers are present in the catalog.


### Product Requirements
1. [Context](#product-requirement-context)
2. [Limits](#product-requirement-limits)
4. [Structure](#product-requirements-structure)
3. [CRUD Product Requirements](#how-to-crud-product-requirements)

#### Product Requirement Context
Product Requirements are needed for the validation of a product per internal or governmental reporting standards. Each state maintains their own reporting criteria and organizations may themselves want to restrict the variance of product infromation by introducing standards on product input to assure employees or partners are inputting valid and necessary information.

Product Requirements are applied to catalogs.  A Catalog can have multiple requirements placed on it.  When a product is added to a catalog, it must meet the requirements that have been applied to the catalog.

#### Limits
Product Requirements at this moment are two dimensional in complexity, meaning that you can have something like:

*_Base Requirement:_*
> Product Type is required with Options of [Edible, Cartridge, Flower]

*_Conditional Requirement:_*
> If Type is Edible, Subtype is required and valid options are: Baked Goods, Chocolates, Gummies.

It is not yet able to reach a third dimension, or have conditions that also have conditions like the example

*_Base Requirement:_*
>Product Type is Required with options of [Edible, Cartridge, Flower]

*_Conditional Requirement:_*
>If Type is Edible, Subtype is required and valid options are: Baked Goods, Chocolates, Gummies.

*_Conditions on the Conditional Requirement (NOT ALLOWED)_*
>If type Edible(1), and if type Subtype of Baked Goods(2), a list of Ingredients is required(3)

the conditions in this example show three dimenisons: Type(1) --> Subtype (2) --> (Ingredients)

This is accomplished in a different manner however: the required flag and conditional requirements.

Rather than say if Type, if Subtype, then ingredients required. You can say if type, subtype is required.

Then you can defined subtype as a not required field, but with conditional validations as a top level item.

[See below for here to see how this can be executed](#examples-of-product-requirements-structure)

#### Structure
The data structure of the Product Requirements record are:

| Field                   | Description       | Type   |
| :---------------------: | :---------------- | :--------------: |
| id | the unique identifier, or Primary Key, for the requirement | integer|
| description | a description as to what the requirements are | string |
| owningCatalogId | if there is an owner of the requirements, for example Garden of Eden's personal corporate requirements, it can be associated to an organization or store's catalog | foreign Key Catalog Id - int |
| requirements | the requirements json object that defines valid input of the product fields | JsonB |

The interface of the requirements json object are two dimensions, or have two levels: Base Requirements and Conditional Requirements

##### Base Requirements
The first level is:

| Field                   | Description       |
| :---------------------: | :---------------- |
| dataType | the correct dataType for this field |
| options  | if there are finite options to select (enum), they would be defined here in an array of either strings or numbers |
| requiredField | boolean |
| conditionalRequirements | an object that can only be applied if theoptions field has been used. This field looks at each option available,and provides further details of what is required for validation. It is the second level and is defined below. |

##### Conditional Requirements
The second level of requirements, or conditional requirements, are detailed by passing the below structure to the conditionalRequirements value of base Requirements for a product field.

*_The data structure mirrors that of the base requirements except that it does not include further conditional requirements._*

| Field                  | Description       |
|:---------------------: | :---------------- |
| dataType | the correct dataType for this field |
| options  | if there are finite options to select, they would be defined here in an array of either strings or numbers |
| required | boolean (true/false) on if the field is required to be valid |

##### Examples of Product Requirements Structure
This is just the requirements object on the Product Requirements Field, below is an example of the structure with some noticeable hints in creating an effective product requirements:

(JSON)
```
{
    "name": {
        "dataType": "string",
        "required": true
    }
    "type": {
        "dataType": "string",
        "options": ["beverage", "pill", "tincture", "flower", "extract", "edible", "plant", "topical", "merch", "cartridge", "misc", preroll"],
        "required: true,
        "conditionalRequirements": {
            "flower": {
                "subtype": {
                    "dataType": "string",
                    "options": ["bulk flower", "infused flower", "kief","pre-pack smalls", "pre-pack", "shake", "strain specific shake"],
                    "required": true,
                }
                "amount": {
                    "dataType": "string",
                    "required": true
                }
                "uom": {
                    "dataType": "string",
                    "required": true,
                }
            },
            "preroll": {
                "subtype": {
                    "dataType": "string",
                    "options": ["flower", "infused","shake"],
                    "required": true,
                }
                "amount": {
                    "dataType": "string",
                    "required": true
                }
                "uom": {
                    "dataType": "string",
                    "required": true,
                }
            },
            "edible": {
                "subtype": {
                    "dataType": "string",
                    "options": ["baked good", "chocolate","gummies"],
                    "required": true,
                }
            }
        }
    }
    "uom": {
        "dataType": "string",
        "options": ["fl-oz", "g", "gal", "kg", "l", "lb", "mg", "ml", "oz", "pt", "qt"]
    }
    "subtype": {
        "dataType": "string",
        "conditionalRequirements": {
            "baked good": {
                "ingredients": {
                    "required": true,
                    "dataType": "array",
                    "options": ["baking soda", "butter", "vanilla"]
                }
            }
        }
    }
}
```

Notice a few things:
1. I can save space on the pre-roll and flower UOM by just declaring it required (always will need the data type) and then defining it as a top level with options below.

2. The previous example on the limitations of 2 dimenisions of requirements, we have accomplished the third by making those requirements communicated at the top level.  In type we say if it is an edible require subtype of these values. What we can't do is in that block declare the requirement that if its a baked good it must have ingredients. Instead we accomplish this in the subtype block.

#### How to CRUD Product Requirements

##### Create Product Requirements
To create product requirements that are not associated to an organization, such as California reporting requirements, a post request can be sent to the API end point of:

> ProductAPI/productRequirements

##### Read Product Requirments
You can search for the product requirements by their Id:
> ProductAPI/productRequirements/:id

##### Update Product Requirements
You can update product requirements using their id.  All the top level fields can be adjusted as normal and not all fields need to be passed if they are not getting updated. For requirements, updates happen in the json object by spreading the updates after the previous updates like :
```
{
    ...requirements,
    ...updates
}
```

this means that if you had:
```
    type: {
        dataType: string,
        requiredField: true,
    },
    subtype: {
        dataType: string,
        requiredField: true,
    }
```

you could update just the type field by passing:
```
{
    requirements: {
        type: {
            dataType: string,
            requiredField: true,
            options: ['Edible', 'Beverage']
        }
    }
}
```

and you would have the below requirements saved:
```
{
    requirements: {
        type: {
            dataType: string,
            requiredField: true,
            options: ['Edible', 'Beverage']
        }
        subtype: {
            dataType: string,
            requiredField: true,
        }
    }
}
```
## Libpulsar for Macs with M1 Chip

1. Install homebrew through rosetta, refer to this aritcle:

    https://medium.com/mkdir-awesome/how-to-install-x86-64-homebrew-packages-on-apple-m1-macbook-54ba295230f

2. Install libpulsar by running:
    ```bash
    brew install libpulsar
    ```
    https://pulsar.apache.org/docs/en/2.5.0/client-libraries-cpp/

3. Node 12 is required. Download nvm (node version manager) and refer to this article to set up and use node 12:

    https://onexlab-io.medium.com/nvm-install-apple-macos-m1-silicon-chip-642deef54dbf

4. The python wrapper is required, install by typing:
    ```bash
    $ pip install pulsar-client==2.9.1
    $ npm install pulsar-client
    ```
    https://pulsar.apache.org/docs/en/client-libraries-python/

    https://pulsar.apache.org/docs/en/client-libraries-node/

5. If you are still facing issues please refer to https://pulsar.apache.org/docs/en/client-libraries-cpp/#macos and set up pulsar-client manually and set paths.

6. You may still need to export these variables: (might work without compiling the source on step 5)
    ```bash
    export LIBRARY_PATH=$(brew --prefix)/lib
    export LDFLAGS=-L$(brew --prefix)/lib
    export CFLAGS=-I$(brew --prefix)/include
    export CPPFLAGS=-I$(brew --prefix)/include
    ```