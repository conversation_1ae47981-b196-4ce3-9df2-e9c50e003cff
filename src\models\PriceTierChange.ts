import { StatusCodes }              from 'http-status-codes';
import _                            from 'lodash';
import { TreezModel }               from '@treez/dev-pack/models/TreezBaseModel';
import {
    enumField,
    jsonField,
    intField,
    stringField,
    autoPKField,
}                                   from '@treez/dev-pack/db/fieldTypes';
import { ErrorResponse }            from '@treez/dev-pack/errors';
import {
    AutoIncrement,
    CreatedAt,
    BelongsTo,
    Column,
    ForeignKey,
    PrimaryKey,
    Table,
}                                   from 'sequelize-typescript';
import { ValidationOptions }        from 'sequelize/types/instance-validator';
import { generateChangeset }        from '../lib/productHistory';
import PriceTier                    from './PriceTier';

export enum PriceTierAction{
    CREATE      = 'CREATE',
    UPDATE      = 'UPDATE',
}

@Table({ tableName: 'priceTierChanges'})
export default class PriceTierChange extends TreezModel<PriceTierChange>{
    @AutoIncrement
    @PrimaryKey
    @Column(autoPKField)
    id:                                     number;

    @Column(enumField(PriceTierAction))
    actionType:                             PriceTierAction;

    @ForeignKey(() => PriceTier)
    @Column({ allowNull: false })
    priceTierId:                            number;

    @Column(jsonField)
    oldPriceTier:                           Partial<PriceTier>;

    @Column(jsonField)
    newPriceTier:                           Partial<PriceTier>;

    @Column(jsonField)
    changes:                                PropertyChange[];

    @Column(stringField)
    userAuthId:                             string;

    @Column(stringField)
    sellTreezUserId:                        string | null;

    @Column(intField)
    version:                                number;

    @BelongsTo(() => PriceTier)
    priceTier:                              PriceTier;

    @CreatedAt
    @Column({ allowNull: false })
    createdAt:                              Date;

    public validate(options?: ValidationOptions) {
        if (_.includes(Object.values(PriceTierAction), this.actionType)) {
            return super.validate(options);
        }
        else {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid enum value "${this.actionType}" for actionType!`
            );
        }
    }

    static async recordPriceTierChange (
        priceTierId:    number,
        actionType:     PriceTierAction,
        userInfo:       UserInfo,
        newPriceTier:   Partial<PriceTier>,
        oldPriceTier?:   Partial<PriceTier>,
    ): Promise<PriceTierChange | undefined> {
        let changes: PropertyChange[] = [];

        if(actionType !== PriceTierAction.CREATE){
            changes = generateChangeset(
                newPriceTier?.toJSON?.() ?? newPriceTier,
                oldPriceTier?.toJSON?.() ?? oldPriceTier,
            );

            if(actionType === PriceTierAction.UPDATE && changes.length === 0){
                return undefined;
            }
        }

        const {
            userAuthId,
            sellTreezUserId
        } = userInfo;

        let version = 1;
        if (actionType !== PriceTierAction.CREATE) {
            const previousChange = await PriceTierChange.findOne({
                limit: 1,
                where: {
                    priceTierId
                },
                order: [
                    ['version' , 'DESC']
                ]
            })

            version = previousChange?.version ?? version;
            version++;
        }

        return await PriceTierChange.create({
            actionType,
            oldPriceTier,
            newPriceTier,
            changes,
            priceTierId,
            userAuthId,
            sellTreezUserId,
            version
        });
    }

}
