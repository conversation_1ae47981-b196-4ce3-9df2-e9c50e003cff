import {
    monetaryField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const PRODUCTS = 'products';
const MSRP = 'msrp';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(PRODUCTS, MSRP, monetaryField);
        await queryInterface.sequelize.query(`UPDATE products SET msrp = price`);
        // Requested by <PERSON> and <PERSON>
        await queryInterface.sequelize.query(`UPDATE products SET type = upper(type)`);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn(PRODUCTS, MSRP);
    },
}
