
import {
    ActionType,
    CatalogProductStatus,
    OwnerType,
    ProductField,
    ProductType,
    UOM,
}                                       from '@treez/commons/sharedTypings/product';
import {
    generateChangeset,
    getSimplifiedHistory,
    HistoryEntryWithDescription,
}                                       from '../../lib/productHistory';
import Catalog                          from '../../models/Catalog';
import {
    addProductToCatalog,
    updateCatalogProduct,
}                                       from '../../models/CatalogProduct';
import { ExternalReferenceType }        from '../../models/ExternalReference';
import Product, {
    createProduct,
    linkProducts,
    mergeProductsToExistingProduct,
    unlinkProduct,
    updateProduct,
}                                      from '../../models/Product';
import {PriceTierMethod, RangeMode}    from "@treez/commons/sharedTypings/priceTier";
import PriceTier                       from "../../models/PriceTier";
import CatalogProductChange            from "../../models/CatalogProductChange";
import {CatalogProductAction}          from "../../lib/sharedInterfaces";

const fakeUser: UserInfo = { userAuthId: '<EMAIL>' };

const orgProduct = {
    amount:             1.00,
    attributes:         {
        flavor: ['pine', 'cedar', 'sandlewoood'],
        ingredient: ['alpha', 'chocolate'],
    },
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "Some Org Product",
    ownerId:            55,
    ownerType:          OwnerType.ORG,
    cannabis:           true,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               ProductType.FLOWER,
    uom:                UOM.EACH,
};

const performProductLink = async (orgCatalog: Catalog) => {
    const [
        productToLink,
        brandProductToLinkTo,
    ] = await Promise.all(
        [
            createProduct(
                orgProduct,
                fakeUser,
            ),
            createProduct(
                {
                    ...orgProduct,
                    ownerType: OwnerType.BRAND,
                },
                fakeUser,
            ),
        ]
    );

    const catalogProductLink = await addProductToCatalog(orgCatalog, productToLink.id, fakeUser);

    await linkProducts(productToLink.id, brandProductToLinkTo.id, fakeUser);

    return {
        productToLink,
        brandProductToLinkTo,
        catalogProductLink
    };
}

const performMerge = async (orgCatalog: Catalog) => {

    const mergeFromProductName = 'Merged From Product';
    const mergeToProductName = 'Product Merged To';

    const [
        productToMerge,
        productToMergeTo
    ] = await Promise.all(
        [
            createProduct(
                {
                    ...orgProduct,
                    name: mergeFromProductName
                },
                fakeUser,
            ),
            createProduct(
                {
                    ...orgProduct,
                    name: mergeToProductName
                },
                fakeUser,
            ),
        ]
    );

    await productToMerge.addExternalReference(
        ExternalReferenceType.SELL_TREEZ_ID,
        "xyz1"
    );

    await productToMergeTo.addExternalReference(
        ExternalReferenceType.SELL_TREEZ_ID,
        "xyz2"
    );

    await addProductToCatalog(orgCatalog, productToMerge.id, fakeUser);
    await addProductToCatalog(orgCatalog, productToMergeTo.id, fakeUser);

    await mergeProductsToExistingProduct(
        productToMergeTo.id,
        orgCatalog.id,
        [productToMerge.id],
        fakeUser,
    );

    return {
        productToMerge,
        productToMergeTo,
    }
}

describe('generateChangeset', () => {
    it('computes changes for multiple images', () => {
        const oldProduct = new Product({
            [ProductField.AMOUNT]:  1,
            [ProductField.IMAGES]:  [
                {
                    default:    true,
                    url:        "https://cataas.com/cat?a",
                }
            ]
        } as Partial<Product>);
        const newProduct = new Product({
            ...oldProduct.get(),
            [ProductField.IMAGES]: [
                {
                    default:    false,
                    url:        "https://cataas.com/cat?b",
                },
                {
                    default:    true,
                    url:        "https://cataas.com/cat?d",
                }, {
                    default:    false,
                    url:        "https://cataas.com/cat?c",
                }
            ],
        });

        const changeset = generateChangeset(newProduct.toJSON(), oldProduct.toJSON());

        expect(changeset.length).toBe(1);

        const imageChange = changeset[0];
        expect(imageChange.property).toBe(ProductField.IMAGES);
        expect(imageChange.oldValue).toEqual([
            {
                default:    true,
                url:        "https://cataas.com/cat?a",
            }
        ]);
        expect(imageChange.newValue).toEqual([
            {
                default:    false,
                url:        "https://cataas.com/cat?b",
            },
            {
                default:    true,
                url:        "https://cataas.com/cat?d",
            }, {
                default:    false,
                url:        "https://cataas.com/cat?c",
            }
        ]);
    });
});

describe('getSimplifiedHistory', () => {
    let catalog: Catalog;
    let product: Product;
    let priceTier: PriceTier;

    beforeEach(async () => {(
        [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            PriceTier.create({
                label: "First Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.UNIT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                }],
            }),
        ]));

        await addProductToCatalog(catalog, product.id, fakeUser, {
            price: 10,
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });

    });

    it('presents new active status action as `ACTIVATE_IN_CATALOG`', async () => {

        await updateCatalogProduct(catalog.id, product.id, fakeUser, {
            status: CatalogProductStatus.DEACTIVATED,
        });

        await updateCatalogProduct(catalog.id, product.id, fakeUser, {
            status: CatalogProductStatus.ACTIVE,
        });


        const history = await getSimplifiedHistory(product.id, catalog.id);

        expect(history.length).toBe(3);

        expect(history[0].actionType).toBe(ActionType.ACTIVATE_IN_CATALOG);
        expect(history[0].property).toBeUndefined();

        expect(history[1].actionType).toBe(ActionType.DEACTIVATE_IN_CATALOG);
        expect(history[1].property).toBeUndefined();

        expect(history[2].actionType).toBe(ActionType.ADD_TO_CATALOG);
    });

    it('differentiates changes made in separate catalogs', async () => {
        // given changes from two catalogs and one product,
        const storeCatalog = await Catalog.create({
            name:               "Store Catalog",
            ownerId:            42,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    catalog.id,
        });
        await addProductToCatalog(storeCatalog, product.id, fakeUser, {
            [ProductField.PRICE]:  100,
        });

        await updateCatalogProduct(catalog.id, product.id, fakeUser, {
            [ProductField.PRICE]:  20,
        });
        await updateCatalogProduct(storeCatalog.id, product.id, fakeUser, {
            [ProductField.PRICE]:  120,
        });

        // when I query the history for one catalog
        const history = await getSimplifiedHistory(product.id, storeCatalog.id);

        // then only the respective actions are returned
        expect(history.length).toBe(2);

        expect(history[0]).toMatchObject({
            actionType: ActionType.UPDATE_IN_CATALOG,
            property:   ProductField.PRICE,
            newValue:   '120.000000',
            oldValue:   '100.000000',
        });
        expect(history[1].actionType).toBe(ActionType.ADD_TO_CATALOG);
    });

    it('merges product and catalog changes in order', async () => {
        // given changes to a product and to a catalog product
        await updateProduct(product.id, {
            [ProductField.NAME]:    'NEW NAME',
        }, fakeUser);

        await updateCatalogProduct(catalog.id, product.id, fakeUser, {
            status: CatalogProductStatus.DEACTIVATED,
        });
        await updateProduct(product.id, {
            [ProductField.ECOMMERCE_NAME]:  'Buy me, eh',
        }, fakeUser);

        // when I query the history
        const history = await getSimplifiedHistory(product.id, catalog.id);

        // then both changes to the product as well as the catalog product are returned
        expect(history.length).toBe(4);

        expect(history[0]).toMatchObject({
            actionType: ActionType.UPDATE,
            property:   ProductField.ECOMMERCE_NAME,
            oldValue:   'Z0M&WHY?!',
            newValue:   'Buy me, eh',
        })
        expect(history[1].actionType).toBe(ActionType.DEACTIVATE_IN_CATALOG);
        expect(history[2]).toMatchObject({
            actionType: ActionType.UPDATE,
            property:   ProductField.NAME,
            oldValue:   'ZOMGWHY?!',
            newValue:   'NEW NAME',
        });
        expect(history[3].actionType).toBe(ActionType.ADD_TO_CATALOG);
    });

    it("shows correct message for product created from merge", async () => {
        const [
            productToMerge1,
            productToMerge2
        ] = await Promise.all(
            [
                createProduct(
                    orgProduct,
                    fakeUser,
                ),
                createProduct(
                    orgProduct,
                    fakeUser,
                ),
            ]
        );

        const mergedFromIds = [productToMerge1.id, productToMerge2.id];

        await addProductToCatalog(catalog, productToMerge1.id, fakeUser);
        await addProductToCatalog(catalog, productToMerge2.id, fakeUser);


        const newProduct = await createProduct(
            orgProduct,
            fakeUser,
            mergedFromIds,
        );

        const history = await getSimplifiedHistory(newProduct.id, catalog.id) as HistoryEntryWithDescription[];
        expect(history.length).toBe(1);
        expect(history[0].description).toEqual(
            `Created from merging ${[productToMerge1.name, productToMerge2.name].map(name => `"${name}"`).join(', ')}`
        );
    });

    it("shows correct description for existing product that was merged to", async () => {
        const {
            productToMergeTo,
            productToMerge
        } =  await performMerge(catalog);


        const history = await getSimplifiedHistory(productToMergeTo.id, catalog.id) as HistoryEntryWithDescription[];

        expect(history.some(log => log.actionType === ActionType.MERGE)).toBe(true);

        const mergedLog = history.find(log => log.actionType === ActionType.MERGE && log.newValue == null && log.oldValue == null);

        expect(mergedLog).not.toBeUndefined();
        expect(mergedLog?.description).toBe(
            `Merged from "${productToMerge.name}"`
        )
    });

    it("shows correct description for product that was linked", async () => {
        const {
            productToLink,
            brandProductToLinkTo,
        } = await performProductLink(catalog);

        const history = await getSimplifiedHistory(productToLink.id, catalog.id) as HistoryEntryWithDescription[];

        expect(history.some(log => log.actionType === ActionType.LINK)).toBe(true);

        const linkedLog = history.find(log => log.actionType === ActionType.LINK && log.newValue == null && log.oldValue == null);

        expect(linkedLog).not.toBeUndefined();
        expect(linkedLog?.description).toBe(
            `Linked to "${brandProductToLinkTo.name}"`
        );
    })

    it("shows correct description for product with price tier that was linked", async () => {

        const {
            productToLink,
            brandProductToLinkTo,
            catalogProductLink
        } = await performProductLink(catalog);

        await CatalogProductChange.create({
            actionType: CatalogProductAction.CREATE,
            catalogProductId: catalogProductLink.id,
            changes: [{
                newValue: priceTier.id,
                oldValue: null,
                property: 'productOverrides.priceTierId',
                description: 'override pricetier'
            }],
            newCatalogProduct:  catalogProductLink,
            oldCatalogProduct: catalogProductLink,
            userAuthId: fakeUser.userAuthId,
            sellTreezUserId: fakeUser.sellTreezUserId,
        } as Partial<CatalogProductChange>);

        const history = await getSimplifiedHistory(productToLink.id, catalog.id) as HistoryEntryWithDescription[];

        expect(history.some(log => log.actionType === ActionType.LINK)).toBe(true);

        const linkedLog = history.find(log => log.actionType === ActionType.LINK && log.newValue == null && log.oldValue == null);

        expect(linkedLog).not.toBeUndefined();
        expect(linkedLog?.description).toBe(
            `Linked to "${brandProductToLinkTo.name}"`
        );
    })

    it("shows correct description for product that was unlinked", async () => {
        const {
            productToLink,
            brandProductToLinkTo,
        } = await performProductLink(catalog);

        await unlinkProduct(productToLink.id, fakeUser);

        const history = await getSimplifiedHistory(productToLink.id, catalog.id) as HistoryEntryWithDescription[];

        expect(history.some(log => log.actionType === ActionType.UNLINK)).toBe(true);

        const linkedLog = history.find(log => log.actionType === ActionType.UNLINK && log.newValue == null && log.oldValue == null);

        expect(linkedLog).not.toBeUndefined();
        expect(linkedLog?.description).toBe(
            `Unlinked from "${brandProductToLinkTo.name}"`
        );
    })
});
