import Ajv                              from 'ajv';
import { StatusCodes }                  from 'http-status-codes';
import _, {
    Dictionary
}                                       from 'lodash';
import {
    OwnerType,
    ProductField,
    ProductDetailsField,
}                                       from '@treez/commons/sharedTypings/product';
import {
    AutoIncrement,
    BelongsToMany,
    Column,
    Is,
    PrimaryKey,
    Table,
}                                       from 'sequelize-typescript';
import {
    jsonField,
    intField,
    stringField,
    enumField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import { ErrorResponse }                from '@treez/dev-pack/errors';
import { ValidationType }               from '../lib/sharedInterfaces';
import Catalog                          from './Catalog';
import Product, {
    ProductRecord,
}                                       from './Product';
import ProductRequirementsToCatalogs    from './ProductRequirementsToCatalogs';

type ValidProductField = ProductField | ProductDetailsField;

interface ProductFieldDefinition {
    dataType      : ValidationType,
    options?      : string[] | number[],
    requiredField?: boolean,
}

type ConditionalRequirements = Record<ValidProductField, ProductFieldDefinition>;

interface ProductFieldDefinitionWithConditions extends ProductFieldDefinition {
    conditionalRequirements?: Record<string, ConditionalRequirements>,
};

type RequirementList = Record<ValidProductField, ProductFieldDefinitionWithConditions>;

const validProductFields: ValidProductField[] = _.concat(Object.values(ProductField), Object.values(ProductDetailsField) as ValidProductField[]);

const requirementsSchema = {
    type: "object",
    propertyNames: { enum: validProductFields },
    patternProperties: {
        ".*": {
            type: "object",
            requiredField: {
                type: ValidationType.BOOLEAN,
            },
            dataType: { enum: Object.values(ValidationType) },
            options: {
                type: ValidationType.ARRAY,
                items: { type: ["number", "string"] }
            },
            conditionalRequirements: {
                type: "object",
                patternProperties: {
                    ".*": {
                        type: "object",
                        propertyNames: { enum: validProductFields },
                        patternProperties: {
                            ".*": {
                                dataType: { enum: Object.values(ValidationType) },
                                invalidFields: {
                                    type: ValidationType.ARRAY,
                                    items: { type: ["string"] },
                                    enum: validProductFields
                                },
                                options: {
                                    type: ValidationType.ARRAY,
                                    items: { type: ["number", "string"] }
                                },
                                requiredField: {
                                    type: ValidationType.BOOLEAN,
                                },
                            }
                        }
                    }
                }
            },
        }
    },
}

function isValidRequirement(requirement: RequirementList) {
    const requirementValidation = new Ajv({
        coerceTypes: true
    });
    const validity = requirementValidation.validate(
        requirementsSchema,
        requirement,
    );

    if (validity === false) {
        throw new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            `Requirements did not match proper format ${requirementValidation.errorsText()}`
        );
    }

    return true;
}

/**
 * @swagger
 *  components:
 *      schemas:
 *          ProductRequirements:
 *              description: Requirements to make a product valid or not
 *              properties:
 *                  id:
 *                      title: 'id of the record'
 *                      type: number
 *                  ownerId:
 *                      title: 'entityId of the owner'
 *                      type: number
 *                  ownerType:
 *                      title: 'type of owner'
 *                      type: string
 *                      enum: [brand, organization, store]
 *                  description:
 *                      title: 'description of the requirements'
 *                      type: string
 *                  requirements:
 *                      title: 'requirements JSON object'
 *                      type: object
 *                      description: 'JSON Schema of requirements'
 */
@Table({tableName: 'productRequirements'})
export default class ProductRequirements extends TreezModel<ProductRequirements> {
    @AutoIncrement
    @PrimaryKey
    @Column(intField)
    id:                                     number;

    @Column(intField)
    ownerId:                                number;

    @Column(enumField(OwnerType))
    ownerType:                              OwnerType;

    @Column(stringField)
    description:                            string;

    @Is(isValidRequirement)
    @Column(jsonField)
    requirements:                           RequirementList;

    @BelongsToMany(() => Catalog, () => ProductRequirementsToCatalogs)
    catalogs:                               Catalog[];
}

export const createProductRequirements = async (productRequirements: Partial<ProductRequirements>): Promise<ProductRequirements> => {
    return await ProductRequirements.build(
        productRequirements
    ).save();
}

export const findProductRequirementsById = async (requirementsId: number) => {
    const productRequirements = await ProductRequirements.findByPk(requirementsId);

    if (productRequirements == null) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `No product requirements were found by that id`
        )
    }
    else {
        return productRequirements;
    }
}

export const findProductRequirementsByOwner = async (ownerId: number, ownerType: OwnerType) => {
    return await ProductRequirements.findAll({
        where: {
            ownerId: ownerId,
            ownerType: ownerType,
        },
    });
}

export const updateProductRequirements = async (requirementId: number, requirementUpdate: Partial<ProductRequirements> ) => {
    const productRequirements = await findProductRequirementsById(requirementId);

    const currentRequirements = productRequirements.requirements;

    const editToRequirements = requirementUpdate.requirements;

    const updatedRequirements = {
        ...currentRequirements,
        ...editToRequirements,
    }

    return await productRequirements.setAttributes({
        ...requirementUpdate,
        requirements: updatedRequirements
    }).save();
}

export const validateProductAgainstRequirements = async (product: Partial<ProductRecord> | Product, catalog: Catalog) => {
    const productRequirementsApplied = _.get(catalog, 'productRequirements', []);

    const errorCollection: string[] = []
    if (productRequirementsApplied.length === 0) {
        return true;
    }
    else {
        _.forEach(productRequirementsApplied, ({requirements}) => {
            const requirementValidation = new Ajv({
                coerceTypes: true
            });
            const isvalidProductPerRequirements = requirementValidation.validate(
                mapJsonSchemaOnRequirements(requirements),
                product,
            );

            if (isvalidProductPerRequirements === false) {
                errorCollection.push(requirementValidation.errorsText());
            }

            return isvalidProductPerRequirements;
        });
    }

    if (_.size(errorCollection) > 0) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Product failed validation on catalog requirements ${JSON.stringify(errorCollection)}`
        );
    }

    return true;
}

export const mapJsonSchemaOnRequirements = (
    requirements: RequirementList,
): Dictionary<any> => {
    const jsonSchema: Dictionary<any> = {
        properties: {},
        allOf: [],
        required: [],
    }

    _.forIn(requirements, (requirementDetails, productField) => {
        const {
            dataType,
            options,
            conditionalRequirements,
            requiredField,
        } = requirementDetails;

        const dataTypeToValidate = options ? { enum: options } : { type: dataType }

        if (requiredField) {
            jsonSchema.required.push(productField);
        }

        jsonSchema.properties[productField] = dataTypeToValidate

        if (conditionalRequirements) {

            const conditions = _.keys(conditionalRequirements) as string[];

            _.forEach(conditions, (condition: string) => {
                const conditionBeingConsidered: ConditionalRequirements = conditionalRequirements[condition];

                const conditionalStatement = {
                    "if": {
                        properties: { [productField]: {"const" : condition} }
                    },
                    "then": {
                        properties: {},
                        required: [],
                    }
                } as Dictionary<any>

                _.forIn(conditionBeingConsidered, (
                    conditionalDetailRequirement,
                    conditionallyRequiredField: ValidProductField
                ) => {
                    const {
                        dataType: dataTypeOfCondition,
                        options: optionsOfCondition,
                        requiredField: requiredForCondition,
                    } = conditionalDetailRequirement;


                    if (requiredForCondition) {
                        conditionalStatement.then.required.push(conditionallyRequiredField)
                    }

                    const conditionalDataTypeToValidate = optionsOfCondition ? { enum: optionsOfCondition } : { type: dataTypeOfCondition }

                    conditionalStatement.then.properties[conditionallyRequiredField] = conditionalDataTypeToValidate

                });

                jsonSchema.allOf.push(conditionalStatement);
            });
        }
    });

    return jsonSchema;
}
