import { QueryInterface }   from 'sequelize';

const products = "products";
const uuid = "uuid";

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.addIndex(products, [uuid]);
        await queryInterface.sequelize.query(`
            ALTER TABLE products DROP CONSTRAINT owner_type_owner_id_uuid;
        `)
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            ALTER TABLE products ADD CONSTRAINT owner_type_owner_id_uuid UNIQUE ("ownerType", "ownerId", uuid);
        `)
        await queryInterface.removeIndex(products, [uuid])
    },
}
