import { QueryInterface }       from 'sequelize';
import {
    allowNull,
    jsonField,
    stringField,
}                               from '@treez/dev-pack/db/fieldTypes';

const productChanges        = 'productChanges';
const changes               = 'changes';
const catalogProductChanges = 'catalogProductChanges';
const sellTreezUserId       = 'sellTreezUserId';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.removeColumn(productChanges, changes);
        await queryInterface.removeColumn(catalogProductChanges, changes);

        await queryInterface.removeColumn(productChanges, sellTreezUserId);
        await queryInterface.removeColumn(catalogProductChanges, sellTreezUserId);
    },

    down: async (queryInterface: QueryInterface) => {
        await queryInterface.addColumn(productChanges, changes, jsonField);
        await queryInterface.addColumn(catalogProductChanges, changes, jsonField);

        await queryInterface.addColumn(productChanges, sellTreezUserId, allowNull(stringField));
        await queryInterface.addColumn(catalogProductChanges, sellTreezUserId, allowNull(stringField));
    }
}
