import { QueryInterface } from 'sequelize';

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            DROP INDEX uuid_idx;

            ALTER TABLE products ADD CONSTRAINT owner_type_owner_id_uuid UNIQUE ("ownerType", "ownerId", uuid);
        `)
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            CREATE UNIQUE INDEX uuid_idx ON products (uuid);

            ALTER TABLE products DROP CONSTRAINT owner_type_owner_id_uuid;
        `)
    },
}
