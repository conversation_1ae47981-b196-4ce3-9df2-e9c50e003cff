import _                        from 'lodash';
import { Op }                   from 'sequelize';

import { HistoryEntry }         from '@treez/commons/sharedTypings/history';
import {
    ActionType,
    CatalogProductStatus,
    ProductAttributesDisplayName,
    ProductAttributesField,
    ProductDetailsField,
    ProductField,
}                               from '@treez/commons/sharedTypings/product';
import {
    CATALOG_PRODUCT_ACTIONS,
    PRODUCT_ACTIONS,
}                               from '../constants/messages';
import CatalogProduct           from '../models/CatalogProduct';
import CatalogProductChange     from '../models/CatalogProductChange';
import PriceTier                from '../models/PriceTier';
import Product                  from '../models/Product';
import ProductChange            from '../models/ProductChange';
import { isNilOrEmpty }         from './commonFunctions';
import {
    CatalogProductAction,
    ProductAction,
}                               from './sharedInterfaces';


const SIX_MONTHS_IN_MILLI = 15552000000; // 6 * 30 * 24 * 60 * 60 * 1000
const PRICE_TIER_ID: keyof CatalogProduct = 'priceTierId';
const CATALOG_OVERRIDES: keyof CatalogProduct = 'catalogOverrides';
const PRODUCT_OVERRIDES = 'productOverrides';
const PRODUCT_OVERRIDES_PRICETIERS = PRODUCT_OVERRIDES + "." + PRICE_TIER_ID;

const productActionTypesMap = new Map<ProductAction, ActionType>([
    [ProductAction.CREATE,            ActionType.CREATE],
    [ProductAction.DELETE,            ActionType.DELETE],
    [ProductAction.LINK,              ActionType.LINK],
    [ProductAction.MERGE,             ActionType.MERGE],
    [ProductAction.UNLINK,            ActionType.UNLINK],
    [ProductAction.UPDATE,            ActionType.UPDATE],
]);
const catalogProductActionTypesMap = new Map<CatalogProductAction, ActionType>([
    [CatalogProductAction.CREATE,     ActionType.ADD_TO_CATALOG],
    [CatalogProductAction.DEACTIVATE, ActionType.DEACTIVATE_IN_CATALOG],
    [CatalogProductAction.DELETE,     ActionType.REMOVE_FROM_CATALOG],
    [CatalogProductAction.UPDATE,     ActionType.UPDATE_IN_CATALOG],
]);

/** @deprecated descriptions must be generated on the front end - https://im360us.atlassian.net/browse/STZ-5422 */
export interface HistoryEntryWithDescription extends HistoryEntry {
    /** @deprecated this is being moved to the front-end */
    description: string,
}

/**
 * Generates a human readable description of a change.
 *
 * @param fieldName the human readable field name
 * @param newValue raw new value for the field
 * @param oldValue raw old value for the field
 */
export type DescriptionFormatter = (
    this        : void,
    property    : string,
    newValue?   : any,
    oldValue?   : any,
) => string;

/**
 * Properties for which changes should not be displayed in the simplified history, whether
 * or not they are persisted.
 */
const HIDDEN_PROPERTY_CHANGES: string[] = [
    ProductField.SEARCH_VECTOR,
    ProductField.STATUS,
    // This was mistakenly being recorded and resulted in duplicate history entries,
    // where only CatalogProduct.priceTierId should be considered
    `${CATALOG_OVERRIDES}.${PRICE_TIER_ID}`
];

const formatData = (value: any): string => {
    if (_.isNumber(value)) {
        return value.toString();
    }
    // picks on "42" and "42.5555" - but not "42.", ".42", "a42" or "42b"
    if (_.isString(value) && /^\d+(\.\d+)?$/.test(value)) {
        return Number.parseFloat(value).toString();
    }

    let text = value;
    if (Array.isArray(value)) {
        text = value.join(', ');
    }
    return `'${text}'`;
};

const computeChanges = (
    newValues:          Record<string, any> | undefined | null,
    oldValues:          Record<string, any> | undefined | null,
    getDescription:     DescriptionFormatter,
    propertyPrefix?:    string,
): PropertyChange[] => {
    const properties = _
        .chain(_.keys(newValues))
        .concat(_.keys(oldValues))
        .uniq()
        .value();

    let changes: PropertyChange[] = [];
    for (let property of properties) {
        const newValue = newValues?.[property];
        const oldValue = oldValues?.[property];

        if (
            _.isEqual(newValue, oldValue) ||
            (isNilOrEmpty(newValue) && isNilOrEmpty(oldValue))
        ) {
            continue;
        }

        const qualifiedProperty = propertyPrefix
            ? `${propertyPrefix}.${property}`
            : property;

        if (_.isObject(newValue ?? oldValue) && !_.isArray(newValue ?? oldValue)) {
            changes = changes.concat(computeChanges(
                newValue,
                oldValue,
                getDescription,
                qualifiedProperty,
            ));
            continue;
        }

        const change: PropertyChange = {
            oldValue,
            newValue,
            property: qualifiedProperty,
            description: getDescription(
                qualifiedProperty,
                newValue,
                oldValue,
            ),
        };
        changes.push(change);
    }

    return changes;
};


/** @deprecated this is being moved to the front-end */
export const getProductFieldName = (path: string): string => {
    const [property, type] = path.split('.').reverse();
    let fieldsToProp: {[key: string]: string};
    if (type === 'attributes') {
        fieldsToProp = ProductAttributesField;
    }
    else if (type === 'details') {
        fieldsToProp = ProductDetailsField;
    }
    else {
        fieldsToProp = ProductField;
    }

    const field = _.findKey(fieldsToProp, prop => prop === property);
    return _.get(
        ProductAttributesDisplayName,
        field as string, // undefined is handled but lodash doesn't have an interface for that
        _.startCase(property)
    );
};

/** @deprecated this is being moved to the front-end */
const getImageChangeDescription = (
    newImages?      : any[],
    oldImages?      : any[],
    contextualizer? : string,
): string => {
    const suffix = contextualizer
        ? ` ${contextualizer}`
        : '';
    return `Images changed${suffix}`;
};

/** @deprecated this is being moved to the front-end */
const getStatusChangeDescription = (newStatus: string, contextualizer?: string) => {
    let action = newStatus.toLowerCase();
    if (newStatus === CatalogProductStatus.ACTIVE) {
        action = 'activated';
    } else if (newStatus === CatalogProductStatus.DEACTIVATED) {
        action = 'deactivated';
    } else if (newStatus === CatalogProductStatus.DELETED) {
        action = 'deleted';
    }
    return `Product ${action}` + (contextualizer
        ? ` ${contextualizer}`
        : '');
}

/**
 * Generates a human readable description of a change, indicating what changed, where, and from which value to what.
 *
 * @param property the qualified path of the property that changed
 * @param newValue raw new value
 * @param oldValue raw old value
 * @param contextualizer optional string specifiying where the change took place (e.g.: "in catalog")
 *
 * @deprecated this is being moved to the front-end
 */
export const getChangeDescription = (
    property:       string,
    newValue:       any,
    oldValue:       any,
    contextualizer: string = '',
): string => {
    if (property.endsWith('images')) {
        return getImageChangeDescription(newValue, oldValue, contextualizer);
    }
    if (property.endsWith('status')) {
        return getStatusChangeDescription(newValue, contextualizer)
    }

    let fieldName;
    if (property.endsWith('descriptions.main')) {
        fieldName = ProductAttributesDisplayName.DESCRIPTIONS;
    }
    else {
        fieldName = getProductFieldName(property);
    }

    const contextualizedField = fieldName + (contextualizer
        ? ` ${contextualizer}`
        : '');

    if (_.isBoolean(newValue || oldValue)) {
        return `${newValue ? 'Checked' : 'Unchecked'} ${contextualizedField}`;
    }
    if (!_.isNil(newValue) && !_.isNil(oldValue)) {
        return `${contextualizedField} changed from ${formatData(oldValue)} to ${formatData(newValue)}`;
    }
    if (_.isNil(newValue)) {
        return `${contextualizedField} removed`;
    }
    return `${contextualizedField} set to ${formatData(newValue)}`;
};

/** @deprecated this is being moved to the frontend */
export const getCatalogChangeDescription = (fieldName: string, newValue: any, oldValue: any): string => {
    return getChangeDescription(fieldName, newValue, oldValue, 'in catalog');
};

/**
 * Recursively compares the given versions of the same model and returns an array containing
 * each diverging property describing the change in a human readable format along with new and
 * old values.
 *
 * @param newModel newest version of the model
 * @param oldModel previous version of the model
 * @param getDescription optional function to generate the human readable description of a change
 */
export const generateChangeset = (
    newModel          : Record<string, any> | undefined | null,
    oldModel          : Record<string, any> | undefined | null,
    getDescription?   : DescriptionFormatter,
): PropertyChange[] => {
    return computeChanges(
        newModel,
        oldModel,
        getDescription ?? getChangeDescription,
    );
};

const getActionType = (modelChange: CatalogProductChange | ProductChange): ActionType => {
    let actionType: ActionType | undefined;
    if (modelChange instanceof CatalogProductChange) {
        actionType = catalogProductActionTypesMap.get(modelChange.actionType);
    }
    else {
        actionType = productActionTypesMap.get(modelChange.actionType);
    }

    if (!actionType) {
        throw new Error(`unknown ActionType: ${modelChange.actionType}`);
    }

    if (actionType === ActionType.UPDATE_IN_CATALOG) {
        const isActivation = _.some(modelChange.changes, change => (
            change.property === ProductField.STATUS && change.newValue === CatalogProductStatus.ACTIVE
        ));

        if (isActivation) {
            return ActionType.ACTIVATE_IN_CATALOG;
        }
    }

    return actionType;
};

const getNamesOfProducts = (mergedProductIds: number[], productNamesById: Map<number,string>) => {
    return _(mergedProductIds)
        .map(id => {
            const productName = productNamesById.get(id) as string;
            return productName;
        })
        .value();
}

const resolveProductsRelatedToHistory = async (productChanges: ProductChange[]) => {
    const productIds = _(productChanges)
        .map(productChange => {
            const productIds : number[] = [];
            if (productChange.mergedFrom) {
                productIds.push(...productChange.mergedFrom);
            }

            if (productChange.actionType === ProductAction.LINK && productChange.newProduct != null) {
                productIds.push(productChange.newProduct?.linkedTo as number);
            }

            if (productChange.actionType === ProductAction.UNLINK && productChange.oldProduct != null) {
                productIds.push(productChange.oldProduct?.linkedTo as number);
            }
            return productIds;
        })
        .reject(_.isNull)
        .flatten()
        .uniq()
        .value() as number[];

    const relatedProductMap = new Map<number,string>();

    if (!_.isEmpty(productIds)) {
        const productsToFetch = await Product.findAll({
            where:{
                id :{
                    [Op.in] : productIds
                },
            },
            attributes: [ProductField.NAME, ProductField.ID]
        });
        productsToFetch.forEach(product => relatedProductMap.set(product.id, product.name));
    }

    return relatedProductMap;
}

const getPriceTierLabels = async (productChanges: CatalogProductChange[]) => {
    const tierIds = new Set<number>();

    const collectNewPriceTierId = (changes?: PropertyChange[]) => {
        for (const change of (changes || [])) {
            if (change.property === PRICE_TIER_ID) {
                if (_.isNumber(change.newValue)) {
                    tierIds.add(change.newValue);
                }
                if (_.isNumber(change.oldValue)) {
                    tierIds.add(change.oldValue);
                }
                // there's at most one change to each property
                break;
            }
        }
    };

    for (const {changes} of productChanges) {
        collectNewPriceTierId(changes);
    }

    const relatedPriceTierMap = new Map<number, string>();

    if (!_.isEmpty(tierIds)) {
        const relatedTiers = await PriceTier.findAll({
            where: {
                id: {
                    [Op.in]: Array.from(tierIds)
                },
            },
            attributes: ['id', 'label'],
        });
        relatedTiers.forEach(tier => relatedPriceTierMap.set(tier.id, tier.label));
    }

    return relatedPriceTierMap;
}

const getEnhancedProductDescription = (productAction: ActionType, productNames: string[]) => {
    const quotedProductNames = productNames.map(name => `"${name}"`);
    if (productAction === ActionType.CREATE) {
        return `Created from merging ${quotedProductNames.join(', ')}`;
    }

    if (productAction === ActionType.LINK) {
        return `Linked to ${quotedProductNames.join(', ')}`;
    }

    if (productAction === ActionType.UNLINK) {
        return `Unlinked from ${quotedProductNames.join(', ')}`;
    }

    return `Merged from ${quotedProductNames.join(', ')}`;
}

/**
 * Parses `ProductChange`s and optionally `CatalogProductChange`s into a list
 * of history entries containing a human readable description, timestamp and
 * change author, sorted from most to least recent.
 *
 * @param productId id of the product which history is to be retrieved and
 * parsed
 * @param catalogId if specified, includes the product changes made to the
 * catalog product as well
 */
export const getSimplifiedHistory = async (
    productId   : number,
    catalogId?  : number,
): Promise<HistoryEntry[]> => {
    const commonAttributes: Array<keyof CatalogProductChange | keyof ProductChange> = [
        'actionType',
        'changes',
        'createdAt',
        'userAuthId',
        'sellTreezUserId'
    ];
    let modelChanges: (CatalogProductChange | ProductChange)[] = [];

    const sixMonthsAgo = new Date();
    sixMonthsAgo.setTime(Date.now() - SIX_MONTHS_IN_MILLI);
    const whereClause = {
        createdAt: {
            [Op.gte]: sixMonthsAgo,
        },
    };

    let catalogProductChanges: CatalogProductChange[] = [];
    if (_.isFinite(catalogId)) {
        catalogProductChanges = await CatalogProductChange.findAll({
            where: whereClause,
            include: [{
                model: CatalogProduct,
                where: {
                    catalogId: catalogId as number,
                    productId,
                }
            }],
            attributes: commonAttributes,
        });
    }

    const productChanges = await ProductChange.findAll({
        where: {
            ...whereClause,
            productId,
        },
        attributes: [
            ...commonAttributes,
            'mergedFrom',
            'newProduct',
            'oldProduct',
        ],
    });

    const [relatedPriceTiersMap, relatedProductsMap] = await Promise.all([
        getPriceTierLabels(catalogProductChanges),
        resolveProductsRelatedToHistory(productChanges)
    ]);

    modelChanges = (catalogProductChanges as typeof modelChanges).concat(productChanges);

    const sortedModelChanges = _(modelChanges)
        .sortBy('createdAt')
        .reverse()
        .value();

    const flattenedHistory : HistoryEntry[] = [];

    for (const modelChange of sortedModelChanges) {
        const {
            actionType: internalActionType,
            userAuthId,
            sellTreezUserId,
            createdAt: timestamp,
            changes,
        } = modelChange;


        const mergedFrom = _.get(modelChange, "mergedFrom") as number[] | undefined;
        const newProduct = _.get(modelChange, "newProduct") as Partial<Product> | undefined;
        const oldProduct = _.get(modelChange, "oldProduct") as Partial<Product> | undefined;

        const exposedActionType = getActionType(modelChange);

        let expandedChangeHistory: HistoryEntryWithDescription[] = _(changes)
            .filter(change => !HIDDEN_PROPERTY_CHANGES.includes(change.property))
            .map((change): HistoryEntryWithDescription => {
                let {
                    newValue,
                    oldValue,
                    property,
                    description,
                } = change;
                const relatedEntities: HistoryEntry['relatedEntities'] = {};

                if (property === PRICE_TIER_ID || property === PRODUCT_OVERRIDES_PRICETIERS) {
                    const relatedPriceTiers = [];
                    if (newValue) {
                        relatedPriceTiers.push({
                            id: newValue,
                            label: relatedPriceTiersMap.get(newValue)!,
                        });
                    }
                    if (oldValue) {
                        relatedPriceTiers.push({
                            id: oldValue,
                            label: relatedPriceTiersMap.get(oldValue)!,
                        });
                    }
                    relatedEntities.priceTiers = relatedPriceTiers;
                }

                return {
                    actionType: exposedActionType,
                    // description has been deprecated. Will be moved to the frontend.
                    description: description,
                    newValue: newValue,
                    oldValue: oldValue,
                    property: property,
                    sellTreezUserId: sellTreezUserId || undefined,
                    timestamp,
                    userAuthId,
                    relatedEntities: _.isEmpty(relatedEntities)
                        ? undefined
                        : relatedEntities,
                };
            })
            .value();

        if (exposedActionType !== ActionType.UPDATE &&
            exposedActionType !== ActionType.UPDATE_IN_CATALOG
        ) {
            let description = '';
            let newEntry = {
                actionType: exposedActionType,
                sellTreezUserId: sellTreezUserId || undefined,
                timestamp,
                userAuthId,
            };

            if (
                [ActionType.MERGE, ActionType.CREATE].includes(exposedActionType)
                && modelChange instanceof ProductChange
                && mergedFrom != null
            ) {
                // TODO: move description generation to frontend and append related entities to response
                const mergedProductNames = getNamesOfProducts(mergedFrom, relatedProductsMap);
                description =  getEnhancedProductDescription(exposedActionType, mergedProductNames);
            }
            else if (
                [ActionType.LINK, ActionType.UNLINK].includes(exposedActionType)
                && modelChange instanceof ProductChange
                && newProduct != null
                && oldProduct != null
            ) {
                let linkedProductNames = exposedActionType === ActionType.LINK ?
                    getNamesOfProducts([newProduct.linkedTo!], relatedProductsMap) : getNamesOfProducts([oldProduct.linkedTo!], relatedProductsMap);

                // TODO: move description generation to frontend and append related entities to response
                description = getEnhancedProductDescription(exposedActionType, linkedProductNames);
            }
            else {
                description = _.get(
                    modelChange instanceof CatalogProductChange
                        ? CATALOG_PRODUCT_ACTIONS
                        : PRODUCT_ACTIONS,
                    internalActionType,
                    internalActionType, // default text
                ) as string;
            }

            expandedChangeHistory.push({
                ...newEntry,
                description,
            });
        }

        flattenedHistory.push(...expandedChangeHistory);
    }

    return flattenedHistory;
};
