import { QueryInterface } from 'sequelize';

export = {
  up: async function (queryInterface: QueryInterface) {
    await queryInterface.sequelize.query(`
        CREATE INDEX IF NOT EXISTS catalogProducts_productId_index ON "catalogProducts" ("productId" ASC);
    `);
  },

  down: async function (queryInterface: QueryInterface) {
    await queryInterface.sequelize.query(`
        DROP INDEX IF EXISTS catalogProducts_productId_index;
    `);
  },
};
