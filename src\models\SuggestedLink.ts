import { StatusCodes }                          from 'http-status-codes';
import _                                        from 'lodash';
import {
    AutoIncrement,
    BelongsTo,
    Column,
    ForeignKey,
    PrimaryKey,
    Table,
    Is,
}                                               from 'sequelize-typescript';
import { OwnerType }                            from '@treez/commons/sharedTypings/product';
import {
    connection,
    transaction,
}                                               from '@treez/dev-pack/db';
import {
    decimalField,
    enumField,
    intField,
}                                               from '@treez/dev-pack/db/fieldTypes';
import TreezModel                               from '@treez/dev-pack/models/TreezBaseModel';
import { ErrorResponse }                        from '@treez/dev-pack/errors';
import weightedLinksCreationQuery               from '../db/queries/weightedLinksCreation';
import Product, {
    findProductById,
}                                               from './Product';

export enum MatchType {
    BrandSuggested = 'BrandSuggested',
    Weighted       = 'Weighted',
    Rejected       = 'Rejected',
}

export const validOwnerType = async (
    validOwnerTypes: OwnerType[],
    fieldBeingChecked: string,
    submittedProductId: number
) => {
    const product = await findProductById(submittedProductId);

    if (!_.includes(validOwnerTypes, product.ownerType)) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Invalid owner type for ${fieldBeingChecked}, has owner type of ${product.ownerType}, needs to be of type ${validOwnerTypes}`
        )
    }
}

@Table({tableName: 'suggestedLinks'})
export default class SuggestedLink extends TreezModel<SuggestedLink> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                                 number;

    //Retail Product... either a store or organization product
    @Is('retail product', async (productToLinkId: number) => {
        await validOwnerType([OwnerType.STORE, OwnerType.ORG], 'productToLinkId', productToLinkId)
    })
    @ForeignKey(() => Product)
    @Column(intField)
    productToLinkId:                    number;

    //Brand product the retail product was matched to
    @Is('brand product', async (brandProductId: number) => {
        await validOwnerType([OwnerType.BRAND], 'brandProductId', brandProductId)
    })
    @ForeignKey(() => Product)
    @Column(intField)
    brandProductId:                     number;

    //Either weighted from query or brand suggested
    @Column(enumField(MatchType))
    type:                               MatchType;

    //the total between the brand name match and the product match
    @Column(decimalField)
    rankWeight:                         number;

    @BelongsTo(() => Product)
    productToLink:                      Product;

    @BelongsTo(() => Product)
    brandProduct:                       Product;

    static refreshWeightedMatches = async () => {
        await transaction(async () => {
            await SuggestedLink.destroy({
                where: {
                    type: MatchType.Weighted,
                }
            });

            await connection.query(weightedLinksCreationQuery)
        });
    }

    static addBrandSuggestedLink = async (
        brandProductId: number,
        productToLinkId: number,
    ): Promise<SuggestedLink> => {
        const [insertedBrandMatch] = await transaction(async () => {
            //see if the retail product already has a suggested brand match,
            const existingBrandMatch = await SuggestedLink.findOne({
                where: {
                    productToLinkId,
                    type: MatchType.BrandSuggested
                }
            });

            //if a brand suggested match for this product already exists, throw an error.
            if (existingBrandMatch != null) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    `There is already an existing brand suggested match for this product`
                );
            }

            //destroy any weighted matches, to be replaced by the brand suggested match
            return await Promise.all([
                SuggestedLink.create({
                    brandProductId,
                    productToLinkId,
                    type: MatchType.BrandSuggested,
                }),
                SuggestedLink.destroy({
                    where: {
                        productToLinkId,
                        type: [MatchType.Weighted],
                    }
                }),
            ]);
        });

        return insertedBrandMatch;
    }

    static findOrThrow = async (id: number) => {
        const suggestedLink = await SuggestedLink.findByPk(id);

        if(suggestedLink == null) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Suggested Link could not be found`
            )
        }

        return suggestedLink;
    }

    static rejectMatch = async (id: number) => {
        const suggestedLink = await SuggestedLink.findOrThrow(id);

        if (suggestedLink.type !== MatchType.Weighted) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Can only destroy weighted matches generated by Treez, Brand Suggested matches must be replaced with another`
            );
        }
        else {
            await suggestedLink.update({
                type: MatchType.Rejected
            });
        }
    }

    static editSuggestedLink = async (id: number, edits: Partial<SuggestedLink>) => {
        const suggestedLink = await SuggestedLink.findOrThrow(id);

        return await suggestedLink.update(edits);
    }

    static getProductMatches = async (productIds: number[]) => {
        const matches = await SuggestedLink.findAll({
            where: {
                type: MatchType.BrandSuggested,
                productToLinkId: productIds,
            }
        });

        return productIds.reduce((productMatchRecord, productId) => {
            const suggestedProductMatch = matches.find((match) => {
                return match.productToLinkId === productId;
            });

            return {
                [productId]: suggestedProductMatch,
                ...productMatchRecord,
            }
        }, {});
    }
}
