import _                        from 'lodash';
import {
    AutoIncrement,
    Column,
    ForeignKey,
    PrimaryKey,
    Table,
    BelongsTo,
}                               from 'sequelize-typescript';
import {
    intField,
    stringField,
    notNull,
    enumField,
}                               from '@treez/dev-pack/db/fieldTypes';
import { TreezModel }           from '@treez/dev-pack/models/TreezBaseModel';
import Product                  from './Product';

export enum ExternalReferenceType {
    SELL_TREEZ_ID                   = 'sellTreezId',
    WEED_MAPS_PRODUCT_VARIANT_ID    = 'weedMapsProductVariantId',
}

const {
    SELL_TREEZ_ID,
    WEED_MAPS_PRODUCT_VARIANT_ID
} = ExternalReferenceType;

@Table({tableName: 'externalReferences'})
export default class ExternalReference extends TreezModel<ExternalReference> {
    @AutoIncrement
    @PrimaryKey
    @Column(intField)
    id:                                 number;

    @Column(notNull(enumField(ExternalReferenceType)))
    type:                               ExternalReferenceType;

    @Column(notNull(stringField))
    externalId:                         string;

    @ForeignKey(() => Product)
    @Column(intField)
    productId:                          number;

    @BelongsTo(() => Product)
    product:                            Product;

    static findProductIds = async (
        externalIds: string[],
    ) => {
        const externalReferences = await ExternalReference.findAll({
            where: {
                externalId: externalIds,
                type      : SELL_TREEZ_ID,
            }
        });

        return externalReferences.map(er => er.productId);
    }

    public replaceProductReferencesForMerge = async (newProductIdToReference: number) => {
        const reference: ExternalReference = this;

        return await reference.update({
            productId: newProductIdToReference,
            type     : SELL_TREEZ_ID,
        });
    }
}

export const findSellTreezId = async (productId: number) => {
    const sellTreezReference = ExternalReference.findOne({
        where: {
            productId,
            type: ExternalReferenceType.SELL_TREEZ_ID
        }
    });

    return _.get(sellTreezReference, 'externalId', null);
}

export const findWeedMapsProductVariantId = async (productId: number) => {
    const weedMapsProductVariantReference = await ExternalReference.findOne({
        where: {
            productId,
            type: WEED_MAPS_PRODUCT_VARIANT_ID,
        }
    });

    return _.get(weedMapsProductVariantReference, 'externalId', null);
}
