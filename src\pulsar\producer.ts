import { memoize }              from 'lodash/fp';
import config                   from 'config';
import { getPulsarClient }      from './client';

export enum TypeOfProductChanges {
    UPDATE = 'update',
    MERGE  = 'merge',
}

export interface ProductUpdateMessage {
    affectedCatalogIds  : number[];
    affectedProductIds  : number[];
    affectedSellTreezIds: string[];
    changedProductId    : number;
    changeType          : TypeOfProductChanges
}

const getProductUpdateProducer = memoize(() => {
    const client = getPulsarClient();

    return client.createProducer({
        compressionType: config.pulsar.compression,
        topic: config.pulsar.topic.productUpdate,
    });
});

export const sendProductUpdateMessage = async (
    productUpdateMessage: ProductUpdateMessage,
    messageId: number,
) => {
    const producer = await initPulsar();

    let before, after;
    before = new Date().getTime();

    await producer.send({
        data: Buffer.from(JSON.stringify(productUpdateMessage)),
        properties: {
            messageId: messageId.toString()
        }
    });

    after = new Date().getTime();
    console.info(`Done sending message to producer, messageId: ${messageId}. Total time in millis: ${after - before}`);
}

export const initPulsar = async () => {
    let before, after;
    before = new Date().getTime();

    const producer = await getProductUpdateProducer();

    after = new Date().getTime();
    console.info(`Finished initializing pulsar. Total time in millis: ${after - before}`);

    return producer;
};