import _                                        from 'lodash';
import {
    OwnerType,
    UOM,
}                                               from '@treez/commons/sharedTypings/product';
import Catalog                                  from '../../models/Catalog';
import Product                                  from '../../models/Product';
import {
    addProductToCatalog,
    CatalogProductStatus
}                                               from '../../models/CatalogProduct';

export const setupProductsAndCatalogs = async (productsData: Array<Partial<Omit<Product, 'amount'> & { amount: string }>>, catalogsData: Array<Partial<Omit<Product, 'amount'> & { amount: string }>> = [], combine = true) => {
    let catalogs: Array<Catalog> = [];

    for (const data of catalogsData) {
        const catalog = await Catalog.create(data);
        catalogs.push(catalog);
    }

    let products: Product[] = [];
    for (const data of productsData) {
        const product = await Product.create(data);
        if ( combine === true ) {
            for (const catalog of catalogs) {
                await addProductToCatalog(
                    catalog,
                    product.id,
                    { userAuthId: '<EMAIL>' },
                    {
                        price: _.get(data, 'price', null),
                        status: _.get(data, 'status', CatalogProductStatus.ACTIVE),
                    }
                );
            }
        }
        products.push(product);
    }

    return {
        catalogs,
        products,
    }
}

export const brandCatalog = {
    name:               "Test Brand Catalog",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
}

export const brandCatalog2 = {
    name:               "Test Brand Catalog 2",
    ownerId:            7225,
    ownerType:          OwnerType.BRAND,
}

export const orgCatalog = {
    name:               "Test Org Catalog",
    ownerId:            55,
    ownerType:          OwnerType.ORG,
}

export const storeCatalog = {
    name:               "Test Store Catalog",
    ownerId:            42,
    ownerType:          OwnerType.STORE,
}

export const brandProduct: Partial<Omit<Product, 'amount'> & { amount: string }> = {
    amount:             "1.000000",
    attributes:         {
        flavor: ['pine', 'cedar', 'sandlewoood'],
        ingredient: ['alpha', 'chocolate'],
    },
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "ZOMGWHY?!",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    cannabis:           true,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    uom:                UOM.EACH,
}

export const brandProducts = [{
    amount:             "1.000000",
    attributes:         {general: ["TEST"]},
    brandName:          "Treez Tester Nonsense",
    barcodes:           [{sku: "SKUXOZEHXKS1"}],
    details:            {for_test: "a detail"},
    eCommerceName:      "Absolute Nonsense",
    name:               "Absolute Nonsense",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "10 piece bucket",
    sku:                "SKUXOZEHXKS1",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure1",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "A bit of Nonsense",
    name:               "A bit of Nonsense",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "25 piece bucket",
    sku:                "SKUXOZEHXKS2",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure2",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "Blue Gummies",
    name:               "Blue Gummies",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS3",
    subtype:            "Modest Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure3",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "ZOMGWHY?!",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "100 piece bucket",
    sku:                "SKUXOZEHXKS4",
    subtype:            "Filler",
    type:               "Nonsense",
    tpc:                "YeahOkSure4",
    uom:                UOM.EACH,
}];

export const storeProduct = {
    amount:             "1.000000",
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "eComStoreProduct",
    externalId:         "8675309",
    name:               "StoreProductName",
    ownerId:            storeCatalog.ownerId,
    ownerType:          OwnerType.STORE,
    packageTracked:     false,
    cannabis:           true,
    size:               "50",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    uom:                UOM.EACH,
}

export const orgProduct = {
    amount:             "1.000000",
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Org Tester",
    eCommerceName:      "eComOrgProduct",
    externalId:         "123456",
    name:               "OrgProductName",
    ownerId:            7224,
    ownerType:          OwnerType.ORG,
    packageTracked:     false,
    cannabis:           true,
    size:               "50",
    sku:                "SKUXOZEHXKS",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    uom:                UOM.EACH,
}

export const MigratePriceTierPayload_MOCK_SINGLE = [{
    "priceTier": {
        "label": "60",
        "ownerId": 11,
        "ownerType": "store",
        "isActive": true,
        "method": "WEIGHT",
        "thresholdType": "FLAT",
        "rangeMode": "RANGE",
        "thresholds": [
            {
                "value": 16.475973,
                "start": 1.000000,
                "end": 1.750000,
                "tolerance": 0.000000
            },
            {
                "value": 15.691402,
                "start": 14.000000,
                "end": 28.000000,
                "tolerance": 0.000000
            },
            {
                "value": 15.691402,
                "start": 3.500000,
                "end": 7.000000,
                "tolerance": 0.000000
            },
            {
                "value": 16.475973,
                "start": 0.000000,
                "end": 1.000000,
                "tolerance": 0.000000
            },
            {
                "value": 15.691402,
                "start": 28.000000,
                "tolerance": 0.000000
            },
            {
                "value": 15.691402,
                "start": 7.000000,
                "end": 14.000000,
                "tolerance": 0.000000
            },
            {
                "value": 15.691402,
                "start": 1.750000,
                "end": 3.500000,
                "tolerance": 0.000000
            }
        ]
    },
    "productIds": [
        "0cd59568-fd80-11e6-8b60-b8aeedec043f"
    ]
}];

export const MigratePriceTierPayloads_MOCK_MULTIPLE = [
    {
        "priceTier": {
            "label": "60",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 16.475973,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.475973,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "50",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 13.076169,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.076169,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.076169,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.729977,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.076169,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.076169,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 13.729977,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "45",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 11.899314,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 11.768552,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 11.899314,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 12.030075,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 11.768552,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 11.768552,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 11.768552,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "40",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 10.460935,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.983982,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.460935,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.983982,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.460935,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.460935,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 10.460935,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "35",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 9.153318,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 9.153318,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 9.153318,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 9.153318,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 9.153318,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 8.891795,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 9.153318,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "30",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 7.845701,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 7.845701,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 8.237986,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 7.845701,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 8.237986,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 7.845701,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 7.845701,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                }
            ]
        },
        "productIds": [
            "0cd59568-fd80-11e6-8b60-b8aeedec043f"
        ]
    },
    {
        "priceTier": {
            "label": "25",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 6.538084,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.538084,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.538084,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.407323,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.276561,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.538084,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 6.407323,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "20",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 5.230467,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.230467,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.491991,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.230467,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.491991,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.230467,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 5.230468,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "65",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 16.999019,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.999019,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 18.306636,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 18.306636,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.999019,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.999019,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 18.306636,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                }
            ]
        }
    },
    {
        "priceTier": {
            "label": "55",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 14.645309,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.383786,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.645309,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.383786,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.383786,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.645309,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 14.383785,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                }
            ]
        },
        "productIds": [
            "858afc65-fd81-11e6-8b60-b8aeedec043f"
        ]
    }
]
