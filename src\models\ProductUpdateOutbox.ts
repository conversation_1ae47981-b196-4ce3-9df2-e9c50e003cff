import _                                from 'lodash';
import Sequelize                        from 'sequelize'
import {
    AutoIncrement,
    Column,
    PrimaryKey,
    Table,
    AfterCreate,
}                                       from 'sequelize-typescript';
import { OwnerType }                    from '@treez/commons/sharedTypings/product';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import {
    booleanField,
    intField,
    jsonField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import logger                           from '@treez/dev-pack/logger';
import CatalogProduct                   from './CatalogProduct';
import ExternalReference, {
    ExternalReferenceType,
}                                       from './ExternalReference';
import Product                          from './Product';
import {
    ProductUpdateMessage,
    sendProductUpdateMessage,
    TypeOfProductChanges,
}                                       from '../pulsar/producer';

@Table({tableName: 'productUpdateOutbox'})
export default class ProductUpdateOutbox extends TreezModel<ProductUpdateOutbox> {
    @AutoIncrement
    @PrimaryKey
    @Column(intField)
    id:                     number;

    @Column(jsonField)
    payload:                ProductUpdateMessage;

    @Column(booleanField)
    published:              boolean;

    static createRecord = async (changedProductId: number, ownerType: OwnerType, changeType: TypeOfProductChanges): Promise<void> => {
        const affectedProductIds = await ProductUpdateOutbox.getAffectedProductIds(changedProductId, ownerType, changeType);

        if (_.isEmpty(affectedProductIds)) {
            return;
        }

        const affectedCatalogIds = _.chain(await CatalogProduct.findAll({
            where: {
                productId: {
                    [Sequelize.Op.or]: affectedProductIds
                }
            },
            attributes: ['catalogId']
        }))
        .map('catalogId')
        .uniq()
        .value();

        const affectedSellTreezIds =  _.chain(await ExternalReference.findAll({
            where: {
                productId: {
                    [Sequelize.Op.or]: affectedProductIds,
                },
                type: ExternalReferenceType.SELL_TREEZ_ID
            },
            attributes: ['externalId']
        }))
        .map('externalId')
        .uniq()
        .value();

        const payload = {
            changedProductId,
            affectedProductIds,
            affectedCatalogIds,
            affectedSellTreezIds,
            changeType,
        }

        await ProductUpdateOutbox.create({
            payload,
            published: false,
        });
    }

    static getAffectedProductIds = async (changedProductId: number, ownerType: OwnerType, changeType: TypeOfProductChanges) => {
        if (ownerType === OwnerType.BRAND) {
            return _.concat(await Product.getProductIdsLinkedToProduct(changedProductId), [changedProductId]);
        }
        else {
            if (TypeOfProductChanges.MERGE === changeType) {
                return await Product.getProductIdsMergedToProduct(changedProductId);
            }
            else {
                return [changedProductId];
            }
        }
    }

    static publishUnpublishedMessages = async () => {
        const unpublishedMessages = await ProductUpdateOutbox.findAll({
            where: {
                published: false,
            }
        });

        for (const unpublishedMessage of  unpublishedMessages) {
            await ProductUpdateOutbox.publishRecord(unpublishedMessage);
        }
    }

    @AfterCreate
    static publishRecord = async (instance: ProductUpdateOutbox, options?: Sequelize.InstanceUpdateOptions) => {
        try {
            await sendProductUpdateMessage(instance.payload, instance.id);

            await instance.update({
                published: true,
            }, {
                transaction: options?.transaction
            })
        }
        catch (error) {
            logger.error(`Error publishing product update message # ${instance.id}`, error)
        }
    }
}
