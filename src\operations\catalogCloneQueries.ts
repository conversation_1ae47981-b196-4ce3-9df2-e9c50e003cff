
/**
 * Query to find all price tiers that belongs to a particular catalog, paginated
 * 
 * @param catalogId
 * @param limit
 * @param offset
*/
export const priceTiersExportQuery: string = `select
pt."id",
pt."label",
pt."ownerId",
pt."ownerType",
pt."isActive",
pt."method",
pt."rangeMode",
pt."thresholdType",
pt."thresholds",
pt."createdAt",
pt."updatedAt",
cpt."id" as "catalogPriceTiers.id",
cpt."catalogId" as "catalogPriceTiers.catalogId",
cpt."priceTierId" as "catalogPriceTiers.priceTierId",
cpt."priceTierOverrides" as "catalogPriceTiers.priceTierOverrides",
cpt."createdAt" as "catalogPriceTiers.createdAt",
cpt."updatedAt" as "catalogPriceTiers.updatedAt"
from
"priceTiers" pt
inner join "catalogPriceTiers" cpt on
pt.id = cpt."priceTierId"
where
cpt."catalogId" = :catalogId
limit :limit offset :offset;`;

/**
 * Query to find all products that belogs to a particular catalog
 * 
 * @param catalogId
 * @param limit
 * @param offset
*/
export const productExportQuery = `
select
	p."id",
	p."amount",
	p."attributes",
	p."barcodes",
	p."brandName",
	p."cannabis",
	p."classification",
	p."displayName",
	p."descriptions",
	p."details",
	p."eCommerceName",
	p."externalId",
	p."images",
	p."linkedTo",
	p."mergedTo",
	p."msrp",
	p."name",
	p."ownerId",
	p."ownerType",
	p."productOverrides",
	p."packageTracked",
	p."productShortCode",
	p."size",
	p."sku",
	p."searchVector",
	p."subtype",
	p."tpc",
	p."type",
	p."uom",
	p."upc",
	p."visible",
	p."createdAt",
	p."updatedAt",
	cp."id" as "catalogProducts.id",
	cp."catalogId" as "catalogProducts.catalogId",
	cp."catalogOverrides" as "catalogProducts.catalogOverrides",
	cp."price" as "catalogProducts.price",
	cp."productId" as "catalogProducts.productId",
	cp."status" as "catalogProducts.status",
	cp."priceTierId" as "catalogProducts.priceTierId",
	cp."createdAt" as "catalogProducts.createdAt",
	cp."updatedAt" as "catalogProducts.updatedAt"
from 
	products p
join "catalogProducts" cp on p.id = cp."productId"
where cp.status != 'DELETED' and cp."catalogId" = :catalogId limit :limit offset :offset;
`;

/**
 * find all external references given product ids
 * 
 * @param catalogId
 * @param limit
 * @param offset
*/
export const externalReferenceExportQuery = `
select
	er."productId",
	er."externalId",
	er."type"
from jsonb_array_elements_text('[:productIds]') js 
join "externalReferences" er on js.value = cast (er."productId" as VARCHAR);
`;


/**
 * validates that there is no external references with externalId we want to use 
 * 
*/
export const  validateExternalReferenceIdQuery = `
select
	count(js.value)
from
	jsonb_array_elements_text(:externalIds) js
join "externalReferences" er on
	js.value = er."externalId"
join "catalogProducts" cp on
	cp."productId" = er."productId"
where
	cp."catalogId" = :catalogId;
`;