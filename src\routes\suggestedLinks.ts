import { sendErrorResponse }    from '@treez/dev-pack/errors';
import logger                   from '@treez/dev-pack/logger';
import {
    Request,
    Response,
    Router,
}                               from 'express';
import { StatusCodes }          from 'http-status-codes';
import { requestorIsTreez }     from '../middleware';
import SuggestedLink            from '../models/SuggestedLink';

const router = Router();

//Brand or Retailer
router.delete('/:id', async (
    response: Response,
    request: Request,
) => {
    const { id } = request.params;

    try {
        await SuggestedLink.rejectMatch(Number(id));

        response.status(StatusCodes.NO_CONTENT).send();
    }
    catch(error) {
        logger.error(`Error deleting suggested link ${id}`, error);
        sendErrorResponse(response, error)
    }
});

//Brand is only one that can change
router.patch(`/:id`, async (
    response: Response,
    request: Request,
) => {
    const { id } = request.params;

    try {
        const updatedLink = SuggestedLink.editSuggestedLink(Number(id), request.body);

        response.status(StatusCodes.ACCEPTED).send(updatedLink);
    }
    catch(error){
        logger.error(`Error editing suggested link id ${id}`, error);
        sendErrorResponse(response, error)
    }
});


router.put(`/weightedLinks`,
requestorIsTreez,
async (response: Response, request: Request) => {
    try {
        await SuggestedLink.refreshWeightedMatches();

        response.status(StatusCodes.ACCEPTED);
    }
    catch(error){
        logger.error(`Error refreshing suggested link table with updated weighted matches`, error);
        sendErrorResponse(response, error);
    }
});
