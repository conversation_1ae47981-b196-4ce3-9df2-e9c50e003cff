import { QueryInterface }               from 'sequelize';
import {
    autoPKField,
    intField,
    stringField,
    foreign<PERSON>ey,
    createUpdateColumns,
    jsonField,
    enumField,
    notNull,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { CatalogProductAction }         from '../../lib/sharedInterfaces';

const catalogProductChanges = 'catalogProductChanges'
const catalogProductId      = 'catalogProductId';
const catalogProducts       = 'catalogProducts';
const id                    = 'id';
const productChanges        = 'productChanges';
const version               = 'version';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.createTable(catalogProductChanges, {
            id               : autoPKField,
            actionType       : enumField(CatalogProductAction),
            catalogProductId : foreignKey(intField, catalogProducts, id),
            newCatalogProduct: notNull(jsonField),
            oldCatalogProduct: json<PERSON>ield,
            userAuthId       : notNull(stringField),
            version          : notNull(intField),
            ...createUpdateColumns,
        });

        await queryInterface.addIndex(catalogProductChanges, [catalogProductId]);

        await queryInterface.addColumn(productChanges, version, intField)
    },
    down: async function (queryInterface: QueryInterface) {
        await queryInterface.removeColumn(productChanges, version)

        await queryInterface.removeIndex(catalogProductChanges, catalogProductId);

        await queryInterface.dropTable(catalogProductChanges);
    }
}
