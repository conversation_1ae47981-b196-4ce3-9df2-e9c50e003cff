import { QueryTypes } from 'sequelize';
import _ from "lodash";

import Catalog from "../models/Catalog";
import PriceTier from "../models/PriceTier";
import Product from "../models/Product";
import { ErrorResponse } from '@treez/dev-pack/errors';
import { StatusCodes } from 'http-status-codes';
import { connection } from '@treez/dev-pack/db';
import { externalReferenceExportQuery, priceTiersExportQuery, productExportQuery } from './catalogCloneQueries';
import ExternalReference from '../models/ExternalReference';
import CatalogProduct from '../models/CatalogProduct';
import CatalogPriceTier from 'models/CatalogPriceTier';

export type ExportProductType = (Omit<Product, 'catalogProducts'> & {
    catalogProducts: CatalogProduct
})[];

export type ExportPriceTiersType = (Omit<PriceTier, 'catalogPricerTiers'> & {
    catalogPriceTiers: CatalogPriceTier
})[];

export type CatalogExportData = Partial<{
    catalog: Partial<Catalog>;
    products: ExportProductType;
    priceTiers: ExportPriceTiersType;
    externalReferences: ExternalReference[];
}>



const fetchCatalogById = async (id: number): Promise<Catalog> => {
    const catalog = await Catalog.findOne({
        where: {
            id
        }
    });
    if (_.isNull(catalog)) {
        throw new ErrorResponse(StatusCodes.NOT_FOUND, "catalog not found")
    }
    return catalog;
}

const fetchPriceTierByCatalog = async (catalogId: number, limit: number, offset: number): Promise<PriceTier[]> => {
    return await connection.query(priceTiersExportQuery, {
        model: PriceTier,
        nest: true,
        mapToModel: true,
        raw: true,
        replacements: { limit, offset, catalogId },
        type: QueryTypes.SELECT
    });
}

const fetchProductsByCatalog = async (catalogId: number, limit: number, offset: number): Promise<Product[]> => {
    return await connection.query(productExportQuery, {
        model: Product,
        nest: true,
        mapToModel: true,
        raw: true,
        replacements: { limit, offset, catalogId },
        type: QueryTypes.SELECT
    })
}


const fetchExternalReferences = async (productIds: number[]): Promise<ExternalReference[]> => {
    return await connection.query(externalReferenceExportQuery, {
        model: ExternalReference,
        type: QueryTypes.SELECT,
        replacements: { productIds }
    })
}

const DEFAULT_LIMIT = 5000

export async function* productsGenerator(catalogId: number, limit: number) {
    let products: Product[] = [];
    let externalReferences: ExternalReference[] = [];

    for (let offset = 0; ; offset += limit) {
        products = await fetchProductsByCatalog(catalogId, limit, offset);
        if (products.length !== 0) {
            externalReferences = await fetchExternalReferences(products.map(product => product.id))
        } else {
            externalReferences = [];
            break;
        }
        yield { products, externalReferences }
    }
};

export async function* priceTierGenerator(catalogId: number, limit: number) {

    let priceTiers: PriceTier[] = [];

    for (let offset = 0; ; offset += limit) {
        priceTiers = await fetchPriceTierByCatalog(catalogId, limit, offset);
        if (priceTiers.length === 0) {
            break;
        }
        yield {
            priceTiers
        }
    }
}

export async function* exportChunkGenerator(catalogId: number, limit: number = DEFAULT_LIMIT) {

    const catalog = await fetchCatalogById(catalogId);
    yield {
        catalog
    };

    for await (let chunk of priceTierGenerator(catalogId, limit)) {
        yield chunk;
    }

    for await (let chunk of productsGenerator(catalogId, limit)) {
        yield chunk;
    }
}