import _                                from 'lodash';
import { ValidationOptions } from 'sequelize/types/instance-validator';
import {
    AutoIncrement,
    Column,
    PrimaryKey,
    Table,
    ForeignKey,
    BelongsTo,
}                                       from 'sequelize-typescript';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import Assortment, {
    AssortmentType
}                                       from './Assortment';
import Product, {
    findProductsByExternalIdsAndCatalog
}                                       from './Product';
import Catalog                          from './Catalog';

@Table({tableName: 'assortmentProducts'})
export default class AssortmentProduct extends TreezModel<AssortmentProduct> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                             number;

    @ForeignKey(() => Product)
    @Column
    productId:                      number;

    @BelongsTo(() => Product)
    product:                        Product;

    @ForeignKey(() => Assortment)
    @Column
    assortmentId:                   number;

    @BelongsTo(() => Assortment)
    assortment:                     Assortment;

    static defaultAttributes = [
        'id',
        'productId',
        'assortmentId',
    ];

    static validPatchFields = [
        'productId',
        'assortmentId',
    ];

    static addAssortmentToProduct = async (
        assortmentId: number,
        externalId: string,
        catalogId: number,
    ) => {
        const products = await findProductsByExternalIdsAndCatalog([externalId], catalogId);
        const productIds = products.map(p => p.id);

        return await Promise.all(productIds.map(async productId => {
            const assortmentProduct = await AssortmentProduct.findOne({
                where: {
                    assortmentId,
                    productId: productId,
                }
            });

            if (assortmentProduct == null) {
                const newAssortmentProduct = await AssortmentProduct.create({
                    assortmentId,
                    productId: productId,
                });
                // TODO: log creation
                return newAssortmentProduct;
            }
        }));
    }

    static bulkCreateAssortmentProducts = async (
        assortmentId: number,
        externalIds: string[],
        catalogId: number
    ) => {
        return await Promise.all(_.map(externalIds, async externalId => {
            return await AssortmentProduct.addAssortmentToProduct(assortmentId, externalId, catalogId);
        }));
    }

    static removeAssortmentFromProduct = async (
        assortmentId: number,
        externalId: string,
        catalogId: number
    ) => {
        const products = await findProductsByExternalIdsAndCatalog([externalId], catalogId);
        const productIds = products.map(p => p.id);
        const assortmentProducts = await AssortmentProduct.findAll({
            where: {
                assortmentId,
                productId: productIds,
            }
        });
        if (assortmentProducts && assortmentProducts.length > 0) {
            const deletePromises = assortmentProducts.map(assortmentProduct => {
                // TODO: log removal
                return assortmentProduct.destroy();
            });
            await Promise.all(deletePromises);
        }
    }

    static findAssortmentProductsByProductIds = async (
        catalogId: number,
        assortmentType: AssortmentType,
        externalIds: string[]
    ) => {
        const assortments = await Catalog.findAssortmentsByCatalog(catalogId, assortmentType);
        const products = await findProductsByExternalIdsAndCatalog(externalIds, catalogId);
        const productIds = products.map(p => p.id);

        const assortmentProducts = await AssortmentProduct.findAll({
            where: {
                productId: productIds,
                assortmentId: assortments.map(a => a.id)
            },
            include: [
                {
                    model: Assortment,
                    as: "assortment"
                },
                {
                    model: Product,
                    as: "product"
                }
            ]
        });

        return assortmentProducts;
    }

    public validate(options?: ValidationOptions) {
        return super.validate(options);
    }
}

export const decorateProductsWithAssortments = async (products: Product[]) => {
    const productIds = products.map(p => p.id);

    const assortmentProducts = await AssortmentProduct.findAll({
        where: {
            productId: productIds,
        },
        include: [
            {
                model: Assortment,
                as: "assortment"
            }
        ]
    });

    if (assortmentProducts && assortmentProducts.length > 0) {
        const productById = new Map(products.map(p => [p.id, p]));

        assortmentProducts.forEach(ap => {
            const product = productById.get(ap.productId);
            if (product) {
                if (!product.assortments) {
                    product.assortments = [];
                }
                product.assortments.push(ap.assortment);
            }
        })
    }
}
