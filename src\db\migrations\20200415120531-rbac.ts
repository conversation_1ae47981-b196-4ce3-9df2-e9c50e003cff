import _                                from 'lodash';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';
import {
    autoPKField,
    createUpdateColumns,
    enumField,
    foreignKey,
    intField,
    notNull,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';

enum PermissionedRecord {
    PRODUCT    = 'product',
    CATALOG    = 'catalog',
    PERMISSION = 'permission',
}

const Catalogs     = 'catalogs';
const Permissions  = 'permissions';
const Roles        = 'roles';
const UsersToRoles = 'usersToRoles';

const userAuthId = 'userAuthId';
const roleToUserConstraint = 'role_to_user_constraint';
const resourceToRoleConstraint = 'resource_to_role_constraint'

const id = 'id';

export = {
    up: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        await queryInterface.createTable(Roles, {
            id       : autoPK<PERSON>ield,
            name     : not<PERSON><PERSON>(stringField),
            catalogId: notNull(foreignKey(intField, Catalogs, id)),
            ...createUpdateColumns,
        });

        await queryInterface.createTable(UsersToRoles, {
            id    : autoPKField,
            roleId: notNull(foreignKey(intField, Roles, id)),
            userAuthId: notNull(stringField),
            ...createUpdateColumns
        });

        await queryInterface.addIndex(UsersToRoles, [userAuthId]);

        await queryInterface.addConstraint(
            UsersToRoles,
            {
                type: "unique",
                fields: ['userAuthId', 'roleId'],
                name: roleToUserConstraint
            }
        );

        await queryInterface.createTable(Permissions, {
            id          : autoPKField,
            access      : notNull(intField),
            resourceId  : notNull(intField),
            resourceType: notNull(enumField(PermissionedRecord)),
            roleId      : notNull(foreignKey(intField, Roles, id)),
            ...createUpdateColumns,
        });

        await queryInterface.addConstraint(Permissions, {
            fields: ['roleId', 'resourceType', 'resourceId'],
            type: "unique",
            name: resourceToRoleConstraint
        });
    },
    down: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        await queryInterface.removeConstraint(Permissions, resourceToRoleConstraint);
        await queryInterface.removeConstraint(UsersToRoles, roleToUserConstraint);
        await queryInterface.removeIndex(UsersToRoles, userAuthId);
        await queryInterface.dropTable(Permissions);
        await queryInterface.dropTable(UsersToRoles);
        await queryInterface.dropTable(Roles);
    },
};
