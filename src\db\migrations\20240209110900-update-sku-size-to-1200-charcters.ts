import { QueryInterface } from "sequelize";
import { Sequelize } from "sequelize-typescript";
import { STRING } from "sequelize";

export = {
    up: async function (queryInterface: QueryInterface, sequelize: Sequelize) {
        await queryInterface.changeColumn("products", "sku", {
            allowNull: true,
            type: STRING(1200),
        });
    },

    down: async function (
        queryInterface: QueryInterface,
        sequelize: Sequelize,
    ) {
        await queryInterface.changeColumn("products", "sku", {
            allowNull: true,
            type: STRING(255),
        });
    },
};
