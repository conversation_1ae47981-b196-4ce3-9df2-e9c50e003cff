import { QueryInterface }               from 'sequelize';
import {
    foreignKey,
    intField,
}                                       from '@treez/dev-pack/db/fieldTypes';

const products         = 'products';
const mergedTo         = 'mergedTo';

export = {
    up: async function( queryInterface: QueryInterface ) {
        await queryInterface.addColumn(products, mergedTo, foreignKey(intField, products, 'id'));
    },
    down: async function( queryInterface: QueryInterface ) {
        await queryInterface.removeColumn(products, mergedTo);
    }
};
