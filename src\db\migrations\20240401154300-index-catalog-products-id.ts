import { QueryInterface } from 'sequelize';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.addIndex('catalogProducts', ['catalogId'], {
            name: 'idx_catalog_products_catalogId'
        });
        await queryInterface.addIndex('products', ['id'], {
            name: 'idx_products_id'
        });
    },

    down: async (queryInterface: QueryInterface) => {
        await queryInterface.removeIndex('catalogProducts', 'idx_catalog_products_catalogId');
        await queryInterface.removeIndex('products', 'idx_products_id');
    }
};
