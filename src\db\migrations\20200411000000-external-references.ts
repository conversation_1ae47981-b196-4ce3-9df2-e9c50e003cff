import { OwnerType }        from '@treez/commons/sharedTypings/product';
import { QueryInterface }   from 'sequelize';
import {
    autoPKField,
    intField,
    stringField,
    foreignKey,
    createUpdateColumns,
    enumField,
    notNull,
}                           from '@treez/dev-pack/db/fieldTypes';

const catalogProducts                     = 'catalogProducts';
const externalId                          = "externalId";
const externalReferences                  = "externalReferences";
const id                                  = "id";
const productIdIndexOnExternalReferences  = "external_references_product_id_idx";
const externalIdIndexOnExternalReferences = 'external_references_external_id_idx';
const productId                           = 'productId'
const products                            = "products";
const uuid                                = "uuid";

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.createTable(externalReferences, {
            id        : autoPKField,
            ownerId   : notNull(stringField),
            ownerType : notNull(enumField(OwnerType)),
            productId : foreignKey(intField, products, id),
            externalId: notNull(stringField),
            ...createUpdateColumns,
        });

        await queryInterface.addIndex(externalReferences, [externalId], {
            name: externalIdIndexOnExternalReferences
        });

        await queryInterface.addIndex(externalReferences, [productId], {
            name: productIdIndexOnExternalReferences
        })

        await queryInterface.sequelize.query(`
            INSERT INTO "externalReferences" ("productId", "externalId", "ownerId", "ownerType")
            SELECT
                products.id AS productId,
                products.uuid AS externalId,
                products."ownerId" AS ownerId,
                products."ownerType" AS ownerType
            FROM products
            WHERE products.uuid IS NOT NULL;
        `);

        await queryInterface.removeColumn(products, uuid);

        await queryInterface.addColumn(catalogProducts, externalId, stringField);
    },

    down: async function ( queryInterface: QueryInterface) {

        await queryInterface.removeColumn(catalogProducts, externalId);

        await queryInterface.addColumn(products, uuid, stringField);

        await queryInterface.sequelize.query(`
            UPDATE products
            SET uuid = er."externalId"
            FROM products p
            INNER JOIN "externalReferences" er ON p.id = er."productId"
        `);

        await queryInterface.removeIndex(externalReferences, productIdIndexOnExternalReferences);
        await queryInterface.removeIndex(externalReferences, externalIdIndexOnExternalReferences);

        await queryInterface.dropTable(externalReferences);
    },
}
