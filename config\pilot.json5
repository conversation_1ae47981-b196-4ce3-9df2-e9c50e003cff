{
    db: {
        host:      'product-db-pilot.treez.io',
        name:      'product-db-pilot',
        namespace: 'product-db-pilot',
        password:  'ssm:/apps/product/envs/pilot/db/product-pilot/password',
        username:  'product-pilot',
    },
    logger: {
        name: 'product-api-pilot',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serviceName: 'product-api-pilot',
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        serviceUrl: 'pulsar://pulsar-broker-preprod.treez.io:6650',
        topic: {
            productUpdate: 'persistent://product-api-pilot/product/productUpdate'
        }
    }
}
