import { QueryInterface }   from 'sequelize';
import {
    boolean<PERSON><PERSON>,
    autoP<PERSON><PERSON>ield,
    createUpdateColumns,
    jsonField,
}                           from '@treez/dev-pack/db/fieldTypes';

// Table Name
const productUpdateOutbox = 'productUpdateOutbox';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.createTable(productUpdateOutbox, {
            id     : autoPKField,
            payload: jsonField,
            published: booleanField,
            ...createUpdateColumns,
        });
    },
    down: async function(queryInterface: QueryInterface) {
        await queryInterface.dropTable(productUpdateOutbox);
    },
};
