include:
  - project: 'treez-inc/engineering/ci-templates/update-kubernetes-manifest'
    ref: 'main'
    file: 'template.yml'

image: nikolaik/python-nodejs:python3.8-nodejs12-bullseye

stages:
  - test
  - tag
  - deploy
  - deploy_kubernetes

cache: &global_cache
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/

.install_pulsar: &install_pulsar
  - echo $PULSAR_VERSION >> /tmp/pulsar
  - |
    if [[ ! -f /tmp/apache-pulsar-client.deb && ! -f /tmp/apache-pulsar-client-dev.deb ]]; then
      wget -O /tmp/apache-pulsar-client.deb https://archive.apache.org/dist/pulsar/pulsar-${PULSAR_VERSION}/DEB/apache-pulsar-client.deb
      wget -O /tmp/apache-pulsar-client-dev.deb https://archive.apache.org/dist/pulsar/pulsar-${PULSAR_VERSION}/DEB/apache-pulsar-client-dev.deb
    fi
  - ls -la /tmp
  - apt-get -y install /tmp/apache-pulsar-client.deb /tmp/apache-pulsar-client-dev.deb

tag:
  stage: tag
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_TITLE =~ /^Merge branch '(.*?)' into 'master'*$/
  script:
    - |
      echo $CI_COMMIT_MESSAGE
      git clone https://${GIT_CI_USERNAME}:${GIT_TOKEN}@gitlab.com/${CI_PROJECT_PATH}.git target-repo
      cd target-repo
      git checkout $CI_COMMIT_BRANCH
      git log -1 --pretty=%B > tempFileMergeCommit.txt
      echo $(cat tempFileMergeCommit.txt)
      echo Get version command
      cat tempFileMergeCommit.txt
      export VERSION_COMMAND=$(cat tempFileMergeCommit.txt | awk -F'[][]' '{printf tolower($2)}')
      echo Version command found $VERSION_COMMAND
      export IS_COMMAND_VALID="false"
      if [ "$VERSION_COMMAND" == "patch" ]; then IS_COMMAND_VALID="true"; fi
      if [ "$VERSION_COMMAND" == "minor" ]; then IS_COMMAND_VALID="true"; fi
      if [ "$VERSION_COMMAND" == "major" ]; then IS_COMMAND_VALID="true"; fi
      if [ "$IS_COMMAND_VALID" == "false" ]; then echo Version command not provided; exit 1; fi
      echo Version Service
      npm version $VERSION_COMMAND --no-git-tag-version
      echo Get current version
      CURRENT_VERSION=$(node -pe "require('./package.json')['version']")
      echo Current version $CURRENT_VERSION
      # echo Get change log message
      # sed -n '/^\*\*Change Log Start\*\*$/,/(^\*\*Change Log Stop\*\*$/p' tempFileMergeCommit.txt > tempChangeLogChunk.txt
      # sed '/\*\*Change Log Start\*\*/d' tempChangeLogChunk.txt > tempChangeLogChunkMinusStart.txt
      # sed '/\*\*Change Log Stop\*\*/d' tempChangeLogChunkMinusStart.txt > tempChangeLogChunkMinusStartStop.txt
      # echo Update change log
      # echo "## [$CURRENT_VERSION] - $(date '+%Y-%m-%d')" > NEWCHANGELOG.md
      # echo >> CHANGELOG.md
      # cat tempChangeLogChunkMinusStartStop.txt >> NEWCHANGELOG.md
      # echo >> CHANGELOG.md
      # cat CHANGELOG.md >> NEWCHANGELOG.md
      git config user.name "${GIT_CI_USERNAME}"
      git config user.email "${GIT_CI_EMAIL}"
      echo Credentials set
      echo Commit new tag changes
      git status
      git add package.json
      git add package-lock.json
      # git add CHANGELOG.md
      git commit -m "$CURRENT_VERSION"
      git tag -a $CURRENT_VERSION -m "$CURRENT_VERSION" # Tag Branch
      git push origin $CURRENT_VERSION # Push Tag
      git push origin $CI_COMMIT_BRANCH # Push Branch


test:
 stage: test
 variables:
   POSTGRES_USER: ubuntu
   POSTGRES_DB: testing
   POSTGRES_HOST: postgres
   POSTGRES_HOST_AUTH_METHOD: trust
   JEST_JUNIT_OUTPUT: ./reports/unit/junit.xml
   TZ: "America/Los_Angeles"
   PULSAR_VERSION: "2.10.2"
 services:
   - postgres:10-alpine
 script:
   - *install_pulsar
   - npm install
   - npm run test
 cache:
   - <<: *global_cache
   - key: "pulsar-$PULSAR_VERSION"
     paths:
       - /tmp/apache-pulsar-client.deb
       - /tmp/apache-pulsar-client-dev.deb
 artifacts:
   paths:
     - ./reports
 except:
   - tags
   - configure-deploy-versioning

deploy_version:
  stage: deploy
  image: docker:19.03.0
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
    DOCKER_HOST: tcp://docker:2375
    REGISTRY: "228276746220.dkr.ecr.us-west-2.amazonaws.com"
    IMAGE_TAG: $CI_COMMIT_TAG
    APP: $CI_PROJECT_NAME
  services:
    - docker:19.03.0-dind
  before_script:
    - cat /etc/*release
    - apk add --no-cache python3 py3-pip
    - pip3 install --upgrade pip
    - pip3 install --no-cache-dir awscli
    - aws --version
  script:
    - IMAGE="${REGISTRY}/${APP}"
    - echo "Building '${IMAGE}:${IMAGE_TAG}' image, to publish..."
    - aws ecr get-login-password | docker login --username AWS --password-stdin $REGISTRY
    - docker login docker.io -u buildtreez -p $DOCKER_HUB_TOKEN
    # - docker pull $IMAGE:latest || true
    - docker build --build-arg "npmToken=$NPM_TOKEN" -t "$IMAGE:$IMAGE_TAG" -t "$IMAGE:latest" --network host .  #--cache-from $IMAGE:latest
    - echo "Publishing '${IMAGE}:${IMAGE_TAG}', '${IMAGE}:latest' images..."
    - docker push "$IMAGE:$IMAGE_TAG"
    - docker push "$IMAGE:latest"
  only:
    - tags

apply_to_manifest:
  stage: deploy_kubernetes
  extends: .apply_version_manifest
  variables:
    IMAGE: 228276746220.dkr.ecr.us-west-2.amazonaws.com/$APP
    IMAGE_TAG: $CI_COMMIT_TAG
    VERSION: $CI_COMMIT_TAG
    GITLAB_EMAIL: $GIT_EMAIL
  only:
    - tags
    - configure-deploy-versioning


