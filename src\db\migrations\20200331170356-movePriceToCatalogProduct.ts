import { QueryInterface }             from 'sequelize';
import { Sequelize }                  from 'sequelize-typescript';
import {
    preciseMonetaryField,
}                                     from '@treez/dev-pack/db/fieldTypes';

const CatalogProducts  = 'catalogProducts';
const Price = 'price';
const Products  = 'products';



export = {
    up: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        // Add price field to catalog product
        await queryInterface.addColumn(
            CatalogProducts, Price, preciseMonetaryField
        );

         // Overwrite catalogProduct price with price from product msrp
        await queryInterface.sequelize.query(
            `UPDATE "catalogProducts" SET "price" = "products"."msrp" FROM "products" WHERE "catalogProducts"."productId" = "products"."id" AND "products"."msrp" IS NOT NULL`
        );

        // Populate catalogProduct price from product price
        await queryInterface.sequelize.query(
            `UPDATE "catalogProducts" SET "price" = "products"."price" FROM "products" WHERE "catalogProducts"."productId" = "products"."id"`
        );

        await queryInterface.sequelize.query(
            `UPDATE "catalogProducts" SET "price" = "products"."price" FROM "products" WHERE "catalogProducts"."productId" = "products"."id"`
        );

        // Remove price from catalogProduct overrides
        await queryInterface.sequelize.query(
            `UPDATE "catalogProducts" SET overrides = overrides - 'price'`
        );

        // drop price from product - Maybe move all brand prices to MSRP?
        await  queryInterface.removeColumn(Products, Price);
    },
    down: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        await queryInterface.removeColumn(CatalogProducts, Price);
        await queryInterface.addColumn(Products, Price, preciseMonetaryField);
    }
}
