{
    db: {
        host:      'product-db-rc.treez.io',
        name:      'product-db-rc',
        namespace: 'product-db-rc',
        password:  'ssm:/apps/product/envs/rc/db/product-rc/password',
        username:  'product-rc',
    },
    logger: {
        name: 'product-api-rc',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serviceName: 'product-api-rc',
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        topic: {
            productUpdate: 'persistent://product-api-rc/product/productUpdate'
        }
    }
}
