import {
    Request,
    Response,
    Router,
}                                                   from 'express';
import { StatusCodes }                              from 'http-status-codes';
import _                                            from 'lodash';
import { ProductData }                              from '@treez/commons/sharedTypings/product';
import { transaction }                              from '@treez/dev-pack/db';
import {
    sendErrorResponse,
    ErrorResponse,
}                                                   from '@treez/dev-pack/errors';
import logger                                       from '@treez/dev-pack/logger';
import {
    AuthenticatedRequest,
    checkRequestorIsAuthenticated,
}                                                   from '@treez/dev-pack/auth';
import { ProductPermission }                        from '../lib/permissions';
import { getSimplifiedHistory }                     from '../lib/productHistory';
import { getUserInfo }                              from '../lib/expressUtils';
import { checkCatalogPermissions }                  from '../middleware';
import Assortment, {
    AssortmentType,
}                                                   from '../models/Assortment';
import AssortmentProduct                            from '../models/AssortmentProduct';
import Catalog, {
    addExistingProductsToCatalog,
    addProductRequirementsToCatalog,
    createProductRequirementsForCatalog,
    defaultCatalogAttributes,
    getCatalog,
    getCatalogs,
    updateCatalog,
    removeProductsFromCatalog,
    removeProductRequirementsFromCatalog,
    findCatalogById,
}                                                   from '../models/Catalog';
import ExternalReferences, {
    findSellTreezId
}                                                   from '../models/ExternalReference';
import {
    createProductForCatalog,
    mergeProductsToExistingProduct,
    mergeProductsToNewProduct,
    ProductInCatalog,
    ProductInformation,
    updateProductInCatalog,
    ProductRecord,
    getProductWithOverrides,
    findAndDeleteExternalReference,
    CatalogProductInformation,
}                                                   from '../models/Product';
import {
    getProductIdWithCatalogIdAndExternalReference,
}                                                   from '../models/CatalogProduct';

const route = Router();

const {
    ReadCatalog,
    UpdateCatalog,
    DeleteCatalog,
} = ProductPermission;

/**
 * @swagger
 *  paths:
 *      /catalogs/${id}:
 *          delete:
 *              summary: Deletes a catalog. Treez Only
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  204:
 *                      description: catalog deleted.
 *                  403:
 *                      description: Forbidden. Must be Treez
 *
 */
route.delete('/:id',
checkCatalogPermissions('id', [[DeleteCatalog]]),
async (request: Request, response: Response) => {
    const {id} = request.params;

    try {
        await transaction( async () => {
            const catalog = await getCatalog({id});

            await catalog.destroy();
        });

        return response.status(StatusCodes.NO_CONTENT).send();

    }
    catch (error) {
        logger.error("Error deleting catalog on product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/${id}/products:
 *          delete:
 *              summary: Removes a product from a catalog and optionally its children catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  204:
 *                      description: Product soft "deleted" (status set as DELETED)
 */
route.delete(`/:id/products`,
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params: { id }
    } = request;

    const childCatalogIds: number[] = _.get(body, 'childCatalogIds');
    const productIds: number[] = _.get(body, 'productIds');

    try {
        await transaction( async () => {
            await removeProductsFromCatalog(
                Number(id),
                productIds,
                childCatalogIds
            );
        });

        return response.status(StatusCodes.NO_CONTENT).send()
    }
    catch (error) {
        logger.error("Error removing products from catalog: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalog_products:
 *          get:
 *              summary: Gets all catalogs for an entity
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: query
 *                  name: ownerId
 *                  schema:
 *                      type: integer
 *                  required: true
 *                - in: query
 *                  name: ownerType,
 *                  schema:
 *                      type: string
 *                      enum: [store, brand, organization]
 *                  required: true
 *              responses:
 *                  200:
 *                      description: Retreived catalogs for ownerId and type
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: object
 *                                  properties:
 *                                      count:
 *                                          title: Catalog Count
 *                                          type: integer
 *                                      data:
 *                                          title: Catalogs
 *                                          type: array
 *                                          items:
 *                                              $ref: '#/components/schemas/Catalog'
 *                  400:
 *                      description: BAD REQUEST- must have ownerId and ownerType identified
 *                  401:
 *                      description: UNAUTHENTICATED- must have bearer token and be authenticated user
 *
 */
route.get('/',
async (request: Request, response: Response) => {
    try {
        const {
            include,
            ownerId,
            ownerType,
        } = request.query;

        if (   ownerId == null
            || ownerType == null ) {
            throw new ErrorResponse(StatusCodes.BAD_REQUEST, 'Required parameters ownerId and ownerType are missing!');
        }

        const catalogs =  await transaction(async () => {

            const whereOption = {
                ownerId: Number(ownerId),
                ownerType,
            } as {
                ownerId: number,
                ownerType: string,
            }

            const includedModels = include ? _.split(include as string, ',') : [];

            const catalogs = await getCatalogs(
                whereOption,
                includedModels,
                defaultCatalogAttributes,
            );

            return {
                count:      catalogs.length,
                data:       catalogs,
            }
        });

        return response.json(catalogs);
    }
    catch (error) {
        logger.error("Error getting catalogs from product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}:
 *          get:
 *              summary: gets a catalog by id
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  200:
 *                      description: OK- Retrieved catalog by id
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: object
 *                                  properties:
 *                                      data:
 *                                          title: Catalog Retrieved
 *                                          $ref: '#/components/schemas/Catalog'
 *                  401:
 *                      description: UNAUTHORIZED- User must have been authorized and request had bearer token
 *
 *
 */
route.get('/:id',
async (request: Request, response: Response) => {
    try {
        const {
            params: { id },
            query: { include },
        } = request;

        const data =  await transaction( async () => {

            const includedModels = include ? _.split(include as string, ',') : [];

            return await getCatalog({ id }, includedModels);
        });

        return response.json({ data });
    }
    catch (error) {
        logger.error("Error getting catalogs from product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/${id}/retailPresence:
 *          get:
 *              summary: Gets a brand catalog's retail presence
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  200:
 *                      description: OK- returns a catalogs adoption data
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  type: array
 *                                  items:
 *                                      $ref: '#/components/schemas/CatalogCarryingBrandProduct'
 *                  401:
 *                      description: UNAUTHORIZED - must be authorized user
 *                  403:
 *                      description: FORBIDDEN - must have read catalog permission
 *
 */
route.get(`/:id/retailPresence`,
checkCatalogPermissions('id', [[ReadCatalog]]),
async (request: Request, response: Response) => {
    const { id } = request.params;

    try {
        const catalog = await findCatalogById(id);

        const catalogMatches = await catalog.getCatalogsWithBrandPresence();

        response.status(StatusCodes.OK).send(catalogMatches);
    }
    catch (error) {
        logger.error(`Error getting catalog matches for catalog ${id}`, error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/${id}:
 *          patch:
 *              summary: Updates a catalog by id
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  202:
 *                      description: ACCEPTED - Updates accepted
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Catalog'
 *                  401:
 *                      description: UNAUTHORIZED - User must be authorized user
 *                  403:
 *                      description: FORBIDDEN - User must have UPDATE CATALOG permissions for catalog or parent catalog
 */
route.patch('/:id',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params,
    } = request;

    const { id } = params;

    try {
        const updatedCatalog =  await transaction( async () => {
            return await updateCatalog(
                Number(id),
                body
            );
        });

        return response.status(StatusCodes.ACCEPTED).json(updatedCatalog);
    }
    catch (error) {
        logger.error("Error patching on product api in catalogs: ", error)
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/override/{productId}:
 *          patch:
 *              summary: Updates a product with catalog specific information such as status and product overrides.
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *                - in: path
 *                  name: productId
 *                  schema:
 *                      type: integer
 *                  required: true
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/requestBodies/ProductOverrides'
 *              responses:
 *                  202:
 *                      description: ACCEPTED - changes accepted, catalog specific information applied.
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Product'
 *                  401:
 *                      description: UNAUTHORIZED - must be authorized user
 *                  403:
 *                      description: FORBIDDEN - must have Update Catalog permission
 *
 */
route.patch('/:id/override/:productId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
    } = request;

    const {
        id: catalogId,
        productId,
    } = params;

    const userInfo = getUserInfo(request);

    try {
        const data = await transaction( async () => {
            const updatedProduct = await updateProductInCatalog(Number(productId), Number(catalogId), body, userInfo);

            let sellTreezId;

            const sellTreezExternalReference = await findSellTreezId(Number(productId));

            if (sellTreezExternalReference == null) {
                const externalReference = await ExternalReferences.findOne({
                    where: {
                        productId,
                    }
                });

                sellTreezId = _.get(externalReference, 'externalId');
            }
            else {
                sellTreezId = sellTreezExternalReference.externalId;
            }

            return {
                ...updatedProduct,
                uuid: sellTreezId,
            }
        })

        return response.status(StatusCodes.ACCEPTED).json({ data });
    }
    catch (error) {
        logger.error("Error updating product on stores api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products:
 *          patch:
 *              summary: Adds a product to a catalog and optionally its children catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *                  description: id of the catalog to add products to
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              type: object
 *                              properties:
 *                                  productIds:
 *                                      type: array
 *                                      items:
 *                                          type: integer
 *                                  childCatalogIds:
 *                                      type: array
 *                                      items:
 *                                          type: integer
 *                                  catalogInformation:
 *                                      type: object
 *                                      description: catalog specific information
 *              responses:
 *                  201:
 *                      description: Created Product(s) in catalog(s)
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Product'
 *                  401:
 *                      description: UNAUTHORIZED - must be authenticated user
 *                  403:
 *                      description: FORBIDDEN - must have update priveldges on parent
 */
route.patch(`/:id/products`,
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params: { id: catalogId },
    } = request;

    const {
        productIds,
        childCatalogIds,
        catalogInformation,
    } = body;

    const userInfo = getUserInfo(request);

    try {
        const products = await transaction( async () => {
            return await addExistingProductsToCatalog(
                Number(catalogId),
                productIds,
                userInfo,
                catalogInformation,
                childCatalogIds,
            );
        });

        response.status(StatusCodes.CREATED).send(products);
    }
    catch (error) {
        logger.error(`Error adding products ${productIds} to catalog ${catalogId}`, error)
        return sendErrorResponse(response, error);
    }
});

/*
*   Backwards compatibility for ST 2.10.3.1 visiblity vs visible
*/
interface ProductInCatalogWithVisibility extends ProductInCatalog {
    visibility?: string,
}
const getVisibilityHandledBody = (patchRequest: ProductInCatalogWithVisibility) => {
    const formattedRequest = { ...patchRequest };

    if (formattedRequest.visibility != null) {
        if (!_.has(formattedRequest, 'visible')) {
            formattedRequest.visible = formattedRequest.visibility === 'hidden' ? false : true;
        }
        delete formattedRequest.visibility;
    }

    return formattedRequest;
}

/**
* Updates ONLY the catalog specific information, namely "active" and any overrides.
*/

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products/{productId}:
 *          patch:
 *              summary: Updates only catalog specific information
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  schema:
 *                      type: integer
 *                  required: true
 *                  description: catalog id
 *                - in: path
 *                  name: productId
 *                  schema:
 *                      type: integer
 *                  required: true
 *                  description: product Id
 *              responses:
 *                  202:
 *                      description: Accepted changes ok
 */
route.patch('/:id/products/:productId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
    } = request;

    const {
        id: catalogId,
        productId,
    } = params;

    const userInfo = getUserInfo(request);

    const updatedBody = getVisibilityHandledBody(body);

    try {
        const data = await transaction( async () => {
            const catalogProduct : ProductInCatalog = await updateProductInCatalog(
                Number(productId),
                Number(catalogId),
                updatedBody as ProductInCatalog,
                userInfo,
            );

            return {
                ...catalogProduct,
                visibility: catalogProduct.visible ? 'visible' : 'hidden',
            }
        });

        return response.status(StatusCodes.ACCEPTED).json({ data });
    }
    catch (error) {
        logger.error("Error updating product on stores api: ", error);
        return sendErrorResponse(response, error);
    }
});


/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products/{productId}/{externalId}:
 *          patch:
 *              summary: Updates a product and returns the external id referenced in the request path. Made to maintain dependency from SellTreez.
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  description: id of the catalog to apply the update to
 *                  required: true
 *                  schema:
 *                      type: integer
 *                - in: path
 *                  name: productId
 *                  description: id of the product to edit
 *                  required: true
 *                  schema:
 *                      type: integer
 *                - in: path
 *                  name: externalId
 *                  description: The SellTreez UUID of reference for the product.
 *                  schema:
 *                      type: string
 *                      format: uuid
 *                  required: true
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/requestBodies/ProductOverrides'
 *              responses:
 *                  202:
 *                      description: ACCEPTED - changes applied
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Product'
 *                  401:
 *                      description: UNAUTHORIZED - must be authorized user
 *                  403:
 *                      description: FORBIDDEN - must have UpdateCatalog permission
 *
 */
route.patch('/:id/products/:productId/:externalId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
        query,
    } = request;

    const {
        id: catalogId,
        externalId,
        productId,
    } = params;

    const excludeLinkedProduct = query.excludeLinkedProduct === 'true';

    const userInfo = getUserInfo(request);

    const updatedBody = getVisibilityHandledBody(body);

    try {
        const data = await transaction( async () => {
            const externalReference = await ExternalReferences.findOne({
                where: {
                    externalId,
                    productId
                }
            });

            if (externalReference === null) {
                throw new ErrorResponse(
                    StatusCodes.NOT_FOUND,
                    `ExternalReference with the externalId of ${externalId} and productId of ${productId} could not be found`
                )
            }

            const updatedCatalogProduct = await updateProductInCatalog(
                Number(productId),
                Number(catalogId),
                updatedBody as ProductInCatalog,
                userInfo,
            );

            const productWithExtReference = {
                ...updatedCatalogProduct,
                uuid: externalReference.externalId,
            }

            if (excludeLinkedProduct === true) {
                const productToReturn = await getProductWithOverrides(
                    Number(productId),
                    {
                        catalogId: Number(catalogId),
                        excludeLinkedProduct,
                    }
                )
                return {
                    ...productWithExtReference,
                    ...productToReturn,
                    visibility: productWithExtReference.visible ? 'visible' : 'hidden'
                }
            }
            else {
                return {
                    ...productWithExtReference,
                    visibility: productWithExtReference.visible ? 'visible' : 'hidden'
                };
            }
        });

        return response.status(StatusCodes.ACCEPTED).json({ data })
    }
    catch (error) {
        logger.error("Error updating product on stores api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/requirements/{requirementsId}:
 *          delete:
 *              summary: Removes product requirements from a catalog and optionally any of its children catalogs
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  description: id of catalog
 *                  schema:
 *                      type: integer
 *                  required: true
 *                - in: path
 *                  name: requirementsId
 *                  description: id of the requirements you want to remove from the catalog
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  202:
 *                      description: ACCEPTED - Requirements removed from catalog
 *                  401:
 *                      description: UNAUTHORIZED - User must be authorized
 *                  403:
 *                      description: FORBIDDEN - User must have UPDATE CATALOG permissions for catalog
 */
route.delete(`/:id/requirements/:requirementsId`,
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params,
    } = request;

    const {
        id: catalogId,
        requirementsId,
    } = params;

    const childCatalogIds: number[] = _.get(body, 'childCatalogIds');

    try {
        await transaction( async () => {
            await removeProductRequirementsFromCatalog(
                Number(catalogId),
                Number(requirementsId),
                childCatalogIds
            );

        });

        response.status(StatusCodes.ACCEPTED).send();
    }
    catch (error) {
        logger.error("Error deleting requirements to catalog to product api: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs:
 *          post:
 *              summary: Creates a new catalog
 *              tags:
 *                - /catalogs
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              type: object
 *                              properties:
 *                                  name:
 *                                      type: string
 *                                      description: name of the catalog
 *                                  parentCatalogId:
 *                                      type: integer
 *                                      description: id of the parent catalog
 *                                  ownerId:
 *                                      type: integer
 *                                      description: the id of the entity that owns this catalog
 *                                  ownerType:
 *                                      type: string
 *                                      description: the type of owner
 *                                      enum: [brand, organization, store]
 *              responses:
 *                  201:
 *                      description: CREATED - catalog was created
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/Catalog'
 *                  401:
 *                      description: UNAUTHORIZED - user must be authorized
 *
 */
route.post('/',
checkRequestorIsAuthenticated(),
async (request: Request, response: Response) => {
    try {
        const data =  await transaction( async () => {
            return await Catalog.create(request.body);
        });

        return response.status(StatusCodes.CREATED).json({ data });
    }
    catch (error) {
        logger.error("Error posting new catalog to product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});



/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/product:
 *          post:
 *              summary: Creates a new product and adds it to its "native" catalog
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  description: id of the catalog that is the native catalog for the product
 *                  required: true
 *                  schema:
 *                      type: integer
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/schemas/ProductInCatalog'
 *              responses:
 *                  201:
 *                      description: CREATED - Product was created and added to catalog
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/ProductInCatalog'
 *                  401:
 *                      description: UNAUTHORIZED - User must be authenticated
 *                  403:
 *                      description: FORBIDDEN - User must have UPDATE CATALOG permission on native catalog
 *
 */
route.post('/:id/product',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
        query,
    } = request;

    const userInfo = getUserInfo(request);
    const { id: catalogId } = params;
    const { isEpmFlag: isEpmProductFlag } = query;
    const boolEpmFlag = isEpmProductFlag === 'true';
    let productToAddToCatalog = body as Partial<ProductInformation>;

    try {
        const data = await transaction( async() => {
            return await createProductForCatalog(
                Number(catalogId),
                productToAddToCatalog,
                userInfo,
                boolEpmFlag,
            );
        });

        return response.status(StatusCodes.CREATED).send({ data })
    }
    catch (error) {
        logger.error("Error posting new product to catalog", error);
        return sendErrorResponse(response, new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            error.message
        ));
    }
});


/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products/{productId}:
 *          get:
 *              summary: Gets a product as it appears in a particular catalog
 *              tags:
 *                - /catalogs
 *              parameters:
 *                - in: path
 *                  name: id
 *                  description: id of catalog
 *                  required: true
 *                  schema:
 *                      type: integer
 *                - in: path
 *                  name: productId
 *                  description: id of the product to retreive
 *                  required: true
 *                  schema:
 *                      type: integer
 *              responses:
 *                  200:
 *                      description: OK - Product Retrieved
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/ProductInCatalog'
 *                  401:
 *                      description: UNAUTHORIZED - User must be authenticed user
 */
route.get('/:id/products/:productId',
async (request: AuthenticatedRequest, response: Response) => {
    const {
        params,
    } = request;

    const {
        id: catalogId,
        productId: productIdOrExternalId,
    } = params;

    try {
        const productId : number = isNaN(Number(productIdOrExternalId))
            ? await getProductIdWithCatalogIdAndExternalReference(Number(catalogId), productIdOrExternalId)
            : Number(productIdOrExternalId)

        const product = await getProductWithOverrides(
            productId,
            {
                catalogId: Number(catalogId),
            }
        );

        response.status(StatusCodes.OK).json(product);
    }
    catch (error) {
        logger.error("Error finding product in the catalog", error);
        return sendErrorResponse(response, error);
    }
});


/**
 * @swagger
 *  components:
 *      requestBodies:
 *          MergeToNewProductRequestBody:
 *              description: Merge existing products, productIds, into new product. Give catalog specific information by catalog, the key in dictionary.
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              product:
 *                                  description: final merge product
 *                                  $ref: '#/components/schemas/Product'
 *                              productIds:
 *                                  description: ids of the products to merge
 *                                  type: array
 *                                  items:
 *                                      type: integer
 *                              catalogProductInformation:
 *                                  description: catalog specific information for each any of the catalogs the product is to be assigned
 *                                  type: object
 *          MergeToExistingProductRequestBody:
 *              description: Merge existing products, productIds, into new product. Give catalog specific information by catalog, the key in dictionary.
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              productIds:
 *                                  description: ids of the products to merge
 *                                  type: array
 *                                  items:
 *                                      type: integer
 *                              catalogProductInformation:
 *                                  description: catalog specific information for each any of the catalogs the product is to be assigned
 *                                  type: object
 */
export interface MergeProductRequestBody {
    product: Partial<ProductRecord>,
    productIds: number[],
    catalogProductInformation: CatalogProductInformation
}

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products/merge:
 *          post:
 *              summary: Merges products together into an identified catalog
 *              description: merge existing products into one new product. catalogProductInformation is a dictionary object with the catalog as key and catalog specific information as the value.
 *              tags:
 *                - /catalogs
 *                - Merge Products
 *              parameters:
 *                - in: path
 *                  description: id of the catalog
 *                  name: id
 *                  required: true
 *                  schema:
 *                      type: integer
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/requestBodies/MergeToNewProductRequestBody'
 *              responses:
 *                  201:
 *                      description: CREATED - products merged to new product
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/ProductInCatalog'
 *                  401:
 *                      description: UNAUTHORIZED - User must be authenticated
 *                  403:
 *                      description: FORBIDDEN - Requestor must have UPDATE CaTALOG permission for catalog id'd
 *
 */
route.post('/:id/products/merge',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        id: catalogId
    } = request.params;

    const {
        product,
        productIds,
        catalogProductInformation,
    } = request.body as MergeProductRequestBody;

    try {
        const userInfo = getUserInfo(request);

        const mergedProduct = await transaction(async () => {
            return await mergeProductsToNewProduct(
                product,
                Number(catalogId),
                productIds,
                userInfo,
                catalogProductInformation,
            );
        });

        response.status(StatusCodes.CREATED).json(mergedProduct);
    }
    catch (error) {
        logger.error("Error merging products to a new product in catalog", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/products/merge/{productId}:
 *          post:
 *              summary: Merges products to an existing product
 *              description: merge existing products into one existing product. Products' native catalogs must share a parent catalog. catalogProductInformation is a dictionary object with the catalog as key and catalog specific information as the value.
 *              tags:
 *                - /catalogs
 *                - Merge Products
 *              parameters:
 *                - in: path
 *                  description: the id of the catalog that is to be the native catalog for the merged product
 *                  name: id
 *                  required: true
 *                  schema:
 *                      type: integer
 *                - in: path
 *                  description: id of the product that all other products are supposed to merge to
 *                  name: productId
 *                  required: true
 *                  schema:
 *                      type: integer
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#/components/requestBodies/MergeToExistingProductRequestBody'
 *              responses:
 *                  201:
 *                      description: CREATED - Products were merged and catalog products created
 *                  401:
 *                      description: UNAUTHORIZED - Must be authenticated user
 *                  403:
 *                      description: FORBIDDEN - Must have UPDATE CATALOG permission on native catalog for target catalog
 */
route.post('/:id/products/merge/:productId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const {
        id: targetCatalogId,
        productId,
    } = request.params;

    const {
        productIds,
        catalogProductInformation,
    } = request.body as MergeProductRequestBody;

    const userInfo = getUserInfo(request);

    try {
        const updatedProduct = await mergeProductsToExistingProduct(
            Number(productId),
            Number(targetCatalogId),
            productIds,
            userInfo,
            catalogProductInformation,
        );

        response.status(StatusCodes.CREATED).json(updatedProduct);
    }
    catch (error) {
        logger.error("Error merging products to a new product in catalog", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/requirements:
 *          post:
 *              summary: Creates product requirements and adds them to a catalog
 *              description: Creates the native catalog for product requirements
 *              tags:
 *                - /catalogs
 *                - Requirements
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#components/schemas/ProductRequirements'
 *              responses:
 *                  201:
 *                      description: CREATED - requirements created and applied to catalog.
 *                  401:
 *                      description: UNAUTHORIZED - Must be authenticated user
 *                  403:
 *                      description: FORBIDDEN - User must have UPDATE CATALOG permission
 */
route.post('/:id/requirements',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params
    } = request;

    const { id } = params;
    try {
        const newProductRequirements =  await transaction( async () => {
            return await createProductRequirementsForCatalog(body, Number(id));
        });

        response.status(StatusCodes.CREATED).json(newProductRequirements);
    }
    catch (error) {
        logger.error("Error posting new product requests to catalog", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  paths:
 *      /catalogs/{id}/requirements/{requirementsId}:
 *          post:
 *              summary: Adds an already created set of requirements to a catalog. Optionally can apply the requirements to children catalogs
 *              tags:
 *                - /catalogs
 *                - Requirements
 *              parameters:
 *                - in: path
 *                  name: id
 *                  description: id of the catalog to add the requirements to
 *                  required: true
 *                  schema:
 *                      type: integer
 *                - in: path
 *                  name: requirementsId
 *                  description: id of the requirements to apply to the catalog
 *                  required: true
 *                  schema:
 *                      type: integer
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              type: object
 *                              properties:
 *                                  childCatalogIds:
 *                                      type: array
 *                                      items:
 *                                          type: integer
 *              responses:
 *                  202:
 *                      description: ACCEPTED - products applied to catalog(s)
 *                  401:
 *                      description: UNAUTHORIZED - User must be authorized
 *                  403:
 *                      description: FORBIDDEN - User must have UPDATE CATALOG permissions
 */
route.post('/:id/requirements/:requirementsId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params,
    } = request;

    const {
        id,
        requirementsId,
    } = params

    const { childCatalogIds } = body as { childCatalogIds: number[] }

    try {
        await addProductRequirementsToCatalog(
            Number(id),
            Number(requirementsId),
            childCatalogIds
        );
        response.status(StatusCodes.ACCEPTED).send();
    }
    catch (error) {
        logger.error("Error posting new product requests to catalog", error);
        return sendErrorResponse(response, error);
    }
});

route.delete('/:id/products/:productId/externalReference/:externalReferenceId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        productId,
        externalReferenceId,
    } = request.params;

    try {
        await findAndDeleteExternalReference(
            Number(productId),
            externalReferenceId,
        );

        return response.status(StatusCodes.ACCEPTED).send();
    }
    catch (error) {
        logger.error("Error deleting external product reference: ", error);
        return sendErrorResponse(response, error);
    }
});

interface DeleteOverrideBody {
    fields: ProductData[],
}

route.delete('/:id/products/:productId/override',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        id: catalogId,
        productId,
    } = request.params;

    const {
        fields,
    } = request.body as DeleteOverrideBody;

    try {
        const result = await transaction( async () => {
            return await Catalog.findAndDeleteOverrides(
                Number(productId),
                Number(catalogId),
                fields,
            );
        });

        response.status(StatusCodes.ACCEPTED).json(result);
    }
    catch (error) {
        logger.error("Error deleting overrides: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Create a new assortment
 */
route.post('/:id/assortments',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        id,
    } = request.params;

    try {
        const assortment = await transaction(async () => {
            return await Assortment.saveAssortment(request.body, Number(id));
        });

        return response.status(StatusCodes.CREATED).json({ data: assortment });
    }
    catch (error) {
        logger.error("Error posting new assortment: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Gets all assortments by catalog and type
 */
route.get('/:id/assortments/:assortmentType',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    try {
        const {
            id,
            assortmentType
        } = request.params;

        const assortments = await transaction(async () => {
                return await Catalog.findAssortmentsByCatalog(
                    Number(id),
                    assortmentType.toUpperCase() as AssortmentType
                );
        });

        return response.json(assortments);
    }
    catch (error) {
        logger.error("Error getting assortments by catalog from product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Delete an assortment.
 */
route.delete('/:id/assortments/:assortmentId',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        id,
        assortmentId
    } = request.params;

    try {
        await transaction( async () => {
            await Assortment.removeAssortment(Number(assortmentId), Number(id));
        });

        return response.status(StatusCodes.NO_CONTENT).send();
    }
    catch (error) {
        logger.error("Error deleting assortment on product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Bulk update list of assortments
 */
route.patch('/:id/assortments/bulkUpdate',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params,
    } = request;

    const { id } = params;

    try {
        await transaction( async () => {
            await Assortment.bulkUpdateAssortments(body.assortments, Number(id));
        });

        return response.status(StatusCodes.ACCEPTED).send();
    }
    catch (error) {
        logger.error("Error bulkUpdate on product api in catalogs: ", error)
        return sendErrorResponse(response, error);
    }
});

/**
 * Get assortmentProducts of productIds
 */
route.get('/:id/assortments/:assortmentType/assortmentProducts',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        id,
        assortmentType
    } = request.params;

    const query = request.query.query || request.body; // support GET body for cases of complex/loaded querying

    try {
        const assortmentProducts = await transaction( async () => {
            return await AssortmentProduct.findAssortmentProductsByProductIds(
                Number(id),
                assortmentType.toUpperCase() as AssortmentType,
                query.productIds
            );
        });

        return response.status(StatusCodes.CREATED).json(assortmentProducts);
    }
    catch (error) {
        logger.error("Error finding assortmentProducts in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Create batch of assortmentProducts
 */
route.post('/:id/assortments/:assortmentId/assortmentProducts',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        body,
        params
    } = request;

    const {
        id,
        assortmentId
    } = params;

    try {
        const assortmentProducts = await transaction( async () => {
            return await AssortmentProduct.bulkCreateAssortmentProducts(Number(assortmentId), body.productIds, Number(id));
        });

        response.status(StatusCodes.CREATED).json(assortmentProducts);
    }
    catch (error) {
        logger.error("Error posting new assortmentProduct in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Delete an assortmentProduct
 */
route.delete('/:id/assortments/:assortmentId/products/:externalId/assortmentProducts',
checkCatalogPermissions('id', [[UpdateCatalog]]),
async (request: Request, response: Response) => {
    const {
        id,
        assortmentId,
        externalId
    } = request.params;

    try {
        await transaction( async () => {
            await AssortmentProduct.removeAssortmentFromProduct(Number(assortmentId), externalId, Number(id));
        });

        return response.status(StatusCodes.NO_CONTENT).send();
    }
    catch (error) {
        logger.error("Error deleting assortmentProduct on product api in catalogs: ", error);
        return sendErrorResponse(response, error);
    }
});

/**
 * Gets the changes made to a product in the context of a catalog
 */
route.get('/:id/products/:productId/history',
checkCatalogPermissions('id', [[ReadCatalog]]),
async (request: Request, response: Response) => {
    const {
        id,
        productId,
    } = request.params;

    try {
        const changes = await getSimplifiedHistory(
            Number(productId),
            Number(id),
        );

        response.status(StatusCodes.OK).json(changes);
    }
    catch (error) {
        logger.error("Error getting history by product id: ", error);
        return sendErrorResponse(response, error);
    }
});

export default route;
