import {
    Request,
    Response,
    Router,
}                                           from 'express';
import { StatusCodes }                      from 'http-status-codes';
import logger                               from '@treez/dev-pack/logger';
import {
    sendErrorResponse,
    ErrorResponse,
}                                           from '@treez/dev-pack/errors';
import {
    connection,
    transaction,
}                                           from '@treez/dev-pack/db';
import _                                    from 'lodash';
import Product                              from '../models/Product';
import searchProducts                       from '../search';
import { SearchCriteria }                   from '../search/searchUtils';
import openapi                              from '../openapi.json';

const route = Router();

route.get('/products', async (request: Request, response: Response) => {
    try {
        const query = request.query.query || request.body; // support GET body for cases of complex/loaded querying

        if (query == null) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Missing required parameters.  Either "query" (for a search) or "productIds" (for specific products) must be included in the request query parameters.'
            );
        }

        let searchQuery: SearchCriteria;

        try {
            searchQuery = (typeof query == 'string') ? JSON.parse(query) : query;
        }
        catch (error) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Invalid/Unparsable query format: ${error}`
            );
        }

        if (searchQuery.catalogId) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `For product's catalog-level information, please use other catalog or product APIs`
            );
        }

        const results = await searchProducts(searchQuery);

        response.status(StatusCodes.OK).send(results);
    }
    catch (error) {
        logger.error(`Error Retrieving Products in the public route`, error);
        return sendErrorResponse(response, error);
    }
});

route.get('/brands', async  (request: Request, response: Response) => {
    try {
        const brandData =  await transaction( async () => {
            return await connection.query(
                `SELECT DISTINCT ON ("ownerId") "ownerId", "brandName" FROM products WHERE "ownerType" = 'brand'`,
                { model: Product }
            );
        });

        return response.json(brandData);
    }
    catch (error) {
        logger.error("Error getting brands on product api: ", error);

        return sendErrorResponse(response, error);
    }
});

route.get('/openapi.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(openapi);
});

export default route;
