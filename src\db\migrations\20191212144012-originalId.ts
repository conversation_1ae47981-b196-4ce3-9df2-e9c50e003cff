import {
    intField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const CATALOG_PRODUCTS = 'catalogProducts';
const ORIGINAL_ID = 'originalId';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(CATALOG_PRODUCTS, ORIGINAL_ID, intField);
        await queryInterface.sequelize.query(`UPDATE "catalogProducts" SET "originalId" = "productId"`); 
        await queryInterface.changeColumn(CATALOG_PRODUCTS, ORIGINAL_ID, {
            ...intField,
            allowNull: false,
        });
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn(CATALOG_PRODUCTS, ORIGINAL_ID);
    },
}
