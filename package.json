{"version": "4.7.3", "private": true, "scripts": {"build": "run-s clean generate-open-api compile", "clean": "rimraf ./dist ./reports", "compile": "tsc", "dev": "run-s clean generate-open-api compile watch", "lint": "eslint --config=./node_modules/@treez/build-tools/.eslintrc.js './src/**/*.{ts,tsx}'", "lint:fix": "eslint --config=./node_modules/@treez/build-tools/.eslintrc.js './src/**/*.{ts,tsx}' --fix", "migrate": "cross-env NODE_PATH=`pwd`/dist run-migrations", "migrations": "npm run build && npm run migrate", "migrations:next": "npm run migrations -- next", "migrations:previous": "npm run migrations -- previous", "migrations:rerun": "npm run migrations -- rerun", "migrations:reset": "npm run migrations -- reset", "start": "npm run migrate && cross-env NODE_PATH=\"`pwd`/dist\" node dist/server.js; npm run serve", "serve": "cross-env NODE_PATH=\"`pwd`/dist\" nodemon --delay 500ms dist/server.js | bunyan", "test": "cross-env NODE_ENV=test && npm run clean && npm run compile && jest --runInBand", "test:coverage": "cross-env NODE_ENV=test && npm run clean && npm run compile && jest --coverage --runInBand", "watch": "run-p  watch:tsc watch:nodemon", "watch:nodemon": "cross-env NODE_PATH=`pwd`/dist nodemon ./dist/server.js", "watch:tsc": "tsc -w", "generate-open-api": "node generate-docs.js"}, "dependencies": {"@treez/commons": "^0.1.10", "@treez/dev-pack": "4.2.0", "@types/uuid": "^3.4.6", "ajv": "^6.12.6", "big.js": "^6.1.1", "bunyan": "^1.8.15", "cross-env": "^7.0.3", "decimal.js": "^10.3.1", "elastic-apm-node": "3.21.1", "express-fileupload": "^1.3.1", "flat": "^5.0.2", "fp-ts": "^2.11.5", "http-status-codes": "^2.1.4", "io-ts": "^2.2.16", "lodash": "^4.17.21", "lru-cache": "^6.0.0", "papaparse": "^5.3.1", "pulsar-client": "~1.4.1", "sequelize": "^6.20.1", "sequelize-typescript": "^2.1.3", "swagger-ui-express": "^4.1.6"}, "devDependencies": {"@treez/build-tools": "0.0.11", "@types/big.js": "^6.1.1", "@types/express-fileupload": "^1.2.2", "@types/flat": "^5.0.2", "@types/jest": "^27.0.1", "@types/lodash": "^4.14.172", "@types/lru-cache": "^5.1.1", "@types/papaparse": "^5.2.6", "@types/source-map-support": "^0.5.4", "@types/supertest": "^2.0.11", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/uuid": "^3.4.10", "@types/validator": "^13.6.3", "eslint": "^6.8.0", "nodemon": "^2.0.12", "source-map-support": "^0.5.19", "swagger-jsdoc": "^6.1.0", "typescript": "^4.1.6"}, "engines": {"node": "12"}}