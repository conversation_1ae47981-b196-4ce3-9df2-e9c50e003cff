import {
    postTestTeardown,
    preTestSetup,
}                                    from '@treez/dev-pack/tests/testHelpers';
import { transaction }               from '@treez/dev-pack/db';
import {
    getUserPermissionsSpy,
    hasPermissionsSpy,
    sendProductUpdateMessageSpy,
}                                    from './util';
import { User }                      from '@treez/dev-pack/auth';
import { resolveUserSpy }            from '../../tests/routes/testApp';
import Product                       from '../../models/Product';
import Assortment                    from '../../models/Assortment';
import AssortmentProduct             from '../../models/AssortmentProduct';
import Capability                    from '../../models/Capability';
import Catalog                       from '../../models/Catalog';
import CatalogPriceTier              from '../../models/CatalogPriceTier';
import CatalogProduct                from '../../models/CatalogProduct';
import CatalogProductChange          from '../../models/CatalogProductChange';
import ExternalReference             from '../../models/ExternalReference';
import PriceTier                     from '../../models/PriceTier';
import PriceTierChange               from '../../models/PriceTierChange';
import ProductChange                 from '../../models/ProductChange';
import ProductRequirements           from '../../models/ProductRequirements';
import ProductRequirementsToCatalogs from '../../models/ProductRequirementsToCatalogs';
import ProductUpdateOutbox           from '../../models/ProductUpdateOutbox';
import SuggestedLink                 from '../../models/SuggestedLink';
import { TruncateOptions } from 'sequelize/types';

beforeAll(async () => {
    await preTestSetup({
        initDbOptions: {
            modelPaths: [
                `${process.cwd()}/src/models/**/!(*.test.*)`,
            ],
        },
    });
});

beforeEach(async () => {
    hasPermissionsSpy.mockImplementation(() => true);
    getUserPermissionsSpy.mockImplementation(() => []);
    sendProductUpdateMessageSpy.mockImplementation(async () => {return});

    resolveUserSpy.mockImplementation(async () => new User({
        email: '<EMAIL>',
        verified: true,
    }));
});

afterEach(async () => {
    const truncateOptions : TruncateOptions = {
        cascade: true,
        restartIdentity: true,
    };

    await transaction(async () => {
        await Assortment.truncate(truncateOptions);
        await AssortmentProduct.truncate(truncateOptions);
        await Capability.truncate(truncateOptions);
        await Catalog.truncate(truncateOptions);
        await CatalogPriceTier.truncate(truncateOptions);
        await CatalogProduct.truncate(truncateOptions);
        await CatalogProductChange.truncate(truncateOptions);
        await ExternalReference.truncate(truncateOptions);
        await PriceTier.truncate(truncateOptions);
        await PriceTierChange.truncate(truncateOptions);
        await Product.truncate(truncateOptions);
        await ProductChange.truncate(truncateOptions);
        await ProductRequirements.truncate(truncateOptions);
        await ProductRequirementsToCatalogs.truncate(truncateOptions);
        await ProductUpdateOutbox.truncate(truncateOptions);
        await SuggestedLink.truncate(truncateOptions);
    });
});

afterAll(async () => {
    return await postTestTeardown();
});
