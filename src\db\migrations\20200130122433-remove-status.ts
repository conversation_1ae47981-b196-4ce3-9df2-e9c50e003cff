import _                                from 'lodash';
import {
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const STATUS = 'status';
const PRODUCTS = 'products';


export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn(PRODUCTS, STATUS);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(PRODUCTS, STATUS, stringField);
    },
}
