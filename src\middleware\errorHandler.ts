import {
    Response,
    NextFunction,
}                               from 'express';
import {
    ReasonPhrases,
    StatusCodes,
}                               from 'http-status-codes';
import { AuthenticatedRequest } from "@treez/dev-pack/auth";
import { ErrorResponse }        from "@treez/dev-pack/errors";
import logger                   from "@treez/dev-pack/logger";

export const middlewareErrorHandler = (
    error: Error | ErrorResponse,
    request: AuthenticatedRequest,
    response: Response,
    next: NextFunction,
) => {
    if (error instanceof ErrorResponse) {
        response.status(error.code);
        response.send(error.message);
    }
    else {
        logger.error(`Error caught in middleware for path ${request.path} from requestor ${request.requestor}`, error);
        response.status(StatusCodes.INTERNAL_SERVER_ERROR);
        response.send(ReasonPhrases.INTERNAL_SERVER_ERROR);
    }
}

export default middlewareErrorHandler;
