
interface HostConfig {
    [state: string]: string;
}

declare module 'config' {
    interface Config {
        auth: {
            disabled: boolean;
            verify: {
                issuer: string;
                keySetUrl: string;
            }
            url:      string;
        }
        aws: {
            region: string;
        };

        db: {
            host:                string;
            lockTimeoutMS:       number;
            name:                string;
            password?:           string;
            port:                number;
            transactionAttempts: number;
            retryDelayMaxMS:     number;
            retryDelayMinMS:     number;
            username?:           string;
        };
        logger: {
            daysToKeep: number;
            dirname:    string;
            name:       string;
            level:      number | "error" | "trace" | "debug" | "info" | "warn" | "fatal" | undefined;
            silent?:    boolean;
        };

        monitoring: {
            elastic: {
                apm: {
                    config: {
                        secretToken:     string,
                        serverUrl:       string,
                        serviceName:     string,
                        [ key: string ]: any,
                    },
                    enabled: boolean,
                },
            },
        },

        pagination: {
            maxPerPage: number;
            minPerPage: number;
        };
        pulsar: {
            compression?: "LZ4" | "Zlib" | undefined;
            serviceUrl: string;
            topic: {
                productUpdate: string;
            };
            unpublishedMessagesDelay: number;
        };
        search: {
            maxPageSize: number;
        },
        server: {
            port: number;
        };
        traceability: {
            hosts: HostConfig;
            path: string;
            stateCodeForTesting: string;
        };

        treez: {
            authEndpoint: string;
        };

    }

    const config: Config;
    export = config;
}
