import { AuthenticatedRequest } from '@treez/dev-pack/auth';
import { getIdsFromRequestQuery } from '../../lib/expressUtils';

describe('getIdsFromRequestQuery', () => {
    it('returns an array of numbers when fetching ids from a request query', () => {
        const request = {} as AuthenticatedRequest;
    
        request.query = {catalogIds: '1,2,3,4,5'};
    
        const ids = getIdsFromRequestQuery('catalogIds', request)
    
        expect(ids.sort()).toMatchObject([1, 2, 3, 4, 5]);
    });

    it(`returns an empty array when the query param is empty`, () => {
        const request = {} as AuthenticatedRequest;
    
        request.query = {};

        const ids = getIdsFromRequestQuery('catalogIds', request)
    
        expect(ids.sort()).toMatchObject([]);
    });
});

