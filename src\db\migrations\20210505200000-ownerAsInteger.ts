import {
    QueryInterface,
}             from 'sequelize';
import logger from '@treez/dev-pack/logger';

export = {

    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await varcharToInteger(queryInterface, `catalogs`);
            await varcharToInteger(queryInterface, `products`);
            await varcharToInteger(queryInterface, `priceTiers`);
            await varcharToInteger(queryInterface, `productRequirements`);
        });
    },

    down: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await integerToVarchar(queryInterface, `catalogs`);
            await integerToVarchar(queryInterface, `products`);
            await integerToVarchar(queryInterface, `priceTiers`);
            await integerToVarchar(queryInterface, `productRequirements`);
        });
    },

};

const varcharToInteger = async (queryInterface: QueryInterface, tableName: string) => {

    logger.info(`changing ownerId from varchar to integer ${tableName}...`);

    await queryInterface.sequelize.query(`
        ALTER TABLE "${tableName}"
            ALTER COLUMN "ownerId" DROP DEFAULT,
            ALTER COLUMN "ownerId" TYPE integer USING (TRIM("ownerId")::integer);
    `);
};

const integerToVarchar = async (queryInterface: QueryInterface, tableName: string) => {

    logger.info(`changing ownerId from integer to varchar ${tableName}...`);

    await queryInterface.sequelize.query(`
        ALTER TABLE "${tableName}"
            ALTER COLUMN "ownerId" TYPE varchar USING ("ownerId"::varchar);
    `);
};
