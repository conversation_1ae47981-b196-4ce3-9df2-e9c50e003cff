import _                    from 'lodash';
import { StatusCodes }      from 'http-status-codes';
import { FindOptions }      from 'sequelize/types';
import {
    OwnerType,
    ProductField,
} from '@treez/commons/sharedTypings/product';
import { ErrorResponse }    from '@treez/dev-pack/errors';
import Product, {
    buildIncludes,
    includableModels,
}                           from '../models/Product';
import ProductSearch        from './searchRunner';
import { SearchCriteria }
                            from './searchUtils';
import {
    decorateWithCapabilities
}                           from '../models/Capability';
import {
    decorateProductsWithAssortments
}                           from "../models/AssortmentProduct";
import { connection }       from '@treez/dev-pack/db';

export const searchProducts = async (
    searchQuery: SearchCriteria,
): Promise<any> => {
    const hasCatalogId = Boolean(searchQuery.catalogId);
    const shouldSearchBrand = !hasCatalogId;

    searchQuery.filters = searchQuery.filters || [];

    if (typeof searchQuery.filters !== 'string'
        && searchQuery.filters.length > 0
    ) {
        searchQuery.filters.forEach(filter => {
            filter.values = filter.values.map(String);
            filter.not = Boolean(filter.not);
        });
    }

    const results = shouldSearchBrand
        ? await searchBrandProducts(searchQuery)
        : await searchCatalogProducts(searchQuery);

    if (!searchQuery.productIds && searchQueryIncludesModel(searchQuery, includableModels.ASSORTMENTS)) {
        await decorateProductsWithAssortments(results.data as Product[]);
    }

    if (searchQueryIncludesModel(searchQuery, includableModels.CAPABILITIES)) {
        const products = results.data as Product[];

        return {
            ...results,
            data: await decorateWithCapabilities(products),
        }
    }
    else {
        return results;
    }
}

export async function* downloableProductsGenerator(catalogId: number) {
    const LIMIT = 4000;
    for (let offset = 0; ; offset += LIMIT) {
        const [result] = await connection.query(downloadSearchQuery(), {
            raw: true,
            replacements: {
                catalogId,
                limit: LIMIT,
                offset
            }
        });
        if(result.length === 0) {
            break;
        }

        yield {
            data: result,
        };
    }
}

const searchBrandProducts = async (
    searchQuery: SearchCriteria,
) => {
    if (searchQuery.catalogId) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `For product's catalog-level information, please use other catalog or product APIs`
        );
    }

    const { productIds } = searchQuery;

    if (productIds) {
        const options = {
            where: {
                id: productIds,
                ownerType: OwnerType.BRAND,
            },
            attributes: Product.defaultAttributes
        };

        const products = await Product.findAll(options);

        return {
            data: products,
        };
    }
    else {
        if (Array.isArray(searchQuery.filters)) {
            searchQuery.filters.push({
                field: ProductField.OWNER_TYPE,
                fuzzy: false,
                not: false,
                values: [OwnerType.BRAND],
            });
        }
        else {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Filters are supposed to be applied as an array`
            );
        }

        const {
            brands,
            products,
            total
        } = await ProductSearch(searchQuery);

        return {
            brands,
            total,
            data: products,
        };
    }
};

const searchCatalogProducts = async (
    searchQuery: SearchCriteria,
) => {
    const {
        include,
        orgId,
        productIds,
    } = searchQuery;

    if (productIds) {
        const options: FindOptions = {
            where: {
                id: productIds,
            },
        };

        if (include) {
            options.include = buildIncludes(include, orgId);
        }

        const products = await Product.findAll(options);

        return {
            data: products,
        };
    } else {
        const searchResponse = await ProductSearch(searchQuery);

        return {
            data: searchResponse.products,
            total: searchResponse.total,
        }
    }
};

const searchQueryIncludesModel = (
    searchQuery: SearchCriteria,
    model: string
) => (
    searchQuery.include &&
    (
        Array.isArray(searchQuery.include) && searchQuery.include.includes(model) ||
        _.isString(searchQuery.include) && searchQuery.include === model
    )
);

export const searchQueryToGetCatalogIdsMaxPriceByProductId: string =
    `SELECT
	"productId",
    array_to_string( array_agg("catalogId"), ',') AS "catalogId",
    MAX(price) AS "price"
    FROM
    jsonb_array_elements_text(:productIdsStr) pi
    JOIN
    "catalogProducts" cp ON cp."catalogId" IN (:catalogIds) AND cp."productId" = pi.value::int
    GROUP BY
    "productId";`
;

export const searchQueryToGetPriceTiersByProductId: string =
    `SELECT
    DISTINCT cp."productId",
    pt."label"
	FROM jsonb_array_elements_text(:productIdsStr) pi
	JOIN
    "catalogProducts" cp ON cp."catalogId" IN (:catalogIds) AND cp."productId" = pi.value::int
	JOIN
	"priceTiers" pt ON pt.id = cp."priceTierId"
	GROUP BY
    pt."label",
	cp."productId";`
;

export const getCatalogIdList: string =
    `SELECT
    "id"
    FROM "catalogs" ct
    WHERE
    ct."id"=:catalogId OR ct."parentCatalogId"=:catalogId;`
;

export function searchQueryToGetProductsByExternalId(): string {
    return `
    WITH er AS (
        SELECT
          er."externalId",
          er."productId"
        FROM
          jsonb_array_elements_text(:externalIds) js
          INNER JOIN "externalReferences" er ON er."externalId" = js.value
          AND er."type" = 'sellTreezId'
      )
      SELECT
        er."externalId",
        COUNT(*) > 1 AS "hasMultipleProductEntries"
      FROM
        "catalogs" c
        JOIN "catalogProducts" cp ON (
          cp."catalogId" = c.id
          OR cp."catalogId" = c."parentCatalogId"
        )
        AND cp."status" = 'ACTIVE'
        JOIN er ON er."productId" = cp."productId"
      WHERE
        c.id = :catalogIds
      GROUP BY
        er."externalId";
      `
};

function downloadSearchQuery(): string {
    return `
        WITH cp AS (
            SELECT *, count(*) OVER() AS "totalCount"
            FROM "catalogProducts" cp
            WHERE cp."catalogId" = :catalogId
                AND cp.status in ('ACTIVE')
            ORDER BY cp."productId" asc
                LIMIT :limit
                OFFSET :offset
        )
        SELECT
            p.id AS "id",
            cp.id AS "catalogProductId",
            cp.price,
            cp.status,
            cp."catalogOverrides",
            p.amount,
            CASE WHEN bt.id IS NULL THEN p."attributes" ELSE bt."attributes" END AS "attributes",
            CASE WHEN bt.id IS NULL THEN p."barcodes" ELSE bt."barcodes" END AS "barcodes",
            CASE WHEN bt.id IS NULL THEN p."brandName" ELSE bt."brandName" END AS "brandName",
            CASE WHEN bt.id IS NULL THEN p."cannabis" ELSE bt."cannabis" END AS "cannabis",
            CASE WHEN bt.id IS NULL THEN p."classification" ELSE bt."classification" END AS "classification",
            CASE WHEN bt.id IS NULL THEN p."descriptions" ELSE bt."descriptions" END AS "descriptions",
            CASE WHEN bt.id IS NULL THEN p."details" ELSE bt."details" END AS "details",
            CASE WHEN bt.id IS NULL THEN p."displayName" ELSE bt."displayName" END AS "displayName",
            CASE WHEN bt.id IS NULL THEN p."eCommerceName" ELSE bt."eCommerceName" END AS "eCommerceName",
            CASE WHEN bt.id IS NULL THEN p."images" ELSE bt."images" END AS "images",
            CASE WHEN bt.id IS NULL THEN p."msrp" ELSE bt."msrp" END AS "msrp",
            CASE WHEN bt.id IS NULL THEN p."name" ELSE bt."name" END AS "name",
            CASE WHEN bt.id IS NULL THEN p."packageTracked" ELSE bt."packageTracked" END AS "packageTracked",
            CASE WHEN bt.id IS NULL THEN p."productShortCode" ELSE bt."productShortCode" END AS "productShortCode",
            CASE WHEN bt.id IS NULL THEN p."size" ELSE bt."size" END AS "size",
            CASE WHEN bt.id IS NULL THEN p."sku" ELSE bt."sku" END AS "sku",
            CASE WHEN bt.id IS NULL THEN p."subtype" ELSE bt."subtype" END AS "subtype",
            CASE WHEN bt.id IS NULL THEN p."tpc" ELSE bt."tpc" END AS "tpc",
            CASE WHEN bt.id IS NULL THEN p."type" ELSE bt."type" END AS "type",
            CASE WHEN bt.id IS NULL THEN p."upc" ELSE bt."upc" END AS "upc",
            p."createdAt",
            p."externalId",
            p."linkedTo",
            p."ownerId",
            p."ownerType",
            p."productOverrides",
            cp."priceTierId",
            p.uom,
            exwm."externalId" as "weedMapsProductVariantId",
            p.visible,
            extz."externalId" AS uuid,
            CASE p."updatedAt" > cp."updatedAt" WHEN true THEN p."updatedAt" WHEN false THEN cp."updatedAt" END AS "updatedAt",
            p."mergedTo",
            cp."totalCount"
        FROM
            cp
        JOIN products p ON p.id = cp."productId"
        LEFT JOIN products bt ON p."linkedTo" = bt.id
        LEFT JOIN "externalReferences" exwm ON exwm."productId" = p.id
            AND exwm.type = 'weedMapsProductVariantId'
        LEFT JOIN LATERAL (
          SELECT
            "externalId",
            "productId"
          FROM
            "externalReferences"
          WHERE
            "type" = 'sellTreezId'
            AND "productId" = p.id
          ORDER BY
            "createdAt" DESC
          LIMIT
            1
        ) extz ON extz."productId" = p.id;
    `
};

export default searchProducts;
