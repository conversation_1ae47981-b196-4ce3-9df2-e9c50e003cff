import {
    QueryInterface,
    Transaction
} from 'sequelize';

const capabilities              = 'capabilities';
const type                      = 'type';
const subtype                   = 'subtype';
const capability                = 'capability';
const rules                     = 'rules';
const createdAt                 = 'createdAt';
const updatedAt                 = 'updatedAt';

export = {
    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(
            { isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE },
            async (transaction) => {
                await queryInterface.sequelize.query(`
                    INSERT INTO "${capabilities}" (
                        "${type}",
                        "${subtype}",
                        "${capability}",
                        "${rules}",
                        "${createdAt}",
                        "${updatedAt}"
                    ) VALUES
                    ('FLOWER', 'PRE-PACK', 'packaging', '{"toTypes":{"PREROLL":["FLOWER","SHAKE"],"FLOWER":["PRE-PACK","PRE-PACK SMALLS","<PERSON>A<PERSON>","STRA<PERSON> SPECIF<PERSON> SHAKE","BULK FLOWER"]}}', NOW(), NOW()),
                    ('FLOWER', 'PRE-PACK SMALLS', 'packaging', '{"toTypes":{"PREROLL":["FLOWER","SHAKE"],"FLOWER":["PRE-PACK","PRE-PACK SMALLS","SHAKE","STRAIN SPECIFIC SHAKE","BULK FLOWER"]}}', NOW(), NOW())
                `, 
                {transaction});
        });
    },
    down: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(
            { isolationLevel: Transaction.ISOLATION_LEVELS.SERIALIZABLE },
            async (transaction) => {
                await queryInterface.sequelize.query(`
                    DELETE FROM "${capabilities}"
                    WHERE "${type}" = 'FLOWER'
                    AND ("${subtype}" = 'PRE-PACK' OR "${subtype}" = 'PRE-PACK SMALLS')
                    AND "${capability}" = 'packaging'
                `, 
                {transaction});
        });
    },
}
