import { QueryInterface }               from 'sequelize';

export = {
    up: async function( queryInterface: QueryInterface) {

        await queryInterface.sequelize.query(`
             ALTER TYPE status RENAME VALUE 'DISABLED' TO 'DEACTIVATED';
        `)
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            ALTER TYPE status RENAME VALUE 'DEACTIVATED' TO 'DISABLED';
        `)
    },
}
