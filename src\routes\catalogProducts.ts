
import _                                    from 'lodash';
import {
    NextFunction,
    Request,
    Response,
    Router,
}                                           from 'express';
import { StatusCodes }                      from 'http-status-codes';
import { AuthenticatedRequest }             from '@treez/dev-pack/auth';
import {
    sendErrorResponse,
    ErrorResponse,
}                                           from '@treez/dev-pack/errors';
import logger                               from '@treez/dev-pack/logger';
import { connection, transaction }                      from '@treez/dev-pack/db';
import { getUserInfo }                      from '../lib/expressUtils';
import { ProductPermission }                from '../lib/permissions';
import {
    checkCatalogPermissions,
    checkPermissionsOnMultipleCatalogs,
}                                           from '../middleware';
import CatalogProduct, {
    bulkReassignProductPriceTiers,
    CatalogProductStatus,
    defaultCatalogProductAttributes,
    deleteCatalogProduct,
    deleteUnusedProductsFromStoreCatalog,
    deleteUnusedProductsFromOrgCatalog, updateCatalogProduct
}                                           from '../models/CatalogProduct';
import Catalog                              from '../models/Catalog';
import { Op, QueryTypes }                               from 'sequelize';
import { assertAllNotNull }                 from '../lib/commonFunctions';
import { searchQueryToGetProductsByExternalId } from '../search';


const {
    UpdateCatalog,
    DeleteCatalog,
} = ProductPermission;

const route = Router();

/**
 * @swagger
 *  components:
 *      requestBodies:
 *          CatalogProductsStatusUpdate:
 *              description: Object to communicate what products in which catalogs to update to a singular status
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              catalogIds:
 *                                  title: Catalog Ids
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              productIds:
 *                                  title: Product Ids
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              status:
 *                                  title: Product Status
 *                                  type: string
 *                                  enum: [ACTIVE, DELETED, DEACTIVED]
 */
interface CatalogProductsStatusUpdate {
    catalogIds?: number[];
    centralCatalogId?: number;
    productIds: number[];
    status: CatalogProductStatus;
}

/**
 * @swagger
 *  paths:
 *      /catalog_products:
 *          patch:
 *              summary: updates the status of multiple products across multiple catalogs. If a centralCatalogId is provided then the central catalog product and all child catalog products associated with that
 *                       centralCatalogId also have their status automatically updated.
 *              requestBody:
 *                  content:
 *                      application/json:
 *                          schema:
 *                              $ref: '#components/schemas/CatalogProduct'
 *              responses:
 *                  202:
 *                      description: Accepted changes to product statuses across all catalogs
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/CatalogProduct'
 *                  400:
 *                      description: Must have at least one catalog and product in the request.
 */
route.patch('/',
checkPermissionsOnMultipleCatalogs('body', 'catalogIds', [[UpdateCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
    try {
        const {
            catalogIds,
            productIds,
            centralCatalogId,
            status,
        } = request.body as CatalogProductsStatusUpdate;

        const userInfo = getUserInfo(request);

        assertAllNotNull([catalogIds, centralCatalogId],
            'Request body may only include either the "centralCatalogId" or "catalogIds" but not both');

        const data = await transaction(async () => {
            let catalogProducts = await queryCatalogProductBy(productIds, catalogIds, centralCatalogId);

            const shouldUpdateParentCatalogStatus = centralCatalogId == null;
            const updatedCatalogProducts = await Promise.all(
                catalogProducts.map(({ catalogId, productId }) => (
                      updateCatalogProduct(catalogId, productId, userInfo, {status}, shouldUpdateParentCatalogStatus)
                ))
            );

            return updatedCatalogProducts;
        })


        return response.json({ data });
    }
    catch (error) {
        logger.error("Error updating products' status on catalogProduct api: ", error);
        return sendErrorResponse(response, error);
    }
})

const queryCatalogProductBy = async (
    productIds: number[],
    catalogIds?: number[],
    centralCatalogId?: number
): Promise<CatalogProduct[]> => {

    const catalogProducts = await transaction(async () => {
        if (centralCatalogId != null) {
            // find the central catalog Product as well as all child catalog Products.
            return await CatalogProduct.findAll({
                attributes: defaultCatalogProductAttributes,
                where: {
                    productId: productIds,
                },
                include: [
                    {
                        model: Catalog,
                        where: {
                            [Op.or]: [
                                { parentCatalogId: centralCatalogId },
                                { id: centralCatalogId }
                            ]
                        }
                    }
                ]
            });
        }
        else if (catalogIds != null){
            return await CatalogProduct.findAll({
                attributes: defaultCatalogProductAttributes,
                where: {
                    catalogId: catalogIds,
                    productId: productIds,
                },
            });
        }

        return [];
    })

    return catalogProducts;
}

interface CatalogProductPricing {
    id:     number;
    status: CatalogProductStatus;
    price:  number | null;
    priceTierId: number | null;
}

interface CatalogDataByProduct {
    [key: number]:  CatalogProductPricing[];
}

/**
 * @swagger
 *  paths:
 *      /catalog_products:
 *          get:
 *              summary: gets products presence across catalogs
 *              parameters:
 *                - in: query
 *                  name: catalogIds
 *                  schema:
 *                      type: array
 *                      items:
 *                          type: integer
 *                - in: query
 *                  name: productIds
 *                  schema:
 *                      type: array
 *                      items:
 *                          type: integer
 *              responses:
 *                  200:
 *                      description: Retrieved product status across catalogs
 *                      content:
 *                          application/json:
 *                              schema:
 *                                  $ref: '#/components/schemas/CatalogProduct'
 *                  400:
 *                      description: Must have at least one catalog and product in the request.
 */
route.get('/',
async (request: AuthenticatedRequest, response: Response) => {
    try {
        const {
            catalogIds,
            productIds,
        } = request.query as {
            catalogIds: string,
            productIds: string,
        }

        if ( productIds == null
            || productIds.length == 0) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Missing values for required query parameter "productIds".`
            );
        }

        const productData: CatalogDataByProduct = {};

        const productIdList = productIds.split(',');

        productIdList.forEach( (id) => {
            productData[Number(id)] = [];
        });

        if (   catalogIds == null
            || catalogIds.length == 0
        ) {
            return response.json(productData);
        }

        const catalogProducts = await CatalogProduct.findAll({
            attributes: defaultCatalogProductAttributes,
            where: {
                catalogId: catalogIds.split(','),
                productId: productIdList,
            }
        });

        catalogProducts.forEach( (catalogProduct: CatalogProduct) => {
            productData[catalogProduct.productId].push({
                id: catalogProduct.catalogId,
                status: catalogProduct.status,
                price:  catalogProduct.price,
                priceTierId: catalogProduct.priceTierId || null
            });
        })

        return response.json(productData);
    }
    catch (error) {
        logger.error("Error getting catalogProducts on catalogProduct api: ", error);
        sendErrorResponse(response, error);
    }
});

route.get('/verify',
    async (request: AuthenticatedRequest, response: Response) => {
        try {
            const {
                catalogIds,
                productIds,
            } = request.query as {
                catalogIds: string,
                productIds: string,
            }

            if (!productIds?.length) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    `Missing values for required query parameter "productIds".`
                );
            }

            const externalIds = "[" + productIds.split(',').map(productId => '"' + productId + '"').join(',') + "]";
            const productQueryResult = await connection.query(searchQueryToGetProductsByExternalId(),
                {
                    replacements: { catalogIds, externalIds },
                    type: QueryTypes.SELECT,
                }
            );
            const productVerificationResponse: any = {};
            productQueryResult.forEach((productQueryData: any) => {
                productVerificationResponse[productQueryData.externalId] = productQueryData.hasMultipleProductEntries
            });
            return response.json(productVerificationResponse);
        }
        catch (error) {
            logger.error("Error verifying catalogProducts on catalogProduct api: ", error);
            sendErrorResponse(response, error);
        }
    });

/**
 * @swagger
 *  paths:
 *      /catalog_products:
 *          delete:
 *              summary: Marks a catalogProduct DELETED and records a catalogProductChange
 *              parameters:
 *                - in: path
 *                  name: catalogId
 *                  schema:
 *                      type: integer
 *                  required: true
 *                - in: path
 *                  name: productId
 *                  schema:
 *                      type: integer
 *                  required: true
 *              responses:
 *                  204:
 *                      description: Found catalogProduct successfully deleted
 *                  400:
 *                      description: Path paramaters catalogId and productId must be valid integers
 *                  404:
 *                      description: No catalogProduct was found with supplied catalogId and productId
 */
route.delete('/:catalogId/:productId',
checkPermissionsOnMultipleCatalogs('body', 'catalogIds', [[DeleteCatalog]]),
async (request: AuthenticatedRequest, response: Response) => {
    const catalogId : number = parseInt(request.params.catalogId);
    const productId : number = parseInt(request.params.productId);
    const userInfo = getUserInfo(request);

    try {
        if (isNaN(catalogId)) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid catalogId. Please provide a valid catalogId'
            )
        }

        if (isNaN(productId)) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid productId. Please provide a valid productId'
            )
        }

        await deleteCatalogProduct(catalogId, productId, userInfo);

        return response.status(StatusCodes.NO_CONTENT).send();
    }
    catch (error) {
        logger.error(`Error deleting catalogProduct entry for catalogId <${catalogId}>, productId <${productId}> : `, error);
        sendErrorResponse(response, error);
    }
});

route.patch('/priceTiers/reassign/:orgCatalogId',
checkCatalogPermissions('orgCatalogId', [[UpdateCatalog]]),
async (request: Request<{ orgCatalogId: string },{}, Record<number,number[]>, {}>, response: Response) => {
    const {
        body: productPriceTierReassignments,
        params: {
            orgCatalogId
        }
    } = request;

    try {
        const orgCatalogIdAsNumber = Number(orgCatalogId);
        if (_.isNaN(orgCatalogIdAsNumber) || orgCatalogIdAsNumber <= 0) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid orgCatalogId. Please provide a valid orgCatalogId'
            )
        }

        await transaction(async () => {
            await bulkReassignProductPriceTiers(productPriceTierReassignments, orgCatalogIdAsNumber);
        });

        response.status(StatusCodes.ACCEPTED).send();
    }
    catch (error) {
        logger.error(`Error reassigning product price tiers ${JSON.stringify(productPriceTierReassignments)}`);
        sendErrorResponse(response, error);
    }
});

route.get('/deleteUnusedProductsEPM/:orgCatalogId',
    checkCatalogPermissions('orgCatalogId', [[UpdateCatalog]]),
    async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {

        const orgCatalogId:number = parseInt(request.params.orgCatalogId);

        try {

            if (isNaN(orgCatalogId)) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid orgCatalogId. Please provide a valid orgCatalogId'
                )
            }

            const orgCatalog = await Catalog.findByPk(orgCatalogId);

            if (orgCatalog == null) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid orgCatalogId. Please provide a valid orgCatalogId'
                )
            };
            const catalogProductList = await deleteUnusedProductsFromOrgCatalog(orgCatalog);

            response.status(StatusCodes.ACCEPTED).send(catalogProductList);
        }
        catch (error) {
            logger.error("Error getting catalogProducts on catalogProduct api: ", error);
            sendErrorResponse(response, error);
        }
    });

route.get('/deleteUnusedStoreProductEPM/:storeCatalogId',
    async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {

        const storeCatalogId:number = parseInt(request.params.storeCatalogId);

        try {
            if (isNaN(storeCatalogId)) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid storeCatalogId. Please provide a valid storeCatalogId'
                )
            }

            const storeCatalog = await Catalog.findByPk(storeCatalogId);

            if (storeCatalog == null) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid storeCatalogId. Please provide a valid storeCatalogId'
                )
            };

            const catalogProductList = await deleteUnusedProductsFromStoreCatalog(storeCatalog);

            response.status(StatusCodes.ACCEPTED).send(catalogProductList);
        }
        catch (error) {
            logger.error("Error getting catalogProducts on catalogProduct api: ", error);
            sendErrorResponse(response, error);
        }
    });

export default route;

