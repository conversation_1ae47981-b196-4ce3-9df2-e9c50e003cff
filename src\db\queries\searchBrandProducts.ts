import { OwnerType }            from "@treez/commons/sharedTypings/product";
import { SearchCriteria }       from "../../search/searchUtils";

const generateBrandProductsQuery = (searchCriteria: SearchCriteria) => {
    const {
        catalogId,
        filters = [],
    } = searchCriteria;

    /*
    hasFilterOnProductVisibility indicates if the search specifically filters on the visible field.
    
    If it doesn't the default is to only show products with a "visible" value of true. 

    If it does, the search query will return any products with visible values consistent with the filter.
    */
    
    const hasFilterOnProductVisibility = filters.some((filter) => filter.field === 'visible');

    return`
        SELECT
            p."amount",
            p."attributes",
            p."barcodes",
            p."brandName",
            p."cannabis",
            p."classification",
            p."createdAt",
            p."descriptions",
            p."details",
            p."displayName",
            p."eCommerceName",
            p."externalId",
            p."id",
            p."images",
            p."msrp",
            p."name",
            p."ownerId",
            p."ownerType",
            p."packageTracked",
            p."size",
            p."sku",
            p."subtype",
            p."tpc",
            p."type",
            p."uom",
            p."upc",
            p."updatedAt",
            p."visible",
            count(*) OVER() AS "totalCount"
        FROM "products" p
        LEFT JOIN "products" bp ON bp."id" = p."linkedTo"
        ${catalogId ? `INNER JOIN "catalogProducts" cp ON cp."productId" = p.id AND cp."catalogId" = :catalogId` : ''}
        WHERE p."ownerType" = '${OwnerType.BRAND}'
        ${!hasFilterOnProductVisibility ? 'AND p."visible" = true' : ''}
`};

export default generateBrandProductsQuery;
