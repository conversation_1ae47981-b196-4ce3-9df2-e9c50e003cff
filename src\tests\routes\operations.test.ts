import _                            from 'lodash';
import { StatusCodes }              from 'http-status-codes';
import supertest                    from 'supertest';
import {
    OwnerType,
    UOM,
}                                   from '@treez/commons/sharedTypings/product';
import Catalog                      from '../../models/Catalog';
import CatalogProduct               from '../../models/CatalogProduct';
import CatalogPriceTier             from '../../models/CatalogPriceTier';
import { addProductToCatalog }      from '../../models/CatalogProduct';
import PriceTier                    from '../../models/PriceTier';
import Product                      from '../../models/Product';
import { mockTreezAccess }          from '../../tests/testHelpers/util';
import app                          from './testApp';

const {
    ORG,
    STORE,
} = OwnerType;

const testUser = {
    userAuthId: '<EMAIL>',
};

describe('/operations endpoints', () => {
    beforeEach(() => {
        mockTreezAccess()
    });

    it(`Creates a central Catalog from multiple store Catalogs`, async () => {
        const [
            storeCatalog1,
            storeCatalog2,
            storeCatalog3,
        ] = await Promise.all([
            Catalog.build({
                name     : 'Store 1',
                ownerId  : 1,
                ownerType: STORE,
            }).save(),
            Catalog.build({
                name     : 'Store 2',
                ownerId  : 2,
                ownerType: STORE,
            }).save(),
            Catalog.build({
                name     : 'Store 3',
                ownerId  : 3,
                ownerType: STORE,
            }).save(),
        ]);

        const [
            product1,
            product2,
            product3,
            product4,
            product5,
            product6,
        ] = await Promise.all([
            Product.build({
                name: 'Product 1',
                ownerId: 1,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
            Product.build({
                name: 'Product 2',
                ownerId: 1,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
            Product.build({
                name: 'Product 3',
                ownerId: 2,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
            Product.build({
                name: 'Product 4',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
            Product.build({
                name: 'Product 5',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
            Product.build({
                name: 'Product 6',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }).save(),
        ]);

        await Promise.all([
            storeCatalog1.$add('products', [product1, product2]),
            storeCatalog2.$add('products', [product1, product2, product3]),
            storeCatalog3.$add('products', [product4, product5, product6]),
        ]);

        const response = await supertest(app)
            .post(`/operations/organization/1`)
            .send({
                name: 'Central Catalog',
                storeCatalogIds: [storeCatalog1.id, storeCatalog2.id, storeCatalog3.id],
                users: {
                    anthony: ['ADMIN', 'EDITOR'],
                    chris: ['ADMIN'],
                }
            });

        const centralCatalog = await Catalog.findByPk(response.body.id, {
            include: [
                {
                    model: Product,
                    as: 'products',
                },
                {
                    model: Catalog,
                    as: 'childCatalogs',
                }
            ]
        });

        if (centralCatalog == null) {
            fail('catalog could not be found after updating')
        }

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            name: 'Central Catalog',
            ownerType: ORG,
            ownerId: 1,
        });

        expect(centralCatalog.products).toHaveLength(6);
        expect(centralCatalog.childCatalogs).toHaveLength(3);
    });

    it(`Will throw an error if an organization already has a central catalog`, async () => {
        await Catalog.create({
            name: 'central Catalog',
            ownerId: 1,
            ownerType: ORG,
        });

        const [
            storeCatalog1,
            storeCatalog2,
            storeCatalog3,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Store 1',
                ownerId  : 1,
                ownerType: STORE,
            }),
            Catalog.create({
                name     : 'Store 2',
                ownerId  : 2,
                ownerType: STORE,
            }),
            Catalog.create({
                name     : 'Store 3',
                ownerId  : 3,
                ownerType: STORE,
            }),
        ]);

        const [
            product1,
            product2,
            product3,
            product4,
            product5,
            product6,
        ] = await Promise.all([
            Product.create({
                name: 'Product 1',
                ownerId: 1,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }),
            Product.create({
                name: 'Product 2',
                ownerId: 1,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }),
            Product.create({
                name: 'Product 3',
                ownerId: 2,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }),
            Product.create({
                name: 'Product 4',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }),
            Product.create({
                name: 'Product 5',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            }),
            Product.create({
                name: 'Product 6',
                ownerId: 3,
                ownerType: STORE,
                type: 'catridge',
                uom: UOM.EACH,
            })
        ]);

        await Promise.all([
            storeCatalog1.$add('products', [product1, product2]),
            storeCatalog2.$add('products', [product1, product2, product3]),
            storeCatalog3.$add('products', [product4, product5, product6]),
        ]);

        const response = await supertest(app)
            .post(`/operations/organization/1`)
            .send({
                name: 'Central Catalog',
                storeCatalogIds: [storeCatalog1.id, storeCatalog2.id, storeCatalog3.id]
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Creates a central Catalog and create catalogPriceTiers for each store catalogPriceTier`, async () => {
        const [
            storeCatalog1,
            storeCatalog2,
            storeCatalog3,
        ] = await Promise.all([
            Catalog.build({
                name     : 'Store 1',
                ownerId  : 1,
                ownerType: STORE,
            }).save(),
            Catalog.build({
                name     : 'Store 2',
                ownerId  : 2,
                ownerType: STORE,
            }).save(),
            Catalog.build({
                name     : 'Store 3',
                ownerId  : 3,
                ownerType: STORE,
            }).save(),
        ]);

        const PRICE_TIER_DATA = {
            "label": "60",
            "ownerId": 11,
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 16.475973,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.475973,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                }
            ]
        };

        const [
            priceTier1,
            priceTier2,
            priceTier3,
        ] = await Promise.all([
            PriceTier.build(PRICE_TIER_DATA).save(),
            PriceTier.build(PRICE_TIER_DATA).save(),
            PriceTier.build(PRICE_TIER_DATA).save()
        ]);

        await Promise.all([
            CatalogPriceTier.build({
                catalogId  : storeCatalog1.id,
                priceTierId: priceTier1.id,
            }).save(),
            CatalogPriceTier.build({
                catalogId  : storeCatalog2.id,
                priceTierId: priceTier2.id,
            }).save(),
            CatalogPriceTier.build({
                catalogId  : storeCatalog3.id,
                priceTierId: priceTier3.id,
            }).save(),
        ]);

        const response = await supertest(app)
            .post(`/operations/organization/1`)
            .send({
                name: 'Central Catalog',
                storeCatalogIds: [storeCatalog1.id, storeCatalog2.id, storeCatalog3.id]
            });

        const newCentralCatalogId = response.body.id;

        const centralCatalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: newCentralCatalogId,
            },
        })

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            name: 'Central Catalog',
            ownerType: ORG,
            ownerId: 1,
        });

        expect(centralCatalogPriceTiers).toHaveLength(3);
    });

    it(`Adds Stores Catalogs to a Organization's Catalogs as child catalogs`, async () => {
        const [
            orgCatalog,
            store1Catalog,
            store2Catalog,
            store1product,
            store2product,
        ] = await Promise.all([
            Catalog.create({
                name     : "Org catalog",
                ownerId  : 250,
                ownerType: OwnerType.ORG,
            }),
            Catalog.create({
                name     : "Store 1 catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "Store 2 catalog",
                ownerId  : 2,
                ownerType: OwnerType.STORE,
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 1 Product',
                name         : 'Store 1 Product',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 2 Product',
                name         : 'Store 2 Product',
                ownerId      : 2,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
        ]);

        await Promise.all([
            addProductToCatalog(store1Catalog, store1product.id, testUser),
            addProductToCatalog(store2Catalog, store2product.id, testUser),
        ]);


        await supertest(app)
            .post(`/operations/organization/${orgCatalog.ownerId}/stores`)
            .send({
                storeCatalogIds: [
                    store1Catalog.id,
                    store2Catalog.id,
                ]
            });

        await store1Catalog.reload();
        await store2Catalog.reload();

        // check that store catalogs were reassinged parent catalogs.
        expect(store1Catalog.parentCatalogId).toBe(orgCatalog.id);
        expect(store2Catalog.parentCatalogId).toBe(orgCatalog.id);

        const catalogProducts = await CatalogProduct.findAll({
            where: {
                productId : [store1product.id, store2product.id],
                catalogId : orgCatalog.id
            }
        });

        // checks the org catalog products were created
        expect(catalogProducts.length).toBe(2);
        expect(
            catalogProducts
            .map(product => product.productId)
            .includes(store1product.id)
        ).toBe(true);

        expect(
            catalogProducts
            .map(product => product.productId)
            .includes(store2product.id)
        ).toBe(true);
    });

    it('allows a catalog to be marked to promote when posting to /operations/organizations/:organizationId meaning it will convert all of its products into organizational products', async () => {
        const [
            store1Catalog,
            store2Catalog,
            store1product,
            store2product,
        ] = await Promise.all([
            Catalog.create({
                name     : "Store 1 catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : "Store 2 catalog",
                ownerId  : 2,
                ownerType: OwnerType.STORE,
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 1 Product',
                name         : 'Store 1 Product',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 2 Product',
                name         : 'Store 2 Product',
                ownerId      : 2,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
        ]);

        await Promise.all([
            addProductToCatalog(store1Catalog, store1product.id, testUser),
            addProductToCatalog(store2Catalog, store2product.id, testUser),
        ]);


        const response = await supertest(app)
            .post(`/operations/organization/2000`)
            .send({
                name: 'Central Catalog',
                storeCatalogIds: [
                    store1Catalog.id,
                    store2Catalog.id,
                ],
                catalogIdToPromote:store1Catalog.id,
            });

        expect(response.status).toBe(StatusCodes.CREATED);

        const orgCatalog = response.body;

        await Promise.all([
            store1Catalog.reload(),
            store2Catalog.reload()
        ])

        // check that store catalogs were reassinged parent catalogs.
        expect(store1Catalog.parentCatalogId).toBe(orgCatalog.id);
        expect(store2Catalog.parentCatalogId).toBe(orgCatalog.id);

        const catalogProducts = await CatalogProduct.findAll({
            where: {
                productId : [store1product.id, store2product.id],
                catalogId : orgCatalog.id
            }
        });

        // checks the org catalog products were created
        expect(catalogProducts.length).toBe(2);
        expect(
            catalogProducts
            .map(product => product.productId)
            .includes(store1product.id)
        ).toBe(true);

        expect(
            catalogProducts
            .map(product => product.productId)
            .includes(store2product.id)
        ).toBe(true);

        await store1product.reload();

        //checks to see that the store 1 products were promoted to be the organization products.
        expect(store1product).toMatchObject({
            ownerId: orgCatalog.ownerId,
            ownerType: OwnerType.ORG,
        });
    });

    it(`allows a catalog to be cloned to create a new catalog with the same product status and prices`, async () => {
        const [
            orgCatalog,
            store1Catalog,
            store1product,
        ] = await Promise.all([
            Catalog.create({
                name     : "Org catalog",
                ownerId  : 250,
                ownerType: OwnerType.ORG,
            }),
            Catalog.create({
                name     : "Store 1 catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 1 Product',
                name         : 'Store 1 Product',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
        ]);

        await Promise.all([
            store1Catalog.update({parentCatalogId: orgCatalog.id}),
            addProductToCatalog(store1Catalog, store1product.id, testUser),
        ]);


        const response = await supertest(app)
            .post(`/operations/cloneCatalog/${store1Catalog.id}`)
            .send({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'Store 2 Catalog'
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            ownerId: 2,
            ownerType: OwnerType.STORE,
            name: 'Store 2 Catalog',
            parentCatalogId: orgCatalog.id,
        });

        const catalogProducts = await CatalogProduct.findAll();

        // checks the org catalog products were created
        expect(catalogProducts.length).toBe(3);

        await store1product.reload();

        //checks to see that the store 1 products were promoted to be the organization products.
        expect(store1product).toMatchObject({
            ownerId: orgCatalog.ownerId,
            ownerType: OwnerType.ORG,
        });
    });

    it('it does not allow you to clone a catalog that does not have a parent catalog', async () => {
        const [
            store1Catalog,
            store1product,
        ] = await Promise.all([
            Catalog.create({
                name     : "Store 1 catalog",
                ownerId  : 1,
                ownerType: OwnerType.STORE,
            }),
            Product.create({
                amount       : "0.500000",
                brandName    : 'Treez Testers',
                eCommerceName: 'Store 1 Product',
                name         : 'Store 1 Product',
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : '1g',
                type         : 'CARTRIDGE',
                uom          : 'g',
            }),
        ]);

        await addProductToCatalog(store1Catalog, store1product.id, testUser);

        const response = await supertest(app)
            .post(`/operations/cloneCatalog/${store1Catalog.id}`)
            .send({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'Store 2 Catalog'
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });
});
