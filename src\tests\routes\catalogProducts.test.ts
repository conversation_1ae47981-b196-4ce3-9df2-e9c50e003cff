import { StatusCodes }                  from 'http-status-codes';
import supertest                        from 'supertest';
import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode
}                                       from '@treez/commons/sharedTypings/priceTier';
import {
    OwnerType,
    UOM
}                                       from '@treez/commons/sharedTypings/product';
import CatalogProduct, {
    CatalogProductStatus,
    deleteUnusedProductsFromOrgCatalog,
    deleteUnusedProductsFromStoreCatalog,
} from '../../models/CatalogProduct';
import CatalogProductChange             from '../../models/CatalogProductChange';
import Product                          from '../../models/Product';
import Catalog                          from '../../models/Catalog';
import PriceTier                        from '../../models/PriceTier';
import {setupProductsAndCatalogs}       from '../../tests/testHelpers/data';
import app                              from './testApp';
import { CatalogProductAction }         from '../../lib/sharedInterfaces';

describe('/catalog_products endpoints', () => {
    it('Updates status for multiple catalogProducts.', async () => {
        const product1: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 123,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product2: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 2',
            name: 'Test Product 2',
            ownerId: 456,
            ownerType: OwnerType.ORG,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalog1 = {
            name: 'Test Org Catalog',
            ownerId: 789,
            ownerType: OwnerType.ORG,
        };

        await setupProductsAndCatalogs([product1, product2], [catalog1]);

        const patchBody = {
            status: CatalogProductStatus.DEACTIVATED,
            catalogIds: [1],
            productIds: [1, 2]
        }

        const response = await supertest(app)
            .patch(`/catalog_products`)
            .send(patchBody);

        expect(response.status).toBe(StatusCodes.OK);

        const catalogProducts = await CatalogProduct.findAll({
            attributes: ['id', 'status', 'productId', 'catalogId'],
            where: {
                catalogId: 1,
                productId: [1, 2],
            },
            order: [['id', 'ASC']],
        });
        expect(catalogProducts).toMatchObject([
            {
                id: 1,
                status: CatalogProductStatus.DEACTIVATED,
                productId: 1,
                catalogId: 1,
            },
            {
                id: 2,
                status: CatalogProductStatus.DEACTIVATED,
                productId: 2,
                catalogId: 1,
            },
        ]);
    });

    it(`Gets catalog list and pricing for a selection of products and catalogs.`, async () => {
        const productForBrand1: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 123,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const productForBrand2: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 2',
            name: 'Test Product 2',
            ownerId: 123,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const productForBrand3: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 3',
            name: 'Test Product 3',
            ownerId: 555,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const productForOrg: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product Org',
            name: 'Test Product Org',
            ownerId: 456,
            ownerType: OwnerType.ORG,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalogForOrg = {
            name: 'Test Org Catalog',
            ownerId: 789,
            ownerType: OwnerType.ORG,
        };
        const catalogForStore1 = {
            name: 'Test Store Catalog 1',
            ownerId: 333,
            ownerType: OwnerType.STORE,
            parentCatalogId: 1,
        };
        const catalogForStore2 = {
            name: 'Test Store Catalog 2',
            ownerId: 999,
            ownerType: OwnerType.STORE,
            parentCatalogId: 1,
        };

        const {
            catalogs,
            products,
        } = await setupProductsAndCatalogs(
            [
                productForBrand1,
                productForBrand2,
                productForBrand3,
                productForOrg,
            ],
            [
                catalogForOrg,
                catalogForStore1,
                catalogForStore2,
            ],
            false
        );

        const [
            orgCatalog,
            catalog1,
            catalog2,
        ] = catalogs;

        const [
            product1,
            product2,
            product3,
            product4,
        ] = products;

        await orgCatalog.$add('products', products);
        await catalog1.$add('products', [product1, product2, product3]);
        await catalog2.$add('products', [product3, product4]);

        const response = await supertest(app)
            .get('/catalog_products?catalogIds=1,2,3&productIds=1,2,3,4');

        expect(response.body).toMatchObject({
            1: [
                {
                    id: 1,
                    price: null,
                },
                {
                    id: 2,
                    price: "0.000000",
                },
            ],
            2: [
                {
                    id: 1,
                    price: null,
                },
                {
                    id: 2,
                    price: "0.000000",
                },
            ],
            3: [
                {
                    id: 1,
                    price: null,
                },
                {
                    id: 2,
                    price: "0.000000",
                },
                {
                    id: 3,
                    price: "0.000000",
                },
            ],
            4: [
                {
                    id: 1,
                    price: null,
                },
                {
                    id: 3,
                    price: "0.000000",
                },
            ],
        });
    });

    it(`Rejects matrix request missing productIds.`, async () => {
        const response = await supertest(app)
            .get('/catalog_products?catalogIds=1,2,3');

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Deletes a catalog product.`, async () => {
        const productForBrand1: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 123,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalogForOrg = {
            name: 'Test Org Catalog',
            ownerId: 789,
            ownerType: OwnerType.ORG,
        };
        const catalogForStore1 = {
            name: 'Test Store Catalog 1',
            ownerId: 333,
            ownerType: OwnerType.STORE,
            parentCatalogId: 1,
        };

        const {
            catalogs,
            products,
        } = await setupProductsAndCatalogs(
            [
                productForBrand1,
            ],
            [
                catalogForOrg,
                catalogForStore1,
            ],
            false
        );


        const [
            orgCatalog,
            catalog1,
        ] = catalogs;

        await orgCatalog.$add('products', products);
        await catalog1.$add('products', products);

        let catalogProducts = await CatalogProduct.findAll({
            where: {
                productId: 1,
                catalogId: 2
            }
        });

        const catalogId = catalogProducts[0].getDataValue("catalogId");
        const productId = catalogProducts[0].getDataValue("productId");

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);

        expect(deleteResponse.status).toBe(StatusCodes.NO_CONTENT);

        catalogProducts = await CatalogProduct.findAll({
            where: {
                productId,
                catalogId,
            }
        });

        expect((catalogProducts.filter((catalogProduct: CatalogProduct) => catalogProduct.status != CatalogProductStatus.DELETED)).length).toEqual(0);
    });

    it("Records a catalogProductChange after deleting a catalogProduct", async () => {
        const productForBrand1: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount: "0.500000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 123,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalogForOrg = {
            name: 'Test Org Catalog',
            ownerId: 789,
            ownerType: OwnerType.ORG,
        };
        const catalogForStore1 = {
            name: 'Test Store Catalog 1',
            ownerId: 333,
            ownerType: OwnerType.STORE,
            parentCatalogId: 1,
        };

        const {
            catalogs,
            products,
        } = await setupProductsAndCatalogs(
            [
                productForBrand1,
            ],
            [
                catalogForOrg,
                catalogForStore1,
            ],
            false
        );


        const [
            orgCatalog,
            catalog1,
        ] = catalogs;

        await orgCatalog.$add('products', products);
        await catalog1.$add('products', products);

        let catalogProducts = await CatalogProduct.findAll({
            where: {
                productId: 1,
                catalogId: 2
            }
        });

        const catalogProductId = catalogProducts[0].getDataValue("id");
        const catalogId = catalogProducts[0].getDataValue("catalogId");
        const productId = catalogProducts[0].getDataValue("productId");

        await supertest(app).delete(`/catalog_products/${catalogId}/${productId}`);

        const catalogProductChanges = await CatalogProductChange.findAll({
            where: {
                catalogProductId,
                actionType: CatalogProductAction.DELETE
            }
        });

        expect(catalogProductChanges.length).toBe(1);
        expect(catalogProductChanges[0].actionType).toBe(CatalogProductAction.DELETE);
        expect(catalogProductChanges[0].oldCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(catalogProductChanges[0].newCatalogProduct.status).toBe(CatalogProductStatus.DELETED);
    });

    it("Rejects deleting a resource that does not exist and returns a 404 with a message", async () => {
        const catalogId = 1;
        const productId = 1;

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);

        expect(deleteResponse.status).toBe(StatusCodes.NOT_FOUND);
        expect(deleteResponse.body.message).toBe(`Product with the id ${productId} could not be found in catalog ${catalogId}`);
    });

    it("Rejects deleting a catalogProduct with a null catalogId and returns a 401 with a message", async () => {
        const catalogId = "null";
        const productId = 1;

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);

        expect(deleteResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(deleteResponse.body.message).toBe('Invalid catalogId. Please provide a valid catalogId');
    });

    it("Rejects deleting a catalogProduct with a null productId and returns a 401 with a message", async () => {
        const catalogId = 1;
        const productId = "null";

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);


        expect(deleteResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(deleteResponse.body.message).toBe('Invalid productId. Please provide a valid productId');
    });


    it("Rejects deleting a catalogProduct with an invalid catalogId and returns a 401 with a message", async () => {
        const catalogId = "not-valid";
        const productId = 1;

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);

        expect(deleteResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(deleteResponse.body.message).toBe('Invalid catalogId. Please provide a valid catalogId');
    });

    it("Rejects deleting a catalogProduct with an invalid productId and returns a 401 with a message", async () => {
        const catalogId = 1;
        const productId = "not-valid";

        const deleteResponse = await supertest(app)
            .delete(`/catalog_products/${catalogId}/${productId}`);

        expect(deleteResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(deleteResponse.body.message).toBe('Invalid productId. Please provide a valid productId');
    });

    it("Updates the priceTier Id's for existing catalog products", async () => {
        const PriceTierTemplate = {
            label: "RipOff Tier",
            ownerId: 91,
            ownerType: OwnerType.ORG,
            isActive: true,
            rangeMode: RangeMode.FIXED_PRICE,
            thresholdType: PriceTierThresholdType.FLAT,
            method: PriceTierMethod.WEIGHT,
            thresholds: [
                {
                    value: 4,
                    start: 1,
                    end: 3.5
                },
                {
                    value: 3,
                    start: 3.5,
                    end: 5
                },
                {
                    value: 1.5,
                    start: 5,
                    end: null
                }
            ]
        };

        const priceTier1 = await PriceTier.create(PriceTierTemplate);
        const priceTier2 = await PriceTier.create(PriceTierTemplate);

        const [
            product1,
            product2,
            product3,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.STORE,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        // create child catalogs
        const childCatalog1 = await Catalog.create({
            name:               "Test Org Catalog 2",
            ownerId:            56,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        const childCatalog2 = await Catalog.create({
            name:               "Test Child Catalog 2",
            ownerId:            57,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        let catalogProduct1 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: product1.id,
            price: 24,
            priceTierId: priceTier1.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        });


        // create catalogProducts
        let catalogProduct2 = await CatalogProduct.create({
            catalogId: childCatalog1.id,
            productId: product2.id,
            price: 24,
            priceTierId: priceTier1.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        let catalogProduct3 = await CatalogProduct.create({
            catalogId: childCatalog2.id,
            productId: product3.id,
            price: 24,
            priceTierId: priceTier1.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        const patchResponse = await supertest(app)
            .patch(`/catalog_products/priceTiers/reassign/${orgCatalog.id}`)
            .send({
                [priceTier2.id] : [product1.id, product2.id, product3.id]
            });


        catalogProduct1 = await CatalogProduct.findByPk(catalogProduct1.id) as CatalogProduct;
        catalogProduct2 = await CatalogProduct.findByPk(catalogProduct2.id) as CatalogProduct;
        catalogProduct3 = await CatalogProduct.findByPk(catalogProduct3.id) as CatalogProduct;

        expect(patchResponse.status).toBe(StatusCodes.ACCEPTED);
        expect(catalogProduct1.priceTierId).toBe(priceTier2.id);
        expect(catalogProduct2.priceTierId).toBe(priceTier2.id);
        expect(catalogProduct3.priceTierId).toBe(priceTier2.id);
    });

    it("Sets org catalogProducts status to deleted if not used by any store catalogs on EPM", async () => {
        const [
            orgProduct1,
            orgProduct2,
            orgProduct3,
            storeProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Org Product 1",
                ownerId:            111,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Org Product 2",
                ownerId:            111,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Org Product 3",
                ownerId:            111,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Store Product",
                ownerId:            56,
                ownerType:          OwnerType.STORE,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            111,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        // create child catalogs
        const childCatalog1 = await Catalog.create({
            name:               "Test Child Catalog 1",
            ownerId:            56,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        const childCatalog2 = await Catalog.create({
            name:               "Test Child Catalog 2",
            ownerId:            57,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        // create catalogProducts org
        let orgCatalogProduct1 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct1.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });
        let orgCatalogProduct2 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct2.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });
        let orgCatalogProduct3 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct3.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        // create catalogProducts store
        let storeCatalogProduct1 = await CatalogProduct.create({
            catalogId: childCatalog1.id,
            productId: orgProduct2.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        let storeCatalogProduct2 = await CatalogProduct.create({
            catalogId: childCatalog2.id,
            productId: orgProduct3.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        let storeCatalogProduct3 = await CatalogProduct.create({
            catalogId: childCatalog1.id,
            productId: storeProduct.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        await deleteUnusedProductsFromOrgCatalog(orgCatalog);

        orgCatalogProduct1 = await CatalogProduct.findByPk(orgCatalogProduct1.id) as CatalogProduct;
        orgCatalogProduct2 = await CatalogProduct.findByPk(orgCatalogProduct2.id) as CatalogProduct;
        orgCatalogProduct3 = await CatalogProduct.findByPk(orgCatalogProduct3.id) as CatalogProduct;

        storeCatalogProduct1 = await CatalogProduct.findByPk(storeCatalogProduct1.id) as CatalogProduct;
        storeCatalogProduct2 = await CatalogProduct.findByPk(storeCatalogProduct2.id) as CatalogProduct;
        storeCatalogProduct3 = await CatalogProduct.findByPk(storeCatalogProduct3.id) as CatalogProduct;

        //only the catalogProduct 1 has a product which is not connected to any other store catalogs so it will be the only one deleted
        expect(orgCatalogProduct1.status).toBe('DELETED');
        expect(orgCatalogProduct2.status).toBe('ACTIVE');
        expect(orgCatalogProduct3.status).toBe('ACTIVE');

        expect(storeCatalogProduct1.status).toBe('ACTIVE');
        expect(storeCatalogProduct2.status).toBe('ACTIVE');
        expect(storeCatalogProduct3.status).toBe('ACTIVE');
    });

    it("Setting status of organization catalogProducts to deleted for products that are owned by stores passed", async () => {
        const [
            storeProduct1,
            storeProduct2,
            orgCatalog,
            orgProduct
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Store Product 1",
                ownerId:            111,
                ownerType:          OwnerType.STORE,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Store Product 2",
                ownerId:            111,
                ownerType:          OwnerType.STORE,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            110,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Org Product",
                ownerId:            110,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            })
        ]);

        // create child catalogs
        const storeCatalog1 = await Catalog.create({
            name:               "Store Catalog 1",
            ownerId:            111,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        const storeCatalog2 = await Catalog.create({
            name:               "Store Catalog 2",
            ownerId:            112,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        //  connecting product1 to org catalog
        let orgCatalogProduct1 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: storeProduct1.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });
        //  connecting product2 to org catalog
        let orgCatalogProduct2 = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: storeProduct2.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });
        //  connecting org product to org catalog
        let orgCatalogProduct = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        //connecting product 1 to store catalog 1
        let storeCatalogProduct11 = await CatalogProduct.create({
            catalogId: storeCatalog1.id,
            productId: storeProduct1.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        //connecting product 2 to store catalog 2
        let storeCatalogProduct22 = await CatalogProduct.create({
            catalogId: storeCatalog2.id,
            productId: storeProduct2.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        //  connecting product2 to store catalog 1
        let storeCatalogProduct21 = await CatalogProduct.create({
            catalogId: storeCatalog1.id,
            productId: storeProduct2.id,
            price: 24,
            status: 'ACTIVE',
            catalogOverrides: {},
        });

        await deleteUnusedProductsFromStoreCatalog(storeCatalog1);

        storeCatalogProduct11 = await CatalogProduct.findByPk(storeCatalogProduct11.id) as CatalogProduct;
        storeCatalogProduct22 = await CatalogProduct.findByPk(storeCatalogProduct22.id) as CatalogProduct;
        storeCatalogProduct21 = await CatalogProduct.findByPk(storeCatalogProduct21.id) as CatalogProduct;
        orgCatalogProduct2 = await CatalogProduct.findByPk(orgCatalogProduct2.id) as CatalogProduct;
        orgCatalogProduct1 = await CatalogProduct.findByPk(orgCatalogProduct1.id) as CatalogProduct;
        orgCatalogProduct = await CatalogProduct.findByPk(orgCatalogProduct.id) as CatalogProduct;

        expect(storeCatalogProduct11.status).toBe('DELETED');
        expect(storeCatalogProduct22.status).toBe('ACTIVE');
        expect(storeCatalogProduct21.status).toBe('ACTIVE');
        expect(orgCatalogProduct2.status).toBe('ACTIVE');
        expect(orgCatalogProduct1.status).toBe('DELETED');
        expect(orgCatalogProduct.status).toBe('ACTIVE');
    });

    it("see if /deleteUnusedStoreProductEPM/:storeCatalogId can handle Nan storeCatalogId", async () => {
        let storeId = '1234oiuoij'
        const getResponse = await supertest(app)
            .get(`/catalog_products/deleteUnusedStoreProductEPM/${storeId}`)
            .send();

        expect(getResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(getResponse.body.message).toBe('Invalid storeCatalogId. Please provide a valid storeCatalogId');
    });

    it("see if /deleteUnusedStoreProductEPM/:storeCatalogId can corrupt storeCatalogId", async () => {
        let storeId = new Catalog(undefined)
        const getResponse = await supertest(app)
            .get(`/catalog_products/deleteUnusedStoreProductEPM/${storeId}`)
            .send();

        expect(getResponse.status).toBe(StatusCodes.BAD_REQUEST);
        expect(getResponse.body.message).toBe('Invalid storeCatalogId. Please provide a valid storeCatalogId');
    });
});
