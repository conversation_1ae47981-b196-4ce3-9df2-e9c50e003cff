import {
    QueryInterface,
}                                       from 'sequelize';
import logger                           from '@treez/dev-pack/logger';
import { OwnerType }                    from '@treez/commons/sharedTypings/product';

const catalogs            = 'catalogs';
const priceTiers          = 'priceTiers';
const productRequirements = 'productRequirements';
const products            = 'products';

const {
    ORG,
    STORE,
    BRAND,
} = OwnerType;

export = {
    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await fixIds(queryInterface, productRequirements);
            await fixIds(queryInterface, priceTiers);
            await fixIds(queryInterface, products);
            await fixIds(queryInterface, catalogs);
        });
    },

    down: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await rollbackIds(queryInterface, catalogs);
            await rollbackIds(queryInterface, products);
            await rollbackIds(queryInterface, priceTiers);
            await rollbackIds(queryInterface, productRequirements);
        });
    },
};

const fixIds = async (
    queryInterface: QueryInterface,
    tableName: string,
) => {
    logger.info(`Normalizing entity IDs for ${tableName}...`);

    await queryInterface.sequelize.query(`
        UPDATE "${tableName}"
        SET "ownerId" = CASE
            WHEN "ownerType" = '${ORG}' THEN CONCAT('1', LPAD("ownerId"::VARCHAR,3,'0'))::INTEGER
            WHEN "ownerType" = '${STORE}' THEN CONCAT('2', LPAD("ownerId"::VARCHAR,3,'0'))::INTEGER
            ELSE CONCAT('3', LPAD("ownerId"::VARCHAR, 3,'0'))::INTEGER END
        WHERE "ownerType" IN ('${ORG}', '${STORE}', '${BRAND}')
        AND "ownerId" IS NOT NULL;
    `);
};

const rollbackIds = async (
    queryInterface: QueryInterface,
    tableName: string,
) => {
    logger.info(`Rolling back IDs to previous values before migration 20210726763423 for table ${tableName}`);

    await queryInterface.sequelize.query(`
        UPDATE "${tableName}"
        SET "ownerId" = RIGHT("ownerId"::VARCHAR, 3)::INTEGER
        WHERE "ownerId" IS NOT NULL;
    `);
}
