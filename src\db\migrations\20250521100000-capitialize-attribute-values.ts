import { QueryInterface } from "sequelize";
import { Sequelize } from "sequelize-typescript";

export = {
    up: async function (queryInterface: QueryInterface, sequelize: Sequelize) {
        await queryInterface.sequelize.query(`
            UPDATE "products" 
            SET attributes = jsonb_build_object( 
                'effect', (
                    SELECT jsonb_agg(upper(value::text)) 
                    FROM jsonb_array_elements_text(attributes->'effect') AS value 
                    WHERE attributes ? 'effect' AND jsonb_typeof(attributes->'effect') = 'array' 
                ), 
                'flavor', (
                    SELECT jsonb_agg(upper(value::text)) 
                    FROM jsonb_array_elements_text(attributes->'flavor') AS value 
                    WHERE attributes ? 'flavor' AND jsonb_typeof(attributes->'flavor') = 'array' 
                ), 
                'general', (
                    SELECT jsonb_agg(upper(value::text)) 
                    FROM jsonb_array_elements_text(attributes->'general') AS value 
                    WHERE attributes ? 'general' AND jsonb_typeof(attributes->'general') = 'array' 
                ), 
                'ingredient', (
                    SELECT jsonb_agg(upper(value::text)) 
                    FROM jsonb_array_elements_text(attributes->'ingredient') AS value 
                    WHERE attributes ? 'ingredient' AND jsonb_typeof(attributes->'ingredient') = 'array' 
                ), 
                'internal', (
                    SELECT jsonb_agg(upper(value::text)) 
                    FROM jsonb_array_elements_text(attributes->'internal') AS value 
                    WHERE attributes ? 'internal' AND jsonb_typeof(attributes->'internal') = 'array' 
                ) 
            ) 
            WHERE attributes IS NOT NULL 
              AND (
                (attributes ? 'effect' AND jsonb_typeof(attributes->'effect') = 'array') OR 
                (attributes ? 'flavor' AND jsonb_typeof(attributes->'flavor') = 'array') OR 
                (attributes ? 'general' AND jsonb_typeof(attributes->'general') = 'array') OR 
                (attributes ? 'ingredient' AND jsonb_typeof(attributes->'ingredient') = 'array') OR 
                (attributes ? 'internal' AND jsonb_typeof(attributes->'internal') = 'array')
            );
        `);
    },

    down: async function (queryInterface: QueryInterface, sequelize: Sequelize) {
    },
};
