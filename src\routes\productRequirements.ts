import {
    Request,
    Response,
    Router,
}                                                  from 'express';
import { StatusCodes }                             from 'http-status-codes';
import {
    sendErrorResponse,
    ErrorResponse,
}                                                  from '@treez/dev-pack/errors';
import logger                                      from '@treez/dev-pack/logger';
import Catalog                                     from '../models/Catalog';
import {
    createProductRequirements,
    findProductRequirementsById,
    findProductRequirementsByOwner,
    updateProductRequirements,
}                                                  from '../models/ProductRequirements';

const route = Router();

route.post('/', async (request: Request, response: Response) => {
    const {
        body,
    } = request;
    try {
        const newReqs = await createProductRequirements(body);
        response.status(StatusCodes.CREATED).json(newReqs);
    }
    catch (error) {
        logger.error("Error posting new state requirements to product api: ", error);

        return sendErrorResponse(response, new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            error.message
        ));
    }
});

route.patch(`/:id`,
async (request: Request, response: Response) => {
    const {
        body,
        params
    } = request;

    const { id } = params;
    try {
        const updatedStateRequirements = await updateProductRequirements(
            Number(id),
            body
        );

        response.status(StatusCodes.ACCEPTED).json(updatedStateRequirements);
    }
    catch (error) {
        logger.error("Error patching new state requirements to product api: ", error);
        return sendErrorResponse(response, error);
    }
});

route.get('/:id',
async (request: Request, response: Response) => {
    const {
        params
    } = request;

    const { id } = params;

    try {
        const stateRequirements = await findProductRequirementsById(Number(id));

        response.status(StatusCodes.OK).json(stateRequirements);
    }
    catch (error) {
        logger.error("Error getting new state requirements to product api: ", error);
        return sendErrorResponse(response, error);
    }
});

route.get('/catalog/:id', async (request: Request, response: Response) => {
    const {
        params
    } = request;

    const { id } = params;

    try {
        const catalog = await Catalog.findByPk(id);
        if(!catalog) {
            throw new ErrorResponse(StatusCodes.NOT_FOUND, `Requested catalog does not exist.`);
        }
        const stateRequirements = await findProductRequirementsByOwner(catalog.ownerId, catalog.ownerType);
        response.status(StatusCodes.OK).json(stateRequirements);
    }
    catch (error) {
        logger.error(`Error getting catalog ${id} from product api: `, error);
        return sendErrorResponse(response, error);
    }
});




export default route;
