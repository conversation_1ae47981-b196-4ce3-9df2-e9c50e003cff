import {
    CatalogProductStatus,
    OwnerType,
    UOM,
}                               from '@treez/commons/sharedTypings/product';
import { connection }           from '@treez/dev-pack/db';
import Product                  from '../../models/Product';
import CatalogProduct           from '../../models/CatalogProduct';
import Catalog                  from '../../models/Catalog';

//This is an exact copy of the query in the migration.
const CentralCatalogStatusMigrationQuery = `WITH children_catalog_products AS (
SELECT
    child_catalog_product."productId",
    child_catalog."parentCatalogId",
    CASE
        BOOL_OR(
            CASE child_catalog_product."status" WHEN '${CatalogProductStatus.ACTIVE}' THEN TRUE ELSE FALSE END
        )
    WHEN TRUE THEN '${CatalogProductStatus.ACTIVE}' ELSE '${CatalogProductStatus.DEACTIVATED}' END AS "childrenCollectiveStatus"
FROM "catalogProducts" child_catalog_product
LEFT JOIN "catalogs" child_catalog ON child_catalog_product."catalogId" = child_catalog."id"
WHERE child_catalog."parentCatalogId" IS NOT NULL
GROUP BY child_catalog_product."productId", child_catalog."parentCatalogId")

UPDATE "catalogProducts" cp
SET "status" = children_catalog_products."childrenCollectiveStatus"::status
FROM children_catalog_products
WHERE children_catalog_products."parentCatalogId" = cp."catalogId"
AND children_catalog_products."productId" = cp."productId"`

describe(`Translates parent catalog products to the consensus status of children catalog's catalog product status for those same products`, () => {
    it(`it updates a central catalog product to DEACTIVATED if all the children catalog products are DEACTIVATED`, async () => {
        const [
            goeOrgCatalog,
            goeStoreCatalog,
            goaStoreCatalog,
            sparcOrgCatalog,
            sparcSantaRosaCatalog,
            sparcMissionCatalog,
            goeProduct,
            sparcProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'GOE central Catalog',
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.STORE,
                name: 'GOE Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'GOA Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.ORG,
                name: 'Sparc Central Catalog',
            }),
            Catalog.create({
                ownerId: 3,
                ownerType: OwnerType.STORE,
                name: 'Sparc Santa Rosa Catalog',
            }),
            Catalog.create({
                ownerId: 4,
                ownerType: OwnerType.STORE,
                name: 'Sparc Mission Catalog',
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 2,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await Promise.all([
            goeStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            goaStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            sparcSantaRosaCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            }),
            sparcMissionCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            })
        ]);

        const [
            goeOrgCatalogProduct,
            goeStoreCatalogProduct,
            goaStoreCatalogProduct,
            sparcOrgCatalogProduct,
            sparcSantaRosaCatalogProduct,
            sparcMissionCatalogProduct,
        ] = await Promise.all([
            CatalogProduct.create({
                catalogId: goeOrgCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goeStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goaStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcOrgCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcSantaRosaCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcMissionCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
        ]);

        await connection.query(CentralCatalogStatusMigrationQuery);

        await Promise.all([
            goeOrgCatalogProduct.reload(),
            goeStoreCatalogProduct.reload(),
            goaStoreCatalogProduct.reload(),
            sparcOrgCatalogProduct.reload(),
            sparcSantaRosaCatalogProduct.reload(),
            sparcMissionCatalogProduct.reload(),
        ]);

        expect(goeOrgCatalogProduct.status).toBe(CatalogProductStatus.DEACTIVATED);
        expect(sparcOrgCatalogProduct.status).toBe(CatalogProductStatus.DEACTIVATED);
    });

    it(`it updates the central catalog product status to ACTIVE if one of the children catalog products are ACTIVE`, async () => {
        const [
            goeOrgCatalog,
            goeStoreCatalog,
            goaStoreCatalog,
            sparcOrgCatalog,
            sparcSantaRosaCatalog,
            sparcMissionCatalog,
            goeProduct,
            sparcProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'GOE central Catalog',
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.STORE,
                name: 'GOE Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'GOA Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.ORG,
                name: 'Sparc Central Catalog',
            }),
            Catalog.create({
                ownerId: 3,
                ownerType: OwnerType.STORE,
                name: 'Sparc Santa Rosa Catalog',
            }),
            Catalog.create({
                ownerId: 4,
                ownerType: OwnerType.STORE,
                name: 'Sparc Mission Catalog',
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 2,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await Promise.all([
            goeStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            goaStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            sparcSantaRosaCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            }),
            sparcMissionCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            })
        ]);

        const [
            goeOrgCatalogProduct,
            goeStoreCatalogProduct,
            goaStoreCatalogProduct,
            sparcOrgCatalogProduct,
            sparcSantaRosaCatalogProduct,
            sparcMissionCatalogProduct,
        ] = await Promise.all([
            CatalogProduct.create({
                catalogId: goeOrgCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goeStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goaStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcOrgCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcSantaRosaCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcMissionCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
        ]);

        await connection.query(CentralCatalogStatusMigrationQuery);

        await Promise.all([
            goeOrgCatalogProduct.reload(),
            goeStoreCatalogProduct.reload(),
            goaStoreCatalogProduct.reload(),
            sparcOrgCatalogProduct.reload(),
            sparcSantaRosaCatalogProduct.reload(),
            sparcMissionCatalogProduct.reload(),
        ]);

        expect(goeOrgCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(sparcOrgCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
    });

    it(`it updates the central catalog product status to ACTIVE if all the children catalog products are ACTIVE`, async () => {
        const [
            goeOrgCatalog,
            goeStoreCatalog,
            goaStoreCatalog,
            sparcOrgCatalog,
            sparcSantaRosaCatalog,
            sparcMissionCatalog,
            goeProduct,
            sparcProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'GOE central Catalog',
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.STORE,
                name: 'GOE Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'GOA Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.ORG,
                name: 'Sparc Central Catalog',
            }),
            Catalog.create({
                ownerId: 3,
                ownerType: OwnerType.STORE,
                name: 'Sparc Santa Rosa Catalog',
            }),
            Catalog.create({
                ownerId: 4,
                ownerType: OwnerType.STORE,
                name: 'Sparc Mission Catalog',
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 2,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await Promise.all([
            goeStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            goaStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            sparcSantaRosaCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            }),
            sparcMissionCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            })
        ]);

        const [
            goeOrgCatalogProduct,
            goeStoreCatalogProduct,
            goaStoreCatalogProduct,
            sparcOrgCatalogProduct,
            sparcSantaRosaCatalogProduct,
            sparcMissionCatalogProduct,
        ] = await Promise.all([
            CatalogProduct.create({
                catalogId: goeOrgCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goeStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goaStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcOrgCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcSantaRosaCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcMissionCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
        ]);

        await connection.query(CentralCatalogStatusMigrationQuery);

        await Promise.all([
            goeOrgCatalogProduct.reload(),
            goeStoreCatalogProduct.reload(),
            goaStoreCatalogProduct.reload(),
            sparcOrgCatalogProduct.reload(),
            sparcSantaRosaCatalogProduct.reload(),
            sparcMissionCatalogProduct.reload(),
        ]);

        expect(goeOrgCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(sparcOrgCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
    });

    it(`it doesn't update catalog products that are not central catalog products`, async () => {
        const [
            goeOrgCatalog,
            goeStoreCatalog,
            goaStoreCatalog,
            sparcOrgCatalog,
            sparcSantaRosaCatalog,
            sparcMissionCatalog,
            goeProduct,
            sparcProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.ORG,
                name: 'GOE central Catalog',
            }),
            Catalog.create({
                ownerId: 1,
                ownerType: OwnerType.STORE,
                name: 'GOE Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.STORE,
                name: 'GOA Store Catalog',
            }),
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.ORG,
                name: 'Sparc Central Catalog',
            }),
            Catalog.create({
                ownerId: 3,
                ownerType: OwnerType.STORE,
                name: 'Sparc Santa Rosa Catalog',
            }),
            Catalog.create({
                ownerId: 4,
                ownerType: OwnerType.STORE,
                name: 'Sparc Mission Catalog',
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "3.5",
                brandName    : "Brand Tester",
                eCommerceName: "Z0M&WHY?!",
                name         : "ZOMGWHY?!",
                ownerId      : 2,
                ownerType    : OwnerType.ORG,
                type         : "Nonsense",
                uom          : UOM.EACH,
            }),
        ]);

        await Promise.all([
            goeStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            goaStoreCatalog.update({
                parentCatalogId: goeOrgCatalog.id,
            }),
            sparcSantaRosaCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            }),
            sparcMissionCatalog.update({
                parentCatalogId: sparcOrgCatalog.id,
            })
        ]);

        const [
            goeOrgCatalogProduct,
            goeStoreCatalogProduct,
            goaStoreCatalogProduct,
            sparcOrgCatalogProduct,
            sparcSantaRosaCatalogProduct,
            sparcMissionCatalogProduct,
        ] = await Promise.all([
            CatalogProduct.create({
                catalogId: goeOrgCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goeStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: goaStoreCatalog.id,
                productId: goeProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcOrgCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.ACTIVE,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcSantaRosaCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
            CatalogProduct.create({
                catalogId: sparcMissionCatalog.id,
                productId: sparcProduct.id,
                status: CatalogProductStatus.DEACTIVATED,
                price: 0.00,
            }),
        ]);

        await connection.query(CentralCatalogStatusMigrationQuery);

        await Promise.all([
            goeOrgCatalogProduct.reload(),
            goeStoreCatalogProduct.reload(),
            goaStoreCatalogProduct.reload(),
            sparcOrgCatalogProduct.reload(),
            sparcSantaRosaCatalogProduct.reload(),
            sparcMissionCatalogProduct.reload(),
        ]);

        expect(goeOrgCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(goeStoreCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(goaStoreCatalogProduct.status).toBe(CatalogProductStatus.ACTIVE);
        expect(sparcOrgCatalogProduct.status).toBe(CatalogProductStatus.DEACTIVATED);
        expect(sparcSantaRosaCatalogProduct.status).toBe(CatalogProductStatus.DEACTIVATED);
        expect(sparcMissionCatalogProduct.status).toBe(CatalogProductStatus.DEACTIVATED);
    });
});
