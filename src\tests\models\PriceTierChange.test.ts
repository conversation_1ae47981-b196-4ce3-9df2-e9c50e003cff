import {
    PriceTierMethod,
    RangeMode,
    PriceTierThresholdType
}                                       from '@treez/commons/sharedTypings/priceTier';
import { OwnerType }                    from '@treez/commons/sharedTypings/product';
import PriceTier                        from '../../models/PriceTier';
import PriceTierChange, {
    PriceTierAction
}                                       from '../../models/PriceTierChange';

const PriceTierTemplate = {
    label: "RipOff Tier",
    ownerId: 91,
    ownerType: OwnerType.ORG,
    isActive: true,
    method: PriceTierMethod.WEIGHT,
    rangeMode: RangeMode.FIXED_PRICE,
    thresholdType: PriceTierThresholdType.FLAT,
    thresholds: [
        {
            value: 4,
            start: 1,
            end: 3.5
        },
        {
            value: 3,
            start: 3.5,
            end: 5
        },
        {
            value: 1.5,
            start: 5,
        }
    ]
};

const userInfo = {
    sellTreezUserId: '<EMAIL>',
    userAuthId: '1234'
};

describe('Price Tier Change model', () => {

    it('records a price tier change on create', async () => {
        const priceTier = await PriceTier.create(PriceTierTemplate);

        await PriceTierChange.recordPriceTierChange(
            priceTier.id,
            PriceTierAction.CREATE,
            userInfo,
            priceTier
        );

        const priceTierChanges = await PriceTierChange.findAll({
            where: {
                priceTierId: priceTier.id
            }
        });

        expect(priceTierChanges.length).toBe(1);
        expect(priceTierChanges[0]).toMatchObject({
            version: 1,
            priceTierId: priceTier.id
        });
        expect(priceTierChanges[0].changes.length).toBe(0);
    })

    it('records a price tier change on update', async () => {
        const priceTierV1 = await PriceTier.create(PriceTierTemplate);

        await PriceTierChange.recordPriceTierChange(
            priceTierV1.id,
            PriceTierAction.CREATE,
            userInfo,
            priceTierV1
        );

        const oldPriceTier = {
            ...priceTierV1.get()
        };

        const priceTierV2 = await priceTierV1.update({
            label: 'V2 label',
            isActive: false
        });

        await PriceTierChange.recordPriceTierChange(
            priceTierV2.id,
            PriceTierAction.UPDATE,
            userInfo,
            priceTierV2,
            oldPriceTier
        );

        const priceTierChanges = await PriceTierChange.findAll({
            where: {
                priceTierId: priceTierV2.id
            },
            order: [['version', 'ASC']]
        });

        expect(priceTierChanges.length).toBe(2);

        const secondChange = priceTierChanges[1];

        expect(secondChange.changes.length).toBe(2);
        expect(secondChange.version).toBe(2);

    })
});

