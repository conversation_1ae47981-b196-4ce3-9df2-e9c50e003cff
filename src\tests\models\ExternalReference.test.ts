import _                                from 'lodash';
import {
    OwnerType,
    ProductType,
    UOM,
}                                       from '@treez/commons/sharedTypings/product';
import { ExternalReferenceType }        from '../../models/ExternalReference';
import Product                          from '../../models/Product';

const {
    BRAND,
} = OwnerType;

const {
    SELL_TREEZ_ID,
    WEED_MAPS_PRODUCT_VARIANT_ID,
} = ExternalReferenceType

describe('External References model', () => {
    it(`Creates an external reference with a type`, async () => {
        const brandProduct = {
            amount:             1.000000,
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          BRAND,
            packageTracked:     false,
            cannabis:           true,
            msrp:               25.000000,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               ProductType.BEVERAGE,
            uom:                UOM.EACH,
        }

        const product = await Product.create(brandProduct);

        const externalReference =  await product.addExternalReference(
            SELL_TREEZ_ID,
        );

        expect(externalReference).not.toBeNull();
        expect(externalReference).toMatchObject({
            type     : SELL_TREEZ_ID,
            productId: product.id
        });
    });

    it(`it allows you to save a custom external id`, async () => {
        const brandProduct = {
            amount:             1.000000,
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          BRAND,
            packageTracked:     false,
            cannabis:           true,
            msrp:               25.000000,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               ProductType.BEVERAGE,
            uom:                UOM.EACH,
        }

        const product = await Product.create(brandProduct);

        const externalReference =  await product.addExternalReference(
            SELL_TREEZ_ID,
            'abcde'
        );

        expect(externalReference).not.toBeNull();
        expect(externalReference).toMatchObject({
            type      : SELL_TREEZ_ID,
            productId : product.id,
            externalId: 'abcde',
        });
    });

    it(`allows you to not enter an externalId it generates a uuid `, async () => {
        const brandProduct = {
            amount:             1.000000,
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          BRAND,
            packageTracked:     false,
            cannabis:           true,
            msrp:               25.000000,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               ProductType.BEVERAGE,
            uom:                UOM.EACH,
        }

        const product = await Product.create(brandProduct);

        const externalReference =  await product.addExternalReference(
            SELL_TREEZ_ID,
        );

        expect(externalReference).not.toBeNull();
        expect(externalReference.externalId).toBeDefined();
    });

    it(`Updates an external reference with WEED_MAPS_PRODUCT_VARIANT_ID type`, async () => {
        const brandProduct = {
            amount:             1.000000,
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          BRAND,
            packageTracked:     false,
            cannabis:           true,
            msrp:               25.000000,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               ProductType.BEVERAGE,
            uom:                UOM.EACH,
        }

        const product = await Product.create(brandProduct);

        const externalReference =  await product.updateExternalReference(
            WEED_MAPS_PRODUCT_VARIANT_ID,
        );

        expect(externalReference).not.toBeNull();
        expect(externalReference).toMatchObject({
            type     : WEED_MAPS_PRODUCT_VARIANT_ID,
            productId: product.id,
        });
    });
});

