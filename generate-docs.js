const swaggerJsdoc = require('swagger-jsdoc');
const fs = require('fs');

const options = {
    swaggerDefinition: {
        openapi: '3.0.0',
        info: {
            title: 'Treez Product API',
            version: '1.0.0', // TODO Match up the version of product API
        },
    },
    apis: ['./src/**/*.ts'],
};

const swaggerSpecification = swaggerJsdoc(options);

fs.writeFile("./src/openapi.json", JSON.stringify(swaggerSpecification), function(err) {
    if(err) {
        return console.log(err);
    }
    console.log(`Open API json spec generated for - Product API. Available at /public/docs`)
});

