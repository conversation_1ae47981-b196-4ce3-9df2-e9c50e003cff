import {
    autoP<PERSON><PERSON>ield,
    createUpdateColumns,
    decimalField,
    enumField,
    foreign<PERSON>ey,
    intField,
    jsonField,
    notNull,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { UOM }                          from "@treez/commons/sharedTypings/product";
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize) {
        await queryInterface.createTable('products', {
            id:                         autoPKField,
            amount:                     notNull(decimalField),
            attributes:                 jsonField,
            brandId:                    notNull(stringField),
            brandName:                  notNull(stringField),
            classification:             stringField,
            descriptions:               jsonField,
            details:                    jsonField,
            displayName:                notNull(stringField),
            images:                     json<PERSON>ield,
            name:                       not<PERSON>ull(stringField),
            productShortCode:           stringField,
            size:                       stringField,
            sku:                        notNull(stringField),
            status:                     stringField,
            subtype:                    stringField,
            tpc:                        string<PERSON>ield,
            type:                       string<PERSON>ield,
            uom:                        notNull(enumField(UOM)),
            upc:                        string<PERSON>ield,
            uuid:                       stringField,
            visibility:                 stringField,

            ...createUpdateColumns
        });

        // I really try to avoid raw SQL, but it appears that none of the major js ORMs have
        // support for TSVECTOR columns.
        await queryInterface.sequelize.query(`ALTER TABLE products ADD COLUMN "searchVector" TSVECTOR`);

        await queryInterface.sequelize.query(`CREATE INDEX "searchIndex" ON products USING gin("searchVector")`);

        await queryInterface.createTable('catalogs', {
            id:                         autoPKField,
            name:                       notNull(stringField),
            source:                     notNull(stringField),
            sourceId:                   notNull(stringField),

            ...createUpdateColumns
        });

        await queryInterface.createTable('catalogProducts', {
            id:                         autoPKField,
            catalogId:                  foreignKey(intField, 'catalogs', 'id'),
            productId:                  foreignKey(intField, 'products', 'id'),

            ...createUpdateColumns
        });

        await queryInterface.createTable('entityReference', {
            id:                         autoPKField,
            catalogId:                  foreignKey(intField, 'catalogs', 'id'),
            entityId:                   notNull(stringField),
            ...createUpdateColumns
        });

        await queryInterface.addConstraint('products', {
            fields: ['brandId', 'sku'],
            type: 'unique',
            name: 'brandId_sku_unique_constraint',
        });

        await queryInterface.addIndex('products', ['name']);
        await queryInterface.addIndex('products', ['subtype']);
        await queryInterface.addIndex('products', ['uuid']);
    },

    down: async function( queryInterface: QueryInterface, sequelize: Sequelize) {
        await queryInterface.dropTable('catalogProducts');
        await queryInterface.dropTable('entityReference');
        await queryInterface.dropTable('products');
        await queryInterface.dropTable('catalog');
    }
}
