import {
    OwnerType,
    ProductType,
    UOM,
}                               from '@treez/commons/sharedTypings/product';
import Catalog                  from '../../models/Catalog';
import {
    addProductToCatalog,
    CatalogProductStatus,
}                               from '../../models/CatalogProduct';
import Product, {
    linkProducts,
}                               from '../../models/Product';
import SuggestedLink, {
    MatchType,
}                               from '../../models/SuggestedLink';

const userInfo = {
    userAuthId: 'abc!123',
    sellTreezUserId: 'christoMashagano',
};

const testBrandProduct1 = {
    brandName: 'Bhang',
    ownerId  : 777,
    ownerType: OwnerType.BRAND,
    type     : ProductType.CARTRIDGE,
    amount   : 1,
    uom      : UOM.G,
    name     : 'Hella High 311',
};

const testBrandProduct2 = {
    brandName: 'Bhang',
    ownerId  : 777,
    ownerType: OwnerType.BRAND,
    type     : ProductType.EDIBLE,
    amount   : 1000,
    uom      : UOM.MG,
    name     : `<PERSON><PERSON>'s Edibles`,
};

const testBrandProduct3 = {
    brandName: 'Bhang',
    ownerId  : 777,
    ownerType: OwnerType.BRAND,
    type     : ProductType.CARTRIDGE,
    amount   : .5,
    uom      : UOM.G,
    name     : 'Maxi Tears'
};

const testRetailProduct1 = {
    brandName: 'Bhang',
    ownerId  : 13,
    ownerType: OwnerType.STORE,
    type     : ProductType.CARTRIDGE,
    amount   : 1,
    uom      : UOM.G,
    name     : 'Hella High',
};

const testRetailProduct2 = {
    brandName: 'Bhahng',
    ownerId  : 13,
    ownerType: OwnerType.STORE,
    type     : ProductType.EDIBLE,
    amount   : 1,
    uom      : UOM.G,
    name     : `Nachi's Edibles`,  
};

const testRetailProduct3 = {
    brandName: 'Bhang',
    ownerId  : 13,
    ownerType: OwnerType.STORE,
    type     : ProductType.CARTRIDGE,
    amount   : .5,
    uom      : UOM.G,
    name     : 'Tears de Maxi'
};

const retailCatalog = {
    name     : 'Retail Catalog',
    ownerId  : 13,
    ownerType: OwnerType.STORE,
};

describe(`Suggested Link Model`, () => {
    it(`throws an error when a retail product (either org or store ownerType) is used as the brandProductId`, async () => {
        const [
            retailProduct1,
            retailProduct2,
        ] = await Promise.all([
            Product.create(testRetailProduct1),
            Product.create(testRetailProduct2),
        ]);

        await expect(SuggestedLink.addBrandSuggestedLink(retailProduct1.id, retailProduct2.id)).rejects.toThrowError();
    });

    it(`throws an error when using brand product as the "productToLinkId`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create(testBrandProduct1),
            Product.create(testBrandProduct2),
        ]);

        await expect(SuggestedLink.addBrandSuggestedLink(brandProduct1.id, brandProduct2.id)).rejects.toThrowError();
    });

    it(`refreshWeightedMatches method does not insert a weighted match for products that already are linked`, async () => {
        const [
            brandProduct,
            retailProduct,
            catalog,
        ] = await Promise.all([
            Product.create(testBrandProduct1),
            Product.create(testRetailProduct1),
            Catalog.create(retailCatalog),
        ]);

        await Promise.all([
            addProductToCatalog(catalog, retailProduct.id, userInfo),
            linkProducts(retailProduct.id, brandProduct.id, userInfo),
        ]);

        await SuggestedLink.refreshWeightedMatches();

        const suggestedLinks = await SuggestedLink.findAll();

        expect(suggestedLinks).toHaveLength(0);
    });

    it(`refreshWeightedMatches method does not insert a weighted match for products that already have a suggested linked`, async () => {
        const [
            brandProduct,
            retailProduct,
            catalog,
        ] = await Promise.all([
            Product.create(testBrandProduct1),
            Product.create(testRetailProduct1),
            Catalog.create(retailCatalog),
        ]);

        await Promise.all([
            addProductToCatalog(catalog, retailProduct.id, userInfo),
        ]);

        await SuggestedLink.create({
            brandProductId: brandProduct.id,
            productToLinkId: retailProduct.id,
            type: MatchType.BrandSuggested,
        });

        await SuggestedLink.refreshWeightedMatches();

        const suggestedLinks = await SuggestedLink.findAll({
            where: {
                type: MatchType.Weighted,
            }
        });

        expect(suggestedLinks).toHaveLength(0);
    });

    it(`refreshWeightedMatches method does not insert weighted matches for products that are not active`, async () => {
        const [
            retailProduct,
            catalog,
        ] = await Promise.all([
            Product.create(testRetailProduct1),
            Catalog.create(retailCatalog),
            Product.create(testBrandProduct1),
        ]);

        await addProductToCatalog(catalog, retailProduct.id, userInfo, {status: CatalogProductStatus.DEACTIVATED});

        await SuggestedLink.refreshWeightedMatches();

        const suggestedLinks = await SuggestedLink.findAll({
            where: {
                type: MatchType.Weighted,
            }
        });

        expect(suggestedLinks).toHaveLength(0);
    });

    it(`refreshWeightedMatches method does not insert weighted matches for matches that have already been rejected`, async () => {
        const [
            retailProduct,
            catalog,
            brandProduct,
        ] = await Promise.all([
            Product.create(testRetailProduct1),
            Catalog.create(retailCatalog),
            Product.create(testBrandProduct1),
        ]);

        await addProductToCatalog(catalog, retailProduct.id, userInfo);

        await SuggestedLink.create({
            brandProductId: brandProduct.id,
            productToLinkId: retailProduct.id,
            type: MatchType.Rejected,
        });

        await SuggestedLink.refreshWeightedMatches();

        const suggestedLinks = await SuggestedLink.findAll({
            where: { type: MatchType.Weighted }
        });

        expect(suggestedLinks).toHaveLength(0);
    });

    it(`Deletes all weighted matches and creates new ones without matching rejected pairs or brand suggested matches`, async () => {
        const [
            brandProduct1,
            brandProduct3,
            retailProduct1,
            retailProduct2,
            retailProduct3,
            catalog,
        ] = await Promise.all([
            Product.create(testBrandProduct1),
            Product.create(testBrandProduct3),
            Product.create(testRetailProduct1),
            Product.create(testRetailProduct2),
            Product.create(testRetailProduct3),
            Catalog.create(retailCatalog),
        ]);

        await Promise.all([
            addProductToCatalog(catalog, retailProduct1.id, userInfo),
            addProductToCatalog(catalog, retailProduct2.id, userInfo),
            addProductToCatalog(catalog, retailProduct3.id, userInfo),
        ]);

        await SuggestedLink.bulkCreate([
            {
                brandProductId : brandProduct1.id,
                productToLinkId: retailProduct2.id,
                type           : MatchType.Weighted,
                rankWeight     : 1.7,
            },
            {
                brandProductId : brandProduct3.id,
                productToLinkId: retailProduct3.id,
                type           : MatchType.BrandSuggested,
            },
            {
                brandProductId : brandProduct3.id,
                productToLinkId: retailProduct2.id,
                type           : MatchType.Rejected,                
            },
        ]);


        await SuggestedLink.refreshWeightedMatches();

        const [
            brandSuggestedLinks,
            weightedLinks,
            rejectedLinks,
        ] = await Promise.all([
            SuggestedLink.findAll({
                where: {
                    type: MatchType.BrandSuggested,
                }
            }),
            SuggestedLink.findAll({
                where: {
                    type: MatchType.Weighted
                },
                order: [['type', 'DESC']],
            }),
            SuggestedLink.findAll({
                where: {
                    type: MatchType.Rejected,
                }
            })
        ]);

        //expect all rejected and suggested links to remain
        expect(brandSuggestedLinks).toHaveLength(1);
        expect(brandSuggestedLinks).toMatchObject([{
            brandProductId: brandProduct3.id,
            productToLinkId: retailProduct3.id,
            type: MatchType.BrandSuggested,
        }]);

        expect(rejectedLinks).toHaveLength(1);
        expect(rejectedLinks).toMatchObject([{
            brandProductId : brandProduct3.id,
            productToLinkId: retailProduct2.id,
            type           : MatchType.Rejected, 
        }])

        expect(weightedLinks).toHaveLength(1);
        expect(weightedLinks).toMatchObject([
            {
                brandProductId : brandProduct1.id,
                productToLinkId: retailProduct1.id,
                type           : MatchType.Weighted,
            },
        ]);
    });
})