import _ from "lodash";

const generateUnlinkedSearch = (
    uuidCondition: string,
    statusFilter: string,
) => (`
    SELECT
        p.id AS "id",
        cp.id AS "catalogProductId",
        cp.price,
        cp.status,
        cp."catalogOverrides",
        cp."priceTierId",
        p.amount,
        p.attributes,
        p.barcodes,
        p."brandName",
        p.cannabis,
        p.classification,
        p."createdAt",
        p.descriptions,
        p.details,
        p."displayName",
        p."eCommerceName",
        p."externalId",
        p.images,
        p."linkedTo",
        p.msrp,
        p.name,
        p."ownerId",
        p."ownerType",
        p."productOverrides",
        p."packageTracked",
        p."productShortCode",
        p.size,
        p.sku,
        p.subtype,
        p.tpc,
        p.type,
        p.uom,
        p.upc,
        p.visible,
        exwm."externalId" as "weedMapsProductVariantId",
        extz."externalId" AS uuid,
        CASE p."updatedAt" > cp."updatedAt"
            WHEN true THEN p."updatedAt"
            WHEN false THEN cp."updatedAt"
        END AS "updatedAt",  
        p."mergedTo",
        count(*) OVER() AS "totalCount"
    FROM products p
    LEFT JOIN products bt ON p."linkedTo" = bt.id
    LEFT JOIN "catalogProducts" cp ON cp."productId" = p.id
    LEFT JOIN "catalogs" c ON c.id = cp."catalogId"
    LEFT JOIN "externalReferences" exwm ON
        exwm."productId"     = p.id
        AND exwm.type        = 'weedMapsProductVariantId'
    LEFT JOIN LATERAL (
        SELECT
            "externalId",
            "productId"
        FROM "externalReferences"
        WHERE "type"    = 'sellTreezId'
        AND "productId" = p.id
        ${uuidCondition}
    ) extz ON extz."productId" = p.id
    WHERE cp."catalogId" = :catalogId ${statusFilter}
    ${uuidCondition === `ORDER BY "createdAt" DESC LIMIT 1` ? ''
    : 'AND extz."externalId" IS NOT NULL'}
`);

export default generateUnlinkedSearch;