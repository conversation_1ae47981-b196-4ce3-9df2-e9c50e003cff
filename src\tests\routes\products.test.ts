import { StatusCodes }                  from 'http-status-codes';
import supertest                        from 'supertest';
import {
    OwnerType,
    ProductType,
    UOM,
}                                       from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode,
}                                       from '@treez/commons/sharedTypings/priceTier';
import _                                from 'lodash';
import Catalog                          from '../../models/Catalog';
import CatalogProduct, {
    getCatalogProduct,
    CatalogProductStatus,
    addProductToCatalog,
}                                       from '../../models/CatalogProduct';
import ExternalReference, {
    ExternalReferenceType
}                                       from '../../models/ExternalReference';
import Product, {
    linkProducts,
    updateProductInCatalog,
}                                       from '../../models/Product';
import PriceTier                        from '../../models/PriceTier';
import ProductUpdateOutbox              from '../../models/ProductUpdateOutbox';
import {
    setupProductsAndCatalogs,
    brandProduct,
}                                       from '../testHelpers/data';
import { SearchType }                   from '../../search/searchUtils';
import SuggestedLink, {
    MatchType,
}                                       from '../../models/SuggestedLink';
import Capability                       from '../../models/Capability';
import app                              from './testApp';

export const brandProducts: unknown[] = [{
    amount:             "1.000000",
    attributes:         {general: ["TEST"]},
    brandName:          "Treez Tester Nonsense",
    barcodes:           [{sku: "SKUXOZEHXKS1"}],
    details:            {for_test: "a detail"},
    eCommerceName:      "Absolute Nonsense",
    name:               "Absolute Nonsense",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "10 piece bucket",
    sku:                "SKUXOZEHXKS1",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure1",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "A bit of Nonsense",
    name:               "A bit of Nonsense",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "25 piece bucket",
    sku:                "SKUXOZEHXKS2",
    subtype:            "Utter Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure2",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "Blue Gummies",
    name:               "Blue Gummies",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKS3",
    subtype:            "Modest Nonsense",
    type:               "Nonsense",
    tpc:                "YeahOkSure3",
    uom:                UOM.EACH,
},
{
    amount:             "1.000000",
    brandName:          "Treez Tester",
    eCommerceName:      "Z0M&WHY?!",
    name:               "ZOMGWHY?!",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    size:               "100 piece bucket",
    sku:                "SKUXOZEHXKS4",
    subtype:            "Filler",
    type:               "Nonsense",
    tpc:                "YeahOkSure4",
    uom:                UOM.EACH,
}];

const directionAsc = <const>"asc";
const createdAt = <const>"createdAt";
const PAGE_SIZE = 2;

const testUser = {userAuthId: '<EMAIL>'};


const testUserInfo = {
    userAuthId: 'abc',
    sellTreezId: '123',
} as UserInfo;

describe('/products endpoints', () => {
    it('Gets the brand-level information for a product', async () => {
        const product = await Product.create({
            amount:             "1.000000",
            attributes:         {
                flavor: ['pine', 'cedar', 'sandlewoood'],
                ingredient: ['alpha', 'chocolate'],
            },
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "Z0M&WHY?!",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products/${product.id}`);

        expect(response.body).toMatchObject({
            amount:             "1.000000",
            attributes:         {
                flavor: ['pine', 'cedar', 'sandlewoood'],
                ingredient: ['alpha', 'chocolate'],
            },
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "Z0M&WHY?!",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        });
    });

    it('Rejects attempt to get the brand-level information for a product that does not exist', async () => {
        const response = await supertest(app)
            .get('/products/1000');

        expect(response.status).toBe(StatusCodes.NOT_FOUND);
        expect(response.body).toMatchObject({
            code:    StatusCodes.NOT_FOUND,
            message: "Product with the id of 1000 could not be found",
        });
    });

    it('Gets product with all catalogs it is assigned to', async () => {
        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        const catalog1 = await Catalog.create({
            name:               "Test Brand Catalog",
            ownerId:            1,
            ownerType:          OwnerType.BRAND,
        });

        const catalog2 = await Catalog.create({
            name:               "Test Brand Catalog 2",
            ownerId:            2,
            ownerType:          OwnerType.BRAND,
        });

        await catalog1.$add('products', [product.id]);
        await catalog2.$add('products', [product.id]);

        const response = await supertest(app)
            .get(`/products/${product.id}/?include=catalogs`);

        expect(response.body).toMatchObject({
            amount       : "50.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });
        expect(response.body).toHaveProperty('catalogs');
        expect(response.body.catalogs).toHaveLength(2);
    });

    it('Gets product with all store catalogs it is assigned to', async () => {
        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        const catalog1 = await Catalog.create({
            name:               "Test Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        });

        const catalog2 = await Catalog.create({
            name:               "Test Catalog 2",
            ownerId:            2,
            ownerType:          OwnerType.STORE,
            parentCatalogId: catalog1.id,
        });

        await catalog1.$add('products', [product.id]);
        await catalog2.$add('products', [product.id]);


        const response = await supertest(app)
            .get(`/products/${product.id}/?include=catalogs&orgId=${catalog1.ownerId}`);

        expect(response.body).toMatchObject({
            amount       : "50.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });
        expect(response.body.catalogs).toHaveLength(1);
    });

    it('Updates a product with a tag attribute via patch', async () => {
        const product = await Product.create(brandProduct);
        await Catalog.create({
            name: 'test catalog',
            ownerId: brandProduct.ownerId,
            ownerType: brandProduct.ownerType,
        });

        const response = await supertest(app)
            .patch(`/products/${product.id}`)
            .send({
                eCommerceName:  "Something Reasonable",
                name:           "Something Reasonable",
                attributes: {
                    ...brandProduct.attributes,
                    internal: ['this is for wade'],
                }
            })

        const updatedProduct = await Product.findByPk(product.id);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            data: {
                ...brandProduct,
                eCommerceName:      "Something Reasonable",
                name:               "Something Reasonable",
                attributes: {
                    flavor: ['PINE', 'CEDAR', 'SANDLEWOOOD'],
                    ingredient: ['ALPHA', 'CHOCOLATE'],
                    internal: ['THIS IS FOR WADE'],
                }
            },
        });
        expect(_.get(updatedProduct, 'dataValues')).toMatchObject({
            ...brandProduct,
            eCommerceName:      "Something Reasonable",
            name:               "Something Reasonable",
            attributes: {
                ...brandProduct.attributes,
                internal: ['this is for wade'],
            }
        });
    });

    it('Updates a product via patch', async () => {
        const catalog = await Catalog.create({
            name: 'test Catalog',
            ownerId: 1,
            ownerType: OwnerType.ORG
        });

        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', [product.id]);

        const response = await supertest(app)
            .patch(`/products/${product.id}`)
            .send({
                eCommerceName: "Playa Cream",
                name         : "THC infused Playa Cream",
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            data: {
                amount       : "50.000000",
                brandName    : "Brand Tester",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "50 piece bucket",
                type         : "Edible",
                uom          : UOM.EACH,
                eCommerceName: "Playa Cream",
                name         : "THC infused Playa Cream",
            },
        });
    });

    it(`Rejects attempt to update a product with invalid field`, async () => {
        const catalog = await Catalog.create({
            name: 'test Catalog',
            ownerId: 1,
            ownerType: OwnerType.ORG
        });

        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', [product.id]);

        const response = await supertest(app)
            .patch(`/products/${product.id}`)
            .send({
                name: null,
            });

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    });

    it(`returns ExternalReference externalId as uuid on patch`, async () => {
        const catalog = await Catalog.create({
            name: 'test Catalog',
            ownerId: 1,
            ownerType: OwnerType.ORG
        });

        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        await catalog.$add('products', [product.id]);

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcdefg'
        );

        const response = await supertest(app)
            .patch(`/products/${product.id}`)
            .send({
                eCommerceName: "Playa Cream",
                name         : "THC infused Playa Cream",
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data.uuid).not.toBeNull();
    });

    it('Gets product info by id, with overrides applied', async () => {
        const product = await Product.create({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });

        const catalog = await Catalog.create({
            name:               "Test Brand Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        });

        await catalog.$add('products', [product.id]);

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        }) as CatalogProduct;

        catalogProduct.catalogOverrides = {
            eCommerceName: "Store Preferred Name",
        };
        await catalogProduct.save();

        const response = await supertest(app)
            .get(`/products/${product.id}?catalogId=${catalog.id}`);

        expect(response.body).toMatchObject({
            amount       : "50.000000",
            brandName    : "Brand Tester",
            eCommerceName: "Store Preferred Name",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        });
    });

    it(`Searches a catalog, returning information with brand Treez Linked information applied.`, async () => {
        const [
            catalog,
            orgProduct,
            brandProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 1',
                name: 'Test Product 1',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const searchObject = {
            catalogId: catalog.id,
            searchString: 'Test Product 2',
        };

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:               [{
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }],
            total: 1,
        });
    });

    it(`Searches a catalog, returning information with brand Treez Linked information applied but with product overrides taken precidence and then catalog overrides after that`, async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 1',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 2',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
            catalogOverrides: {
                eCommerceName: 'Catalog Override Name',
            }
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        await orgProduct.update({
            productOverrides: {
                eCommerceName: 'Product Override',
                name: 'Org Decided this name',
            }
        });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                catalogId: catalog.id,
                searchString: 'Test Product 2',
            })});

        expect(response.body).toMatchObject({
            data:               [{
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Catalog Override Name',
                name: 'Org Decided this name',
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }],
            total: 1,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Searches for products, order by rank`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                page: 1,
                pageSize: PAGE_SIZE,
                searchString: "Nonsense",
                orderByRank: true,
            })});

        expect(response.body).toMatchObject({
            data: brandProducts.slice(0, 2),
            total: brandProducts.length,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Searches for products with full name`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                page: 1,
                pageSize: PAGE_SIZE,
                searchString: "Blue Gummies",
            })});

        expect(response.body).toMatchObject({
            data: brandProducts.slice(2, 3),
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Searches for products with invalid characters that are ignored`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                page: 1,
                pageSize: PAGE_SIZE,
                searchString: "Blue (Gummies!)",
            })});

        expect(response.body).toMatchObject({
            data: brandProducts.slice(2, 3),
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Searches for products using pagination (later pages)`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                page: 2,
                pageSize: PAGE_SIZE,
                searchString: "Nonsense",
                orderByRank: true,
            })});

        expect(response.body).toMatchObject({
            data: brandProducts.slice(2, 4),
            total: brandProducts.length,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Gets products using pagination and sort`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                page: 1,
                pageSize: PAGE_SIZE,
                searchString: "",
                sortBy: {
                    field: 'sku',
                    order: directionAsc,
                },
            })});

        expect(response.body).toMatchObject({
            data: brandProducts.slice(0, PAGE_SIZE),
            total: brandProducts.length,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Ignores search term madeup only of whitespace`, async () => {
        await Product.create({
            amount:             "1.000000",
            attributes:         {
                flavor: ['pine', 'cedar', 'sandlewoood'],
                ingredient: ['alpha', 'chocolate'],
            },
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:        "Z0M&WHY?!",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`)
            .query({
                query: JSON.stringify({searchString: '     ' })
            });

        expect(response.body).toMatchObject({
            data: [{
                amount:             "1.000000",
                attributes:         {
                    flavor: ['pine', 'cedar', 'sandlewoood'],
                    ingredient: ['alpha', 'chocolate'],
                },
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:        "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                cannabis:           true,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`Searches for products using filter`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                searchString: '',
                filters: [
                    {
                        field: 'size',
                        fuzzy: false,
                        values: [(brandProducts as Partial<Product>[])[1].size],
                    },
                ],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [brandProducts[1]],
            total: 1,
        });
    });

    it(`Searches for products using brand filter`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        await Product.create({
            amount:             "1.000000",
            attributes:         {
                flavor: ['pine', 'cedar', 'sandlewoood'],
                ingredient: ['alpha', 'chocolate'],
            },
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "Z0M&WHY?!",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                searchString: '',
                filters: [{
                        field: 'brandName',
                        fuzzy: false,
                        values: ["Brand Tester"],
                }],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                amount:             "1.000000",
                attributes:         {
                    flavor: ['pine', 'cedar', 'sandlewoood'],
                    ingredient: ['alpha', 'chocolate'],
                },
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                cannabis:           true,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`permits searches with single quotation marks in the search query`, async () => {
        await Product.create({
            name:               "Product ' This Searches",
            ownerId:            1,
            ownerType:          OwnerType.BRAND,
            type:               "Cartridge",
            uom:                UOM.EACH,
        });

        const searchObject = {
            searchString: "Product ' ",
        };

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product ' This Searches",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                type:               "Cartridge",
                uom:                UOM.EACH,
            }]
        });
    })

    it(`permits searches with colons in the search query`, async () => {
        await Product.build({
            name:               "Product: This Searches",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            type:               "Nonsense",
            uom:                UOM.EACH,
        }).save();

        const searchObject = {
            searchString: 'Product: ',
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product: This Searches",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`permits searches by words or parts of words in any part if a product or brand name in the search query`, async () => {
        await Product.build({
            name:               "Product $$$ This Searches",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            type:               "Nonsense",
            uom:                UOM.EACH,
        }).save();

        const searchObject = {
            searchString: '$$$ This',
        }

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product $$$ This Searches",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`permits searches with the character | in the search query`, async () => {
        await Product.build({
            name:               "Product| This Searches",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            type:               "Nonsense",
            uom:                UOM.EACH,
        }).save();

        const searchObject = {
            searchString: 'Product| ',
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product| This Searches",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`permits searches with the character < in the search query`, async () => {
        await Product.build({
            name:               "Product< This Searches",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            type:               "Nonsense",
            uom:                UOM.EACH,
        }).save();

        const searchObject = {
            searchString: `Product< `,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product< This Searches",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`permits searches with the character ' in the search query`, async () => {
        await Product.build({
            name:               "Product' This Searches",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            type:               "Nonsense",
            uom:                UOM.EACH,
        }).save();

        const searchObject = {
            searchString: `Product' `,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                name:               "Product' This Searches",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`Searches for products using filter and search term`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                searchString: 'Absolute',
                filters: [
                    {
                        field: 'subtype',
                        fuzzy: false,
                        values: [(brandProducts as Partial<Product>[])[0].subtype],
                    },
                ],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [brandProducts[0]],
            total: 1,
        });
    });

    it(`Searches for products using attribute filter`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const searchObject = {
            searchString: 'Absolute',
            filters: [
                {
                    field: 'attribute.general',
                    fuzzy: false,
                    values: [(brandProducts as Partial<Product>[])[0].attributes!["general"]],
                },
            ],
        };

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [brandProducts[0]],
            total: 1,
        });
    });

    it(`Searches for products using detail filter`, async () => {
        await Product.bulkCreate(brandProducts as Partial<Product>[], { individualHooks: true });

        const searchObject = {
            searchString: 'Absolute',
            filters: [
                {
                    field: 'details.for_test',
                    fuzzy: false,
                    //@ts-ignore
                    values: [(brandProducts as Partial<Product>[])[0].details!["for_test"]],
                },
            ],
        };

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [brandProducts[0]],
            total: 1,
        });
    });

    it(`Searches for products with empty filters, which are properly ignored`, async () => {
        await Product.create({
            amount:             "1.000000",
            attributes:         {
                flavor: ['pine', 'cedar', 'sandlewoood'],
                ingredient: ['alpha', 'chocolate'],
            },
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Edible",
            uom:                UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                searchString: "Edible",
                filters: [],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: [{
                amount:             "1.000000",
                attributes: {
                    flavor: ['pine', 'cedar', 'sandlewoood'],
                    ingredient: ['alpha', 'chocolate'],
                },
                barcodes:           [{sku: "SKUXOZEHXKS"}],
                brandName:          "Brand Tester",
                eCommerceName:      "ZOMG",
                name:               "ZOMGWHY?!",
                ownerId:            7224,
                ownerType:          OwnerType.BRAND,
                cannabis:           true,
                size:               "50 piece bucket",
                sku:                "SKUXOZEHXKS",
                subtype:            "Utter Nonsense",
                type:               "Edible",
                uom:                UOM.EACH,
            }],
            total: 1,
        });
    });

    it(`Searches using rank and sortBy list`, async () => {
        await setupProductsAndCatalogs(brandProducts as Partial<Omit<Product, 'amount'> & { amount: string }>[]);

        const sortedBrandProducts = _.sortBy(brandProducts, ['size', 'tpc']);

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                searchString: "bucket",
                orderByRank: true,
                sortBy: [{
                    field: "size",
                    order: directionAsc,
                },
                {
                    field: "tpc",
                    order: directionAsc,
                }],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:  sortedBrandProducts,
            total: brandProducts.length,
        });
    });

    it(`Searches using both rank and other sort`, async () => {
        await setupProductsAndCatalogs(brandProducts as Partial<Omit<Product, 'amount'> & { amount: string }>[]);

        const sortedBrandProducts = _.sortBy(brandProducts, ['size'])

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                searchString: "bucket",
                orderByRank: true,
                sortBy: {
                    field: "size",
                    order: directionAsc,
                },
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:  sortedBrandProducts,
            total: brandProducts.length,
        });
    });

    it(`Searches using request body`, async () => {
        await setupProductsAndCatalogs(brandProducts as Partial<Omit<Product, 'amount'> & { amount: string }>[]);

        const sortedBrandProducts = _.sortBy(brandProducts, ['size'])

        const response = await supertest(app)
            .get(`/products`)
            .query({})
            .send({
                searchString: "bucket",
                orderByRank: true,
                sortBy: {
                    field: "size",
                    order: directionAsc,
                },
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:  sortedBrandProducts as Product[],
            total: brandProducts.length,
        });
    });

    it(`Search via POST request`, async () => {
        await setupProductsAndCatalogs(brandProducts as Partial<Omit<Product, 'amount'> & { amount: string }>[]);

        const sortedBrandProducts = _.sortBy(brandProducts, ['size'])

        const response = await supertest(app)
            .post(`/products/search`)
            .query({})
            .send({
                searchString: "bucket",
                orderByRank: true,
                sortBy: {
                    field: "size",
                    order: directionAsc,
                },
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:  sortedBrandProducts,
            total: brandProducts.length,
        });
    });

    it(`Searches but finds nothing`, async () => {
        await Product.create({
            amount     : "50",
            brandName  : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                searchString: "bucket",
                filters: [{
                    field: "type",
                    values: ["CARTRIDGE", "EXTRACT"],
                    fuzzy: false,
                }],
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(0);
    });

    it(`Rejects invalid search format (improperly formatted fields)`, async () => {
        await Product.build({
            amount     : "50",
            brandName  : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        }).save();

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                filters: `This isn't supposed to be a string`,
            })});

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Rejects invalid filter field`, async () => {
        await Product.create({
            amount     : "50",
            brandName  : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`)
            .query({query: JSON.stringify({
                searchString: '',
                filters: [{
                    field: "metatype",
                    values: ["Utter Nonsense"],
                    fuzzy: true,
                }],
            })});

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Rejects invalid sort field`, async () => {
        await Product.create({
            amount     : "50",
            brandName  : "Brand Tester",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        });

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                searchString: '',
                sortBy: {
                    field: "fish",
                    order: directionAsc,
                },
            })});

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

//LINKING PRODUCT
    it('Links an org product to a brand product.', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 1',
                name: 'Test Product 1',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            productId: orgProduct.id,
            catalogId: catalog.id,
            status: CatalogProductStatus.ACTIVE,
        });

        const response = await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`);

        const linkedProduct = await Product.findByPk(
            orgProduct.id, {
                include: [
                    {
                        model: Product,
                        as: 'linkedBrandProduct',
                    }
                ]
            }
        );

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(_.get(linkedProduct, 'dataValues[linkedTo]')).toBe(brandProduct.id);
        expect(_.get(linkedProduct, 'dataValues[linkedBrandProduct[id]]')).toBe(brandProduct.id);
    });

    it('records a change in the ProductUpdateOutbox table when linking an org product to a brand product.', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 1',
                name: 'Test Product 1',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            productId: orgProduct.id,
            catalogId: catalog.id,
            status: CatalogProductStatus.ACTIVE,
        });

        await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`);

        const productUpdates = await ProductUpdateOutbox.findAll()

        const productUpdate = _.head(productUpdates);

        expect(productUpdates).toHaveLength(1);
        expect(_.get(productUpdate, 'payload')).toMatchObject({
            changedProductId: orgProduct.id
        });
    });

    it('Returns a product with BrandTreez linked information', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 1',
                name: 'Test Product 1',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const response = await supertest(app)
            .get(`/products/${orgProduct.id}?catalogId=${catalog.id}`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        });
    });

    it('saves product overrides upon linking and returns them correctly', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 1',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Test Product 2',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id
        });

        const linkResponse = await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`)
            .send({
                size: '1.00g',
            });

        const response = await supertest(app)
            .get(`/products/${orgProduct.id}?catalogId=${catalog.id}`);

        expect(linkResponse.status).toBe(StatusCodes.ACCEPTED);
        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Brand Preferred Name',
            name: 'Test Product 1',
            size: '1.00g',
            type: 'CARTRIDGE',
            uom: 'g',
        });
    });

    it('Rejects attempt to link a brand product to an org product.', async () => {
        const [
            orgCatalog,
            orgProduct1,
            orgProduct2
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "Because we hate databases?",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                sku:                "SKUZOMGEHXKS",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "ORG Tester",
                eCommerceName:      "Crunchy Cells",
                name:               "Crunchy Cells",
                ownerId:            2,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                sku:                "SKUWTFEHXKS",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct1.id,
        });

        const response = await supertest(app)
            .patch(`/products/${orgCatalog.id}/link/${orgProduct1.id}`)
            .send({
                productToLinkId: orgProduct2.id,
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('searches by brand linked fields', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'A',
                eCommerceName: 'Alice Apples',
                name: 'f7ield',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'Bob Bacon',
                name: 'F\ield',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id
        });

        await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`)

        const searchObject = {
            catalogId: catalog.id,
            searchString: 'f7ield',
            excludeLinkedProduct: false,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct.id);
        expect(response.body.data[0].name).toBe('f7ield');
        expect(response.body.data[0].brandName).toBe('A');
    });

    it('prevents searching linked products by their original values', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'A',
                eCommerceName: 'RAW GARDEN ORANGE PEEL',
                name: 'RAW GARDEN ORANGE PEEL',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'RAW GARDEN FRUIT FUSION',
                name: 'RAW GARDEN FRUIT FUSION',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id
        });

        await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`)

        const searchObject = {
            catalogId: catalog.id,
            searchString: 'FRUIT FUSION',
            excludeLinkedProduct: false,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(0);
    });

    it('searches by brand linked overridden fields', async () => {
        const [
            catalog,
            brandProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "0.500000",
                brandName: 'A',
                eCommerceName: 'Alice Apples',
                name: 'Alice Apples',
                ownerId: 888,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'Bob Bacon',
                name: 'Bob Bacon',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id
        });

        await supertest(app)
            .patch(`/products/${orgProduct.id}/link/${brandProduct.id}`)
            .send({
                name: 'Charlie Cookie',
            });

        const searchObject = {
            catalogId: catalog.id,
            searchString: 'Charlie',
            excludeLinkedProduct: false,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct.id);
        expect(response.body.data[0].name).toBe('Charlie Cookie');
        expect(response.body.data[0].brandName).toBe('A');
    });

    it('Gets products by catalog', async () => {
        const product = await Product.build({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.ORG,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        }).save();

        const catalog1 = await Catalog.build({
            name:               "Test Brand Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        }).save();

        await catalog1.$add('products', [product.id]);

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                catalogId: 1,
                searchString: '',
            })});

        expect(response.status).toBe(StatusCodes.OK)
        expect(response.body).toMatchObject({
            data:               [{
                amount     : "50.000000",
                brandName  : "Brand Tester",
                eCommerceName: "Nachi's Edibles",
                name       : "Nachi's Edibles",
                ownerId    : 1,
                ownerType  : OwnerType.ORG,
                size       : "50 piece bucket",
                type       : "Edible",
                uom        : UOM.EACH,
            }],
            total:              1,
        });
    });

    it('Gets products by catalog, ignoring inactive products', async () => {
        const storeCatalog = {
            name:               "Test Store Catalog",
            ownerId:            42,
            ownerType:          OwnerType.ORG,
        };
        const activeProduct: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Test Brand",
            eCommerceName:      "Active Product",
            name:               "Active Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }
        const inactiveProduct: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKX"}],
            brandName:          "Test Brand",
            eCommerceName:      "Inactive Product",
            name:               "Inactive Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "55 piece bucket",
            sku:                "SKUXOZEHXKX",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }

        await setupProductsAndCatalogs([activeProduct, inactiveProduct], [storeCatalog]);

        const inactiveCatalogProduct = await getCatalogProduct({where: { id: 2 }});

        inactiveCatalogProduct.status = CatalogProductStatus.DEACTIVATED;
        await inactiveCatalogProduct.save();

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                catalogId: 1,
                searchString: '',
            })});

        expect(response.status).toBe(StatusCodes.OK)
        expect(response.body).toMatchObject({
            data:               [activeProduct],
            total:              1,
        });
    });

    it('Gets products by catalog, including inactive products', async () => {
        const storeCatalog = {
            name:               "Test Store Catalog",
            ownerId:            42,
            ownerType:          OwnerType.STORE,
        };
        const activeProduct: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Test Brand",
            eCommerceName:      "Active Product",
            name:               "Active Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }
        const inactiveProduct: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKX"}],
            brandName:          "Test Brand",
            eCommerceName:      "Inactive Product",
            name:               "Inactive Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "55 piece bucket",
            sku:                "SKUXOZEHXKX",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }

        await setupProductsAndCatalogs([activeProduct, inactiveProduct], [storeCatalog]);

        const inactiveCatalogProduct = await getCatalogProduct({where: { id: 2 }});

        inactiveCatalogProduct.status = CatalogProductStatus.DEACTIVATED;

        await inactiveCatalogProduct.save();

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                catalogId: 1,
                searchString: '',
                status: [CatalogProductStatus.ACTIVE, CatalogProductStatus.DEACTIVATED],
            })});

        expect(response.status).toBe(StatusCodes.OK)
        expect(response.body).toMatchObject({
            data:               [activeProduct, inactiveProduct],
            total:              2,
        });
    });

    it(`Doesn't get products in an empty catalog`, async () => {
        const product = await Product.build({
            amount       : "50",
            brandName    : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name         : "Nachi's Edibles",
            ownerId      : 1,
            ownerType    : OwnerType.BRAND,
            size         : "50 piece bucket",
            type         : "Edible",
            uom          : UOM.EACH,
        }).save();

        const catalog1 = await Catalog.build({
            name:               "Test Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        }).save();

        const catalog2 = await Catalog.build({
            name:               "Test Catalog 2",
            ownerId:            2,
            ownerType:          OwnerType.STORE,
        }).save();

        await catalog1.$add('products', [product.id]);

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                catalogId: catalog2.id,
                searchString: '',
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data:               [],
            total:              0,
        });
    });

    it('Gets product by dateRange updatedAt', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        })

        const productData = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Test Brand",
            eCommerceName:      "Active Product",
            name:               "Active Product",
            ownerId:            7224,
            ownerType:          OwnerType.STORE,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH
        }

        const product = Product.build(productData);
        await product.save();

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
            createdAt: Date.now() - 1000000,
            updatedAt: Date.now() - 1000
            
        });

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'uuid'
        );       

        const response = await supertest(app)
        .get(`/products`).query({query: JSON.stringify({
            catalogId: catalog.id,
            status: [CatalogProductStatus.ACTIVE],
            dateRange: {
                field: "updatedAt",
                start: (Date.now() / 1000) - 2000,
                end: (Date.now() / 1000)
            }
        })});

        expect(response.body).toMatchObject({
            data:               [productData],
            total:              1,
        });

    });

    it('Gets products by dateRange', async () => {
        const product1Data = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Test Brand",
            eCommerceName:      "Active Product",
            name:               "Active Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }

        const product2Data = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKX"}],
            brandName:          "Test Brand",
            eCommerceName:      "Inactive Product",
            name:               "Inactive Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "55 piece bucket",
            sku:                "SKUXOZEHXKX",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }

        const product1 = Product.build(product1Data);
        await product1.save();
        product1.set({createdAt: Date.now() - 1000});
        await product1.save();

        const product2 = Product.build(product2Data);
        await product2.save();

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                dateRange: {
                    field: createdAt,
                    start: Number((product1.createdAt.valueOf() / 1000) - 2000),
                    end: Number(product2.createdAt.valueOf() / 1000),
                },
                searchType: SearchType.BrandTreez,
            })});

        expect(response.body).toMatchObject({
            data:               [product1Data],
            total:              1,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it('Gets products by dateRange with searchterm', async () => {
        const product1Data = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKS"}],
            brandName:          "Test Brand",
            eCommerceName:      "Active Product",
            name:               "Active Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            cannabis:           true,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }
        const product2Data = {
            amount:             "1.000000",
            barcodes:           [{sku: "SKUXOZEHXKX"}],
            brandName:          "Test Brand",
            eCommerceName:      "Inactive Product",
            name:               "Inactive Product",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            packageTracked:     false,
            cannabis:           true,
            size:               "55 piece bucket",
            sku:                "SKUXOZEHXKX",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        }

        const product1 = Product.build(product1Data);
        await product1.save();
        product1.set({createdAt: Date.now() - 1000});
        await product1.save();

        const product2 = Product.build(product2Data);
        await product2.save();

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify({
                dateRange: {
                    field: createdAt,
                    start: Number((product1.createdAt.valueOf() / 1000) - 2000),
                    end: Number(product2.createdAt.valueOf() / 1000),
                },
                searchString: 'product',
            })});

        expect(response.body).toMatchObject({
            data:               [product1Data],
            total:              1,
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it(`Gets distinct values for a field within a catalog`, async () => {
        const product1 = {
            amount: "0.5",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product2 = {
            amount: "1",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 2',
            name: 'Test Product 2',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product3 = {
            amount: "0.5",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalog = {
            name: 'Test Org Catalog',
            ownerId: 377,
            ownerType: OwnerType.ORG,
        };

        await setupProductsAndCatalogs([product1, product2, product3], [catalog]);

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=1&fields=name`);

        const sortedResponseNames = _.sortBy(response.body.name);

        expect(response.status).toBe(StatusCodes.OK);
        expect(sortedResponseNames).toEqual([
            'Test Product 1',
            'Test Product 2',
        ]);
    });

    it(`Gets distinct values for multiple fields within a catalog`, async () => {
        const product1 = {
            amount: "0.5",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            size: '0.5g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product2 = {
            amount: "1",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 2',
            name: 'Test Product 2',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product3 = {
            amount: "0.5",
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const catalog = {
            name: 'Test Org Catalog',
            ownerId: 377,
            ownerType: OwnerType.ORG,
        };

        await setupProductsAndCatalogs([product1, product2, product3], [catalog]);

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=1&fields=name,size`);

        const sortedResponseNames = _.sortBy(response.body.name);
        const sortedResponseSizes = _.sortBy(response.body.size);

        expect(response.status).toBe(StatusCodes.OK);
        expect(sortedResponseNames).toEqual([
            'Test Product 1',
            'Test Product 2',
        ]);
        expect(sortedResponseSizes).toEqual([
            '0.5g',
            '1g',
        ]);

    });

    it(`Gets distinct values for multiple attributes within a catalog`, async () => {
        const product1 = {
            amount: "0.5",
            attributes: {
                "effect" : [ "CHILL" , "STONEY"]
            },
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 1',
            name: 'Test Product 1',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            size: '0.5g',
            type: 'CARTRIDGE',
            uom: 'g',
        };
        const product2 = {
            amount: "1",
            attributes: {
                "flavor" : [ "DURBY" , "JACKFRUIT"]
            },
            brandName: 'Treez Testers',
            eCommerceName: 'Test Product 2',
            name: 'Test Product 2',
            ownerId: 888,
            ownerType: OwnerType.BRAND,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        };

        const catalog = {
            name: 'Test Org Catalog',
            ownerId: 377,
            ownerType: OwnerType.ORG,
        };

        await setupProductsAndCatalogs([product1, product2], [catalog]);

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=1&attributes=effect,flavor`);

        const sortedResponseEffects = _.sortBy(response.body.effect);
        const sortedResponseFlavors = _.sortBy(response.body.flavor);

        expect(response.status).toBe(StatusCodes.OK);
        expect(sortedResponseEffects).toEqual([
            'CHILL',
            'STONEY',
        ]);
        expect(sortedResponseFlavors).toEqual([
            'DURBY',
            'JACKFRUIT',
        ]);
    });

    it(`Gets distinct values within a catalog including linked products`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgProduct2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F1" , "F2"]
                },
                brandName:          "BRAND_1",
                name:               "PRODUCT_1",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               "SIZE_1",
                subtype:            "SUBTYPE_1",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F3" , "F4"]
                },
                brandName:          "BRAND_2",
                name:               "PRODUCT_2",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "SIZE_2",
                subtype:            "SUBTYPE_2",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F5" , "F6"]
                },
                brandName:          "BRAND_3",
                name:               "PRODUCT_3",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "SIZE_3",
                subtype:            "SUBTYPE_3",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);
        await orgProduct.reload();

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=${orgCatalog.id}&fields=name,brandName&attributes=flavor`);

        const sortedResponseBrandName = _.sortBy(response.body.brandName);
        const sortedResponseFlavor = _.sortBy(response.body.flavor);
        const sortedResponseName = _.sortBy(response.body.name);

        expect(response.status).toBe(StatusCodes.OK);
        expect(sortedResponseBrandName).toEqual([
            'BRAND_1',
            'BRAND_2',
            'BRAND_3',
        ]);
        expect(sortedResponseFlavor).toEqual([
            'F1',
            'F2',
            'F3',
            'F4',
            'F5',
            'F6',
        ]);
        expect(sortedResponseName).toEqual([
            'PRODUCT_1',
            'PRODUCT_2',
            'PRODUCT_3',
        ]);
    });

    it(`Gets distinct values within a catalog including linked products filtered by sellTreezId`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgProduct2,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F1" , "F2"]
                },
                brandName:          "BRAND_1",
                name:               "PRODUCT_1",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               "SIZE_1",
                subtype:            "SUBTYPE_1",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F3" , "F4"]
                },
                brandName:          "BRAND_2",
                name:               "PRODUCT_2",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "SIZE_2",
                subtype:            "SUBTYPE_2",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                attributes: {
                    "flavor" : [ "F5" , "F6"]
                },
                brandName:          "BRAND_3",
                name:               "PRODUCT_3",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "SIZE_3",
                subtype:            "SUBTYPE_3",
                type:               "FLOWER",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            '420blazeit'
        );

        await orgProduct2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            '520grazeit'
        );

        await linkProducts(orgProduct.id, brandProduct.id, testUser);
        await orgProduct.reload();

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=${orgCatalog.id}&fields=name,brandName&attributes=flavor&productIds=420blazeit`);

        const sortedResponseBrandName = _.sortBy(response.body.brandName);
        const sortedResponseFlavor = _.sortBy(response.body.flavor);
        const sortedResponseName = _.sortBy(response.body.name);

        expect(response.status).toBe(StatusCodes.OK);
        expect(sortedResponseBrandName).toEqual([
            'BRAND_1',
            'BRAND_2',
        ]);
        expect(sortedResponseFlavor).toEqual([
            'F1',
            'F2',
            'F3',
            'F4',
        ]);
        expect(sortedResponseName).toEqual([
            'PRODUCT_1',
            'PRODUCT_2',
        ]);
    });

    it(`Rejects attempt to get distinct values for a field that doesn't exist`, async () => {
        await Product.build({
            amount     : "50",
            brandName  : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        }).save();

        await Catalog.build({
            name:               "Test Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        }).save();

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=1&fields=fish`);

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Gets distinct values filtered by product selltreezId`, async () => {
        const product1: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount     : "50",
            brandName  : "Treez",
            eCommerceName: "AskTreez",
            name       : "AskTreez",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "Unknown",
            type       : "Merch",
            uom        : UOM.EACH,
        };
        const product2: Partial<Omit<Product, 'amount'> & { amount: string }> = {
            amount     : "50",
            brandName  : "Hastings' Herb",
            eCommerceName: "Wade's Wonder Weed",
            name       : "Wade's Wonder Weed",
            ownerId    : 1,
            ownerType  : OwnerType.BRAND,
            size       : "3.5 grams",
            type       : "Flower",
            uom        : UOM.EACH,
        };

        const catalog = {
            name:               "Test Catalog",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
        };

        const { products } = await setupProductsAndCatalogs([product1, product2], [catalog]);

        await products[0].addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            '420blazeit'
        );

        await products[1].addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            '520grazeit'
        );

        const response = await supertest(app)
            .get(`/products/distinct?catalogId=1&fields=name&productIds=520grazeit`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.name).toEqual([
            "Wade\'s Wonder Weed",
        ]);
    });

    it(`unlinks a product from a brandtreez product, keeping it's overrides and returning its previous information`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Real BrandName",
                eCommerceName:      "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);
        await orgProduct.reload();

        await updateProductInCatalog(
            orgProduct.id,
            orgCatalog.id,
            {
                status: CatalogProductStatus.DEACTIVATED,
                price: 24.999999,
                eCommerceName: "Nacho's Edible Snacks",
            },
            testUser
        );

        const response = await supertest(app)
            .patch(`/products/${orgProduct.id}/unlink`)

        await orgProduct.reload();

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(orgProduct.get()).toMatchObject({
            amount     : "1.000000",
            brandName  : "Brand Tester",
            eCommerceName: "Nacho's Edible Snacks",
            name       : "Ignacio's Munchies",
            ownerId    : 1,
            ownerType  : OwnerType.ORG,
            size       : "50 piece bucket",
            subtype    : "Utter Nonsense",
            type       : "Nonsense",
            uom        : UOM.EACH,
            productOverrides: null,
            linkedTo: null,
        });
    });

    it(`supports sellTreez search of UUIDs by searching external References`, async () => {
        const [
            orgCatalog,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount     : "50",
                brandName  : "Brand Tester",
                eCommerceName: "Nacho's Edibles",
                name       : "Nacho's Edibles",
                ownerId    : 1,
                ownerType  : OwnerType.ORG,
                size       : "50 piece bucket",
                type       : "Edible",
                uom        : UOM.EACH,
            }),
        ]);

        await orgCatalog.$add('product', orgProduct.id);

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abc-123'
        );

        const response = await supertest(app)
            .get(`/products`)
            .send({
                catalogId: orgCatalog.id,
                uuid: 'abc-123',
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data[0]).toMatchObject({
            amount     : "50.000000",
            brandName  : "Brand Tester",
            eCommerceName: "Nacho's Edibles",
            name       : "Nacho's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.ORG,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        });
    });

    it(`supports sellTreez search by attributes`, async () => {
        const [
            orgCatalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount     : "50",
                attributes : {
                    effect: ["WOOZY"],
                    flavor: ["CHOCOLATE", "CREAM"],
                    general: [],
                    ingredient: ["LOVE"],
                },
                brandName  : "Hastings' Herb",
                eCommerceName: "Wade's Wonder Wagonwheels",
                name       : "Wade's Wonder Wagonwheels",
                ownerId    : 1,
                ownerType  : OwnerType.ORG,
                size       : "24 pack",
                type       : "Edible",
                uom        : UOM.EACH,
            }),
            Product.create({
                amount     : "50",
                attributes : {
                    effect: [],
                    flavor: ["WEEDY"],
                    general: [],
                    ingredient: ["LOVE"],
                },
                brandName  : "Hastings' Herb",
                eCommerceName: "Wade's Wonder Whistle",
                name       : "Wade's Wonder Whistle",
                ownerId    : 1,
                ownerType  : OwnerType.ORG,
                size       : "100 pack",
                type       : "Cartridge",
                uom        : UOM.EACH,
            }),
        ]);

        await orgCatalog.$add('product', orgProduct1.id);
        await orgCatalog.$add('product', orgProduct2.id);

        const testSearchGenerator = {
            "catalogId": 1,
            "status": [
                "ACTIVE",
                "DEACTIVATED"
            ],
            "filters": [],
            "sortBy": {
                "field": "displayName",
                "order": "asc"
            },
            "offset": 0,
            "page": 1,
            "pageSize": 100,
            "paginated": false,
            "orderByRank": false
        };

        const response1 = await supertest(app)
            .get(`/products`)
            .send({
                "searchString": "CHOCO",
                ...testSearchGenerator,
            });

        expect(response1.status).toBe(StatusCodes.OK);
        expect(response1.body.data.length).toBe(1);
        expect(response1.body.data[0].name).toBe("Wade's Wonder Wagonwheels");

        const response2 = await supertest(app)
            .get(`/products`)
            .send({
                "searchString": "LOVE",
                ...testSearchGenerator,
            });

        expect(response2.status).toBe(StatusCodes.OK);
        expect(response2.body.data.length).toBe(2);
        expect(response2.body.data.map((p : Product) => p.name)).toContain("Wade's Wonder Wagonwheels");
        expect(response2.body.data.map((p : Product) => p.name)).toContain("Wade's Wonder Whistle");
    });

    //Required for ST backwards compatibility v2.9.7
    it('Updates a product price via patch', async () => {
        const catalog = await Catalog.create({
            name: 'test Catalog',
            ownerId: 1,
            ownerType: OwnerType.STORE
        });

        const product = await Product.create({
            amount     : "50",
            brandName  : "Brand Tester",
            eCommerceName: "Nachi's Edibles",
            name       : "Nachi's Edibles",
            ownerId    : 1,
            ownerType  : OwnerType.STORE,
            size       : "50 piece bucket",
            type       : "Edible",
            uom        : UOM.EACH,
        });

        await addProductToCatalog(
            catalog,
            product.id,
            testUser,
        )

        const response = await supertest(app)
            .patch(`/products/${product.id}`)
            .send({
                price: 100,
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body.data.price).toBe('100.000000');
    });

    it(`returns brand product's null over store's non-null value if not overridden`, async () => {
        const [
            brandProduct,
            orgProduct,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            null,
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                displayName:        "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        const catalog = await CatalogProduct.create({
            catalogId: orgCatalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct.id, brandProduct.id, testUser);

        const response = await supertest(app)
            .get(`/products`)
            .send({
                "searchString": "",
                "catalogId": catalog.id,
                "status": [
                    "ACTIVE",
                    "DEACTIVATED"
                ],
                "filters": [
                    {
                        "field": "id",
                        "values": [orgProduct.id],
                        "fuzzy": false
                    }
                ],
                "sortBy": {
                    "field": "displayName",
                    "order": "asc"
                },
                "offset": 0,
                "page": 1,
                "pageSize": 100,
                "paginated": false,
                "orderByRank": false
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(1)
        expect(response.body.data[0].id).toBe(orgProduct.id)
        expect(response.body.data[0].linkedTo).toBe(brandProduct.id)
        expect(response.body.data[0].size).toBe(null)
        expect(response.body.data[0].subtype).toBe(null)
        expect(response.body.data[0].amount).toBe("1.000000")
        expect(response.body.data[0].brandName).toBe("Real BrandName")
        expect(response.body.data[0].displayName).toBe("Nacho's Munchies")
        expect(response.body.data[0].name).toBe("Ignacio's Edible Snacks")
    });

    it(`returns one product despite it having multiple references`, async () => {
        const [
            catalog,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
        );

        const searchObject = {
            catalogId: catalog.id,
            searchString: 'Test Product 2',
        };

        const response = await supertest(app)
        .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0]).toMatchObject({
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Brand Preferred Name',
            name: 'Test Product 2',
            ownerId: 420,
            ownerType: OwnerType.ORG,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
        });
        expect(response.body.data[0]).toHaveProperty('uuid');
    });

    it(`will return unique brands if the search criteria contains the searchBrands flag`, async () => {
        await Promise.all([
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Nachis Edibles',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "21.000000",
                brandName: 'Nachis Edibles',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        const searchObject = {
            searchString: 'e',
            searchBrands: true,
        };

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.brands).toHaveLength(2);
    });

    it(`allows a product to have multiple sellTreezIds and finds the product, returning it only once, by each id`, async () => {
        const [
            catalog,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const sellTreezId1 = 'abcdefg';
        const sellTreezId2 = '123456';

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId1
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId2
        );

        const searchObject1 = {
            catalogId: catalog.id,
            filters: [{
                field: 'uuid',
                fuzzy: false,
                values: [sellTreezId1]
            }]
        }

        const response1 = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject1)});

        expect(response1.status).toBe(StatusCodes.OK);
        expect(response1.body.data).toHaveLength(1);
        expect(response1.body.data[0]).toMatchObject({
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Brand Preferred Name',
            name: 'Test Product 2',
            ownerId: 420,
            ownerType: OwnerType.ORG,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
            uuid: sellTreezId1
        });

        const searchObject2 = {
            catalogId: catalog.id,
            filters: [{
                field: 'uuid',
                fuzzy: false,
                values: [sellTreezId2]
            }]
        }

        const response2 = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject2)});

        expect(response2.status).toBe(StatusCodes.OK);
        expect(response2.body.data).toHaveLength(1);
        expect(response2.body.data[0]).toMatchObject({
            amount: "1.000000",
            brandName: 'Treez Testers',
            eCommerceName: 'Brand Preferred Name',
            name: 'Test Product 2',
            ownerId: 420,
            ownerType: OwnerType.ORG,
            size: '1g',
            type: 'CARTRIDGE',
            uom: 'g',
            uuid: sellTreezId2
        });
    });
    it(`returns an individual product record for each of the products sellTreezIds it is related to and are asked for`, async () => {
        const [
            catalog,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const sellTreezId1 = 'abcdefg';
        const sellTreezId2 = '123456';

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId1
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId2
        );

        const searchObject1 = {
            catalogId: catalog.id,
            filters: [{
                field: 'uuid',
                fuzzy: false,
                values: [sellTreezId1, sellTreezId2]
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject1)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(2);

        const sellTreezIds = _.map(response.body.data, 'uuid');

        expect(sellTreezIds).toEqual(expect.arrayContaining([sellTreezId1, sellTreezId2]))
    });

    it(`doesnt return too many rows when you don't limit the amount of external references and instead limit the external id values`, async () => {
        const [
            catalog,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'Treez Testers',
                eCommerceName: 'Brand Preferred Name',
                name: 'Test Product 2',
                ownerId: 420,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const sellTreezId1 = 'abcdefg';
        const sellTreezId2 = '123456';
        const sellTreezId3 = 'zxyasfda';

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId1
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId3
        );

        await orgProduct.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId2
        );

        const searchObject1 = {
            catalogId: catalog.id,
            filters: [{
                field: 'uuid',
                fuzzy: false,
                values: [sellTreezId1, sellTreezId2]
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject1)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(2);

        const sellTreezIds = _.map(response.body.data, 'uuid');

        expect(sellTreezIds).toEqual(expect.arrayContaining([sellTreezId1, sellTreezId2]))
    });

    it(`returns products with visible field`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: false,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const sellTreezId1 = 'abcdefg';
        const sellTreezId2 = '123456';

        await orgProduct1.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId1
        );

        await orgProduct2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId2
        );

        const searchObject1 = {
            catalogId: catalog.id,
            filters: [{
                field: 'uuid',
                fuzzy: false,
                values: [sellTreezId1, sellTreezId2]
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject1)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(2);

        const product1 = _.find(response.body.data, p => p.id === orgProduct1.id);
        expect(product1.visible).toBe(true);

        const product2 = _.find(response.body.data, p => p.id === orgProduct2.id);
        expect(product2.visible).toBe(false);
    });

    it(`it searches with filter not in ID - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: false,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'id',
                fuzzy : false,
                not   : true,
                values: [orgProduct1.id],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(orgProduct1.id);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
    });

    it(`it searches with filter not in UUID - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const sellTreezId1 = 'abcdefg';
        const sellTreezId2 = '123456';

        await orgProduct1.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId1
        );

        await orgProduct2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            sellTreezId2
        );

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'uuid',
                fuzzy : false,
                not   : true,
                values: [sellTreezId1],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].uuid).toBe(sellTreezId2);
    });

    it(`it searches with filter not in linkedTo - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'C',
                eCommerceName: 'C',
                name: 'Charlie Cereal',
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'D',
                eCommerceName: 'D',
                name: 'Douglas Donut',
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await linkProducts(orgProduct1.id, brandProduct1.id, testUser);
        await linkProducts(orgProduct2.id, brandProduct2.id, testUser);

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'linkedTo',
                fuzzy : false,
                not   : true,
                values: [brandProduct1.id],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].linkedTo).not.toBe(brandProduct1.id);
    });

    it(`it searches with filter not in type - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'FLOWER',
                uom: 'g',
                visible: true,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'type',
                fuzzy : false,
                not   : true,
                values: ['CARTRIDGE'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(orgProduct1.id);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].type).toBe('FLOWER');
    });

    it(`it searches with filter not in visible - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'FLOWER',
                uom: 'g',
                visible: false,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'visible',
                fuzzy : false,
                not   : true,
                values: ['true'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(orgProduct1.id);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].visible).toBe(false);
    });

    it(`it searches with filter not in an attribute field - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                attributes: {
                    flavor: ['CHOCOLATE'],
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                attributes: {
                    flavor: ['VANILLA'],
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'FLOWER',
                uom: 'g',
                visible: true,
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'attribute.flavor',
                fuzzy : false,
                not   : true,
                values: ['CHOCOLATE'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});


        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(orgProduct1.id);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].attributes.flavor[0]).toBe('VANILLA');
    });

    it(`it searches with filter not in a detail field - Catalog Products`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENTLESS',
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENT',
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'FLOWER',
                uom: 'g',
            }),
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'details.extractionMethod',
                fuzzy : false,
                not   : true,
                values: ['SOLVENTLESS'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(orgProduct1.id);
        expect(response.body.data[0].id).toBe(orgProduct2.id);
        expect(response.body.data[0].details.extractionMethod).toBe('SOLVENT');
    });

    it(`it searches with filter not in ID - Brand Products`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        const searchObject = {
            filters: [{
                field : 'id',
                fuzzy : false,
                not   : true,
                values: [brandProduct1.id],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(brandProduct1.id);
        expect(response.body.data[0].id).toBe(brandProduct2.id);
    });

    it(`it searches with filter not in type - Brand Products`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'FLOWER',
                uom: 'g',
            }),
        ]);

        const searchObject = {
            filters: [{
                field : 'type',
                fuzzy : false,
                not   : true,
                values: ['CARTRIDGE'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(brandProduct1.id);
        expect(response.body.data[0].id).toBe(brandProduct2.id);
        expect(response.body.data[0].type).toBe('FLOWER');
    });

    it(`it searches with filter not in visible - Brand Products`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                amount: "1.000000",
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: false,
            }),
        ]);

        const searchObject = {
            filters: [{
                field : 'visible',
                fuzzy : false,
                not   : true,
                values: ['true'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(brandProduct1.id);
        expect(response.body.data[0].id).toBe(brandProduct2.id);
        expect(response.body.data[0].visible).toBe(false);
    });

    it(`it searches with filter not in an attribute field - Brand Products`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                amount: "1.000000",
                attributes: {
                    flavor: ['CHOCOLATE'],
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                attributes: {
                    flavor: ['VANILLA'],
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        const searchObject = {
            filters: [{
                field : 'attribute.flavor',
                fuzzy : false,
                not   : true,
                values: ['CHOCOLATE'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(brandProduct1.id);
        expect(response.body.data[0].id).toBe(brandProduct2.id);
        expect(response.body.data[0].attributes.flavor[0]).toBe('VANILLA');
    });

    it(`it searches with filter not in a detail field`, async () => {
        const [
            brandProduct1,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENTLESS',
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENT',
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.BRAND,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
            }),
        ]);

        const searchObject = {
            filters: [{
                field : 'details.extractionMethod',
                fuzzy : false,
                not   : true,
                values: ['SOLVENTLESS'],
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).not.toBe(brandProduct1.id);
        expect(response.body.data[0].id).toBe(brandProduct2.id);
        expect(response.body.data[0].details.extractionMethod).toBe('SOLVENT');
    });

    it(`it searches with 'priceTierId' filter - excludeLinkedProduct == true`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENTLESS',
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENT',
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: false,
            }),
        ]);

        const priceTier = await PriceTier.create({
            label: "RipOff Tier",
            ownerId: 91,
            ownerType: OwnerType.ORG,
            isActive: true,
            rangeMode: RangeMode.FIXED_PRICE,
            thresholdType: PriceTierThresholdType.FLAT,
            method: PriceTierMethod.WEIGHT,
            thresholds: [
                {
                    value: 4,
                    start: 1,
                    end: 3.5
                },
                {
                    value: 3,
                    start: 3.5,
                    end: 5
                },
                {
                    value: 1.5,
                    start: 5,
                    end: null
                }
            ]
        })

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
            priceTierId: priceTier.id
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'priceTierId',
                fuzzy : false,
                values: [priceTier.id],
                excludeLinkedProduct: true,
            }]
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct1.id);
        expect(response.body.data[0].id).not.toBe(orgProduct2.id);
        expect(response.body.data[0].priceTierId).toBe(priceTier.id);
    });

    it(`it searches with 'priceTierId' filter - excludeLinkedProduct == false`, async () => {
        const [
            catalog,
            orgProduct1,
            orgProduct2,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENTLESS',
                },
                brandName: 'A',
                eCommerceName: 'A',
                name: 'Alice Apples',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: true,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENT',
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: 'CARTRIDGE',
                uom: 'g',
                visible: false,
            }),
        ]);

        const priceTier = await PriceTier.create({
            label: "RipOff Tier",
            ownerId: 91,
            ownerType: OwnerType.ORG,
            isActive: true,
            rangeMode: RangeMode.FIXED_PRICE,
            thresholdType: PriceTierThresholdType.FLAT,
            method: PriceTierMethod.WEIGHT,
            thresholds: [
                {
                    value: 4,
                    start: 1,
                    end: 3.5
                },
                {
                    value: 3,
                    start: 3.5,
                    end: 5
                },
                {
                    value: 1.5,
                    start: 5,
                    end: null
                }
            ]
        })

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
            priceTierId: priceTier.id
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const searchObject = {
            catalogId: catalog.id,
            filters: [{
                field : 'priceTierId',
                fuzzy : false,
                values: [priceTier.id],
            }],
            excludeLinkedProduct: false,
        }

        const response = await supertest(app)
            .get(`/products`).query({query: JSON.stringify(searchObject)});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].id).toBe(orgProduct1.id);
        expect(response.body.data[0].id).not.toBe(orgProduct2.id);
        expect(response.body.data[0].priceTierId).toBe(priceTier.id);
    });

    it('Get merged product details: productId, mergedTo and externalId', async () => {
        const product1 = await Product.create({
            name: 'Happy Pill',
            type: 'PILL',
            ownerId: 1,
            ownerType: OwnerType.BRAND,
            uom: 'g',
        });

        const product2 = await Product.create({
            name: 'Wonderful Cookie',
            type: 'EDIBLE',
            ownerId: 1,
            ownerType: OwnerType.BRAND,
            uom: 'g',
        });

        const product3 = await Product.create({
            name: 'Tasty Cookie',
            type: 'EDIBLE',
            ownerId: 1,
            ownerType: OwnerType.BRAND,
            uom: 'g',
        });

        const [
            reference1,
            reference2,
            reference3,
        ] = await Promise.all([
            ExternalReference.create({
                type: ExternalReferenceType.SELL_TREEZ_ID,
                externalId: "61c4424a-5ccc-11eb-ae93-0242ac130002",
                productId: product1.id
            }),
            ExternalReference.create({
                type: ExternalReferenceType.SELL_TREEZ_ID,
                externalId: "61c44470-5ccc-11eb-ae93-0242ac130002",
                productId: product2.id
            }),
            ExternalReference.create({
                type: ExternalReferenceType.SELL_TREEZ_ID,
                externalId: "61c44560-5ccc-11eb-ae93-0242ac130002",
                productId: product3.id
            }),
            product1.update({
                mergedTo: product3.id
            }),
            product2.update({
                mergedTo: product3.id
            }),
        ])

        const response = await supertest(app)
            .get('/products/mergedProductIds')
            .send({
                "productIds": [product3.id],
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toHaveLength(3);

        const sortedProducts = _.sortBy(response.body, 'id');
        expect(sortedProducts).toMatchObject([
            {
                id: product1.id,
                mergedTo: product3.id,
                externalId: reference1.externalId
            },
            {
                id: product2.id,
                mergedTo: product3.id,
                externalId: reference2.externalId
            },
            {
                id: product3.id,
                mergedTo: null,
                externalId: reference3.externalId
            },
        ]);
    });

    it('decorates products with capabilities when include=capabilities flag is included', async () => {
        const [
            product1,
            product2,
            product3,
            product4,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               null,
                subtype:            "SubType1",
                type:               "Type1",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                displayName:        "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "SubType1",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               null,
                subtype:            "SubType2",
                type:               "Type1",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                displayName:        "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "SubType2",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product3.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product4.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        const response = await supertest(app)
            .get('/products')
            .send({
                "searchString": "",
                "catalogId": catalog.id,
                "status": [
                    "ACTIVE",
                ],
                "filters": [
                    {
                        "field": "subtype",
                        "values": ["SubType1", "SubType2"],
                        "fuzzy": false
                    }
                ],
                "offset": 0,
                "page": 1,
                "pageSize": 10,
                "paginated": false,
                "orderByRank": false,
                "include": ["capabilities"]
            });

        const products = response.body.data;
        for(let product of products) {
            expect(product._internal).not.toBeUndefined();
            expect(product._internal.capabilities).not.toBeUndefined();
        }
    });

    it('decorates products with the right capabilities', async () => {
        const [
            product1,
            product2,
            product3,
            product4,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               null,
                subtype:            "SubType3",
                type:               "Type1",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                displayName:        "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "SubType4",
                type:               "Type1",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               null,
                subtype:            "SubType3",
                type:               "Type2",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                displayName:        "Ignacio's Edible Snacks",
                name:               "Ignacio's Munchies",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                subtype:            "SubType4",
                type:               "Type2",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            })
        ]);

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product1.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product2.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product3.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product4.id,
            status: CatalogProductStatus.ACTIVE,
            price: 25.000000,
        });

        await Capability.bulkCreate([
            {
                subtype: "SubType3",
                type: "Type1",
                capability: "Capability1",
                rules : {}
            },
            {
                subtype: "SubType3",
                type: "Type2",
                capability: "Capability2",
                rules : {}
            },
            {
                subtype: "SubType4",
                type: "Type1",
                capability: "Capability1",
                rules : {}
            },
            {
                subtype: "SubType4",
                type: "Type2",
                capability: "Capability2",
                rules : {}
            },
        ]);

        const response = await supertest(app)
            .get('/products')
            .send({
                "searchString": "",
                "catalogId": catalog.id,
                "status": [
                    "ACTIVE",
                ],
                "filters": [
                    {
                        "field": "subtype",
                        "values": ["SubType1", "SubType2"],
                        "fuzzy": false
                    }
                ],
                "offset": 0,
                "page": 1,
                "pageSize": 10,
                "paginated": false,
                "orderByRank": false,
                "include": ["capabilities"]
            });

            const products = response.body.data;
            for(let product of products) {
                expect(product._internal).not.toBeUndefined();
                expect(product._internal.capabilities).not.toBeNull();

                expect(product._internal.capabilities['Capability1']).not.toBeUndefined();
                expect(product._internal.capabilities['Capability2']).not.toBeUndefined();
            }
    });

    it('returns the base fields for both unlinked and linked search types', async () => {
        const baseFields = [
            ...Product.linkableBrandFields,
            ...Product.unlinkableFields,
            "catalogProductId",
            "catalogOverrides",
            "priceTierId",
            "uuid",
            "weedMapsProductVariantId",
        ];

        const [
            catalog,
            orgProduct1,
        ] = await Promise.all([
            Catalog.create({
                name: 'Test Org Catalog',
                ownerId: 377,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                amount: "1.000000",
                details: {
                    extractionMethod: 'SOLVENT',
                },
                brandName: 'B',
                eCommerceName: 'B',
                name: 'Bob Burger',
                ownerId: 1,
                ownerType: OwnerType.ORG,
                size: '1g',
                type: ProductType.CARTRIDGE,
                uom: UOM.G,
                visible: false,
            }),
        ]);

        await addProductToCatalog(catalog, orgProduct1.id, testUserInfo);

        const [
            linkedResponse,
            unlinkedResponse,
        ] = await Promise.all([
            supertest(app)
                .get('/products')
                .send({
                    catalogId: catalog.id,
                    searchType: SearchType.Retail,
                }),
            supertest(app)
                .get('/products')
                .send({
                    catalogId: catalog.id,
                    searchType: SearchType.UnlinkedRetail,
                }),
        ]);

        const linkedProducts = linkedResponse.body.data;
        const unlinkedProducts = unlinkedResponse.body.data;

        expect(linkedProducts).toHaveLength(1);
        expect(unlinkedProducts).toHaveLength(1);

        const linkedProduct = linkedProducts[0];
        const unlinkedProduct = unlinkedProducts[0];

        for(let i = 0; i < baseFields.length; i++) {
            const field = baseFields[i];

            expect(linkedProduct).toHaveProperty(field);
            expect(unlinkedProduct).toHaveProperty(field);
        }
    });


    it(`Returns only one product when it searches without a catalog id`, async () => {
        const orgCatalog = await Catalog.create({
            ownerId: 1,
            ownerType: OwnerType.ORG,
            name: 'Brand Org'
        });

        const [
            brandCatalog,
            retailCatalog,
            brandProduct1,
            brandProduct2,
            retailProduct,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                name: 'brand catalog',
                parentCatalogId: orgCatalog.id,
            }),
            Catalog.create({
                ownerId: 100,
                ownerType: OwnerType.STORE,
                name: 'store catalog',
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            "SubType1",
                type:               ProductType.EDIBLE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's PreRolls",
                name:               "Ignacio's Splifs",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            "SubType1",
                type:               ProductType.PREROLL,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Stella's Tincture",
                name:                "Stella's Tincture",
                ownerId:            100,
                ownerType:          OwnerType.STORE,
                size:               null,
                subtype:            "SubType1",
                type:               ProductType.TINCTURE,
                uom:                UOM.EACH,
            }),
        ]);

        await Promise.all([
            //The first two add to both the brand and the brand's org catalog (2 catalog products created for both products)
            addProductToCatalog(brandCatalog, brandProduct1.id, testUserInfo),
            addProductToCatalog(brandCatalog, brandProduct2.id, testUserInfo),
            addProductToCatalog(retailCatalog, retailProduct.id, testUserInfo),
        ]);

        const response = await supertest(app)
            .get('/products')
            .query({query: JSON.stringify({
                filters: [
                    {
                        field: "ownerType",
                        values: [OwnerType.BRAND],
                        fuzzy: false,
                    },
                    {
                        field: "ownerId",
                        values: ["2"],
                        fuzzy: false,
                    }
                ],
            })});

        expect(response.body.total).toBe(2);
        expect(response.body.data).toHaveLength(2);

        const sortedProducts = _.sortBy(response.body.data, 'name');

        expect(sortedProducts).toMatchObject([
            {
                name: brandProduct1.name,
                ownerType: OwnerType.BRAND,
            },
            {
                name: brandProduct2.name,
                ownerType: OwnerType.BRAND,
            }
        ]);

        expect(response.body.data[1].ownerType).toBe(OwnerType.BRAND);
    });

    it(`it filters a brand trees search by catalog id`, async () => {
        const [
            brand1Catalog,
            brand2Catalog,
            brand1Product,
            brand2Product,
        ] = await Promise.all([
            Catalog.create({
                ownerType: OwnerType.BRAND,
                ownerId: 1,
                name: 'Brand 1',
            }),
            Catalog.create({
                ownerType: OwnerType.BRAND,
                ownerId: 2,
                name: 'Brand 2',
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            1,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            "SubType1",
                type:               ProductType.EDIBLE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's PreRolls",
                name:               "Ignacio's Splifs",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            "SubType1",
                type:               ProductType.PREROLL,
                uom:                UOM.EACH,
            }),
        ]);

        await Promise.all([
            addProductToCatalog(brand1Catalog, brand1Product.id, testUserInfo),
            addProductToCatalog(brand2Catalog, brand2Product.id, testUserInfo),
        ]);

        const response = await supertest(app)
            .get('/products')
            .query({query: JSON.stringify({
                catalogId: brand1Catalog.id,
                searchType: SearchType.BrandTreez
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0]).toMatchObject({
            amount:             null,
            brandName:          "Real BrandName",
            displayName:        "Nacho's Munchies",
            name:               "Ignacio's Edible Snacks",
            ownerId:            1,
            ownerType:          OwnerType.BRAND,
            size:               null,
            subtype:            "SubType1",
            type:               ProductType.EDIBLE,
            uom:                UOM.EACH,
        });
    });

    it(`does not return brand products in search if they are marked visible false`, async () => {
        const [
            visibleProduct,
        ] = await Promise.all([
            Product.create({
                amount     : null,
                brandName  : "Real BrandName",
                name       : "Visible Product",
                ownerId    : 2,
                ownerType  : OwnerType.BRAND,
                subtype    : "SubType1",
                type       : ProductType.PREROLL,
                uom        : UOM.EACH,
                visible    : true,
            }),
            Product.create({
                amount     : null,
                brandName  : "Real BrandName",
                name       : "Invisible Product",
                ownerId    : 2,
                ownerType  : OwnerType.BRAND,
                subtype    : "SubType1",
                type       : ProductType.PREROLL,
                uom        : UOM.EACH,
                visible    : false,
            }),
        ]);

        const response = await supertest(app)
            .get('/products')
            .query({query: JSON.stringify({
                searchType: SearchType.BrandTreez,
            })});

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0]).toMatchObject({
            id: visibleProduct.id,
            name: visibleProduct.name,
        });
    });

    it(`does not apply the visible restriction on searching BrandTreez if the visible field is explicitely filtered on`, async () => {
        const [
            invisibleProduct,
        ] = await Promise.all([
            Product.create({
                amount     : null,
                brandName  : "Real BrandName",
                name       : "Invisible Product",
                ownerId    : 2,
                ownerType  : OwnerType.BRAND,
                subtype    : "SubType1",
                type       : ProductType.PREROLL,
                uom        : UOM.EACH,
                visible    : false,
            }),
            Product.create({
                amount     : null,
                brandName  : "Real BrandName",
                name       : "Visible Product",
                ownerId    : 2,
                ownerType  : OwnerType.BRAND,
                subtype    : "SubType1",
                type       : ProductType.PREROLL,
                uom        : UOM.EACH,
                visible    : true,
            })
        ]);

        const response = await supertest(app)
            .get('/products')
            .query({query: JSON.stringify({
                searchType: SearchType.BrandTreez,
                filters: [{
                    field: 'visible',
                    fuzzy: false,
                    values: [false]
                }]
            })});

        expect(response.status).toBe(StatusCodes.OK)
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0]).toMatchObject({
            id: invisibleProduct.id,
        })
    });

    it('discontinues a brand product when sending a patch request to /products/:id/discontinue without a body, defaulting the param of "isDiscontinued" to true', async () => {
        const brandProduct = await Product.create({
            amount     : null,
            brandName  : "Real BrandName",
            name       : "Visible Product",
            ownerId    : 2,
            ownerType  : OwnerType.BRAND,
            subtype    : "SubType1",
            type       : ProductType.PREROLL,
            uom        : UOM.EACH,
            visible    : true,
        });

        const response = await supertest(app)
            .patch(`/products/${brandProduct.id}/discontinue`);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            id: brandProduct.id,
            visible: false,
        });
    });

    it('it updates the products visible field to false when explicitly using true as the value of the "isDiscontinued" in the body of a patch to /products/:id/discontinue', async () => {
        const brandProduct = await Product.create({
            amount     : null,
            brandName  : "Real BrandName",
            name       : "Visible Product",
            ownerId    : 2,
            ownerType  : OwnerType.BRAND,
            subtype    : "SubType1",
            type       : ProductType.PREROLL,
            uom        : UOM.EACH,
            visible    : true,
        });

        const response = await supertest(app)
            .patch(`/products/${brandProduct.id}/discontinue`)
            .send({isDiscontinued: true});

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            id: brandProduct.id,
            visible: false,
        });
    });

    it('it updates the products visible field to true when using false as the value of the "isDiscontinued" in the body of a patch to /products/:id/discontinue', async () => {
        const brandProduct = await Product.create({
            amount     : null,
            brandName  : "Real BrandName",
            name       : "Visible Product",
            ownerId    : 2,
            ownerType  : OwnerType.BRAND,
            subtype    : "SubType1",
            type       : ProductType.PREROLL,
            uom        : UOM.EACH,
            visible    : false,
        });

        const response = await supertest(app)
            .patch(`/products/${brandProduct.id}/discontinue`)
            .send({isDiscontinued: false});

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            id: brandProduct.id,
            visible: true,
        });
    });

    it('it rejects updating a product that is not a brand product', async () => {
        const retailProduct = await Product.create({
            amount     : null,
            brandName  : "Real BrandName",
            name       : "Visible Product",
            ownerId    : 2,
            ownerType  : OwnerType.STORE,
            subtype    : "SubType1",
            type       : ProductType.PREROLL,
            uom        : UOM.EACH,
            visible    : true,
        });

        const response = await supertest(app)
        .patch(`/products/${retailProduct.id}/discontinue`);

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`Checks to see if last update date is the latest`, async () => {
        const {
            ORG,
        } = OwnerType;
        
        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          ORG,
                parentCatalogId:    null,
            }),
        ]);

        //add a new product
       const catalogProduct = await addProductToCatalog(orgCatalog, product.id, { userAuthId: '<EMAIL>' });

        //get the old update value
        const response = await supertest(app)
            .get(`/products`)
            .send({
                "filters": [{
                    "field": "id",
                    "fuzzy": false,
                    "not": false,
                    "values": [catalogProduct.productId]
                }],
                "include": ["assortments"],
                "status": ["ACTIVE", "DEACTIVATED", "DELETED"],
                "catalogId": catalogProduct.catalogId,
                "excludeLinkedProduct": true
            });

        expect(response.status).toBe(StatusCodes.OK);
        let oldUpdatedAt = response.body.data[0].updatedAt;

        //find same product again
        const productFound = await CatalogProduct.findOne({
            where: {
                productId: catalogProduct.productId,
                catalogId: catalogProduct.catalogId
            }
        });

        if (productFound !== null) {

            //once found update the price
            await productFound.update({
                price: 54.0000
            });

            //get the update price again
            const responseUpdated = await supertest(app)
                .get(`/products`)
                .send({
                    "filters": [{
                        "field": "id",
                        "fuzzy": false,
                        "not": false,
                        "values": [catalogProduct.productId]
                    }],
                    "include": ["assortments"],
                    "status": ["ACTIVE", "DEACTIVATED", "DELETED"],
                    "catalogId": catalogProduct.catalogId,
                    "excludeLinkedProduct": true
                });

            expect(responseUpdated.status).toBe(StatusCodes.OK);
            let newUpdatedAt = responseUpdated.body.data[0].updatedAt;

            expect(new Date(newUpdatedAt).getTime()).not.toEqual(new Date(oldUpdatedAt).getTime());
            expect(new Date(newUpdatedAt).getTime()).toBeGreaterThan(new Date(oldUpdatedAt).getTime());
        }
     })
});

describe(`suggested linking behavior and endpoints`, () => {
    it(`allows a brand to suggest a linked product`, async () => {
        const [
            brandProduct,
            retailProduct,
        ] = await Promise.all([
            Product.create({
                brandName: 'Bhang',
                ownerId  : 777,
                ownerType: OwnerType.BRAND,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High 311',
            }),
            Product.create({
                brandName: 'bang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High Three 11',
            })
        ]);

        const response = await supertest(app)
            .post(`/products/${brandProduct.id}/suggestedLink/${retailProduct.id}`);

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            brandProductId: brandProduct.id,
            productToLinkId: retailProduct.id,
            type: MatchType.BrandSuggested,
        });
    });

    it(`it throws a ${StatusCodes.BAD_REQUEST} for suggesting a link to a product that has already been suggested`, async () => {
        const [
            brandProduct1,
            retailProduct,
            brandProduct2,
        ] = await Promise.all([
            Product.create({
                brandName: 'Bhang',
                ownerId  : 777,
                ownerType: OwnerType.BRAND,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High 311',
            }),
            Product.create({
                brandName: 'bang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High Three 11',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 777,
                ownerType: OwnerType.BRAND,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Maxi Tears',
            })
        ]);

        await SuggestedLink.create({
            productToLinkId: retailProduct.id,
            brandProductId: brandProduct1.id,
            type: MatchType.BrandSuggested,
        });

        const response = await supertest(app)
            .post(`/products/${brandProduct2.id}/suggestedLink/${retailProduct.id}`);

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it(`gets products adoption metrics`, async () => {
        const brandProduct1 = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const brandProduct2 = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Not So High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct1,
            orgProduct1,
            orgProduct2,
            storeProduct2,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Product to Weight',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Banana Dutch Sauce',
            }),
        ]);

        const [
            linkedOrgProduct,
            linkedStoreProduct,
        ] = await Promise.all([
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Linked Tasty Cartridge',
                linkedTo : brandProduct1.id,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Linked Stupid Cartridge',
                linkedTo : brandProduct2.id,
            }),
            await storeCatalog.update({
                parentCatalogId: orgCatalog.id,
            }),
        ]);

        const userInfo = {userAuthId: 'bananas', sellTreezUserId: 'apples'};

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct1.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct1.id, userInfo),
            addProductToCatalog(storeCatalog, storeProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, linkedOrgProduct.id, userInfo),
            addProductToCatalog(storeCatalog, linkedStoreProduct.id, userInfo),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: orgProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct2.id,
                productToLinkId: storeProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct2.id,
                productToLinkId: orgProduct2.id,
                type: MatchType.Weighted,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: storeProduct2.id,
                type: MatchType.Weighted,
            }),
        ]);

        const response = await supertest(app)
            .get(`/products/adoption?productIds=${brandProduct1.id},${brandProduct2.id}`);

        expect(response.body).toMatchObject({
            [brandProduct1.id]: {
                productId: 1,
                linkedRetailCount: 1,
                brandSuggestedLinksCount: 1,
                totalCount: 4,
                weightedMatches: 2,
                linkAdoption: 0.25,
                brandSuggestedAdoption: 0.25
            },
            [brandProduct2.id]: {
                productId: 2,
                linkedRetailCount: 2,
                brandSuggestedLinksCount: 2,
                totalCount: 5,
                weightedMatches: 1,
                linkAdoption: 0.4,
                brandSuggestedAdoption: 0.4
            }
        });
    });

    it(`correctly returns a brand product that has not gotten any adoption`, async () => {
        const [
            brandProduct1,
            brandProduct2,
         ] = await Promise.all([
             Product.create({
                brandName: 'Bhang',
                ownerId  : 777,
                ownerType: OwnerType.BRAND,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High 311',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 777,
                ownerType: OwnerType.BRAND,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Not So High 311',
            }),
        ]);

        const [
            orgCatalog,
            storeCatalog,
            storeProduct1,
            orgProduct1,
            orgProduct2,
            storeProduct2,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Product to Weight',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Banana Dutch Sauce',
            }),
        ]);

        const [
            linkedOrgProduct,
            linkedStoreProduct,
        ] = await Promise.all([
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Linked Tasty Cartridge',
                linkedTo : brandProduct1.id,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Linked Stupid Cartridge',
                linkedTo : brandProduct1.id,
            }),
            await storeCatalog.update({
                parentCatalogId: orgCatalog.id,
            }),
        ]);

        const userInfo = {userAuthId: 'bananas', sellTreezUserId: 'apples'};

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct1.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct1.id, userInfo),
            addProductToCatalog(storeCatalog, storeProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, linkedOrgProduct.id, userInfo),
            addProductToCatalog(storeCatalog, linkedStoreProduct.id, userInfo),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: orgProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: storeProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: orgProduct2.id,
                type: MatchType.Weighted,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct1.id,
                productToLinkId: storeProduct2.id,
                type: MatchType.Weighted,
            }),
        ]);

        const response = await supertest(app)
            .get(`/products/adoption?productIds=${brandProduct1.id},${brandProduct2.id}`);

        expect(response.body).toMatchObject({
            [brandProduct1.id]: {
                productId: brandProduct1.id,
                linkedRetailCount: 3,
                brandSuggestedLinksCount: 3,
                totalCount: 9,
                weightedMatches: 3,
                linkAdoption: 0.33,
                brandSuggestedAdoption: 0.33
            },
            [brandProduct2.id]: {
                productId: brandProduct2.id,
                linkedRetailCount: 0,
                brandSuggestedLinksCount: 0,
                totalCount: 0,
                weightedMatches: 0,
                linkAdoption: 0,
                brandSuggestedAdoption: 0,
            }
        });
    });

    it(`searches products in catalogs by brandName`, async () => {
        const [
            orgCatalog,
            storeCatalog,
            storeProduct1,
            orgProduct1,
            orgProduct2,
            storeProduct2,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Ndombele',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'TipTop',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Product to Weight',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Banana Dutch Sauce',
            }),
        ]);

        const userInfo = {userAuthId: 'bananas', sellTreezUserId: 'apples'};

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct1.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct1.id, userInfo),
            addProductToCatalog(storeCatalog, storeProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct2.id, userInfo),
        ]);

        const response = await supertest(app)
            .get(`/products`).query({
                query: JSON.stringify({
                    catalogId: orgCatalog.id,
                    brandMatch: 'Bhang'
                })
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(2)
    });

    it(`Returns all default brand fields when it searches BrandTreez`, async () => {
        const [
            brandCatalog,
            product,
        ] = await Promise.all([
            Catalog.create({
                ownerId: 2,
                ownerType: OwnerType.BRAND,
                name: 'brand catalog',
            }),
            Product.create({
                amount:             null,
                brandName:          "Real BrandName",
                displayName:        "Nacho's Munchies",
                name:               "Ignacio's Edible Snacks",
                ownerId:            2,
                ownerType:          OwnerType.BRAND,
                size:               null,
                subtype:            "SubType1",
                type:               "Type1",
                uom:                UOM.EACH,
            })
        ]);

        await addProductToCatalog(brandCatalog, product.id, testUserInfo);

        const response = await supertest(app)
            .get('/products')
            .query({query: JSON.stringify({
                ownerType: OwnerType.BRAND,
                ownerId: 2,
            })});


        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data).toHaveLength(1);

        const retreivedProduct = response.body.data[0];

        expect(retreivedProduct).toHaveProperty("amount");
        expect(retreivedProduct).toHaveProperty("attributes");
        expect(retreivedProduct).toHaveProperty("barcodes");
        expect(retreivedProduct).toHaveProperty("brandName");
        expect(retreivedProduct).toHaveProperty("cannabis");
        expect(retreivedProduct).toHaveProperty("classification");  expect(retreivedProduct).toHaveProperty("createdAt");
        expect(retreivedProduct).toHaveProperty("descriptions");
        expect(retreivedProduct).toHaveProperty("details");
        expect(retreivedProduct).toHaveProperty("displayName");
        expect(retreivedProduct).toHaveProperty("eCommerceName");
        expect(retreivedProduct).toHaveProperty("externalId");
        expect(retreivedProduct).toHaveProperty("id");
        expect(retreivedProduct).toHaveProperty("images");
        expect(retreivedProduct).toHaveProperty("ownerId");
        expect(retreivedProduct).toHaveProperty("ownerType");
        expect(retreivedProduct).toHaveProperty("packageTracked");
        expect(retreivedProduct).toHaveProperty("size");
        expect(retreivedProduct).toHaveProperty("sku");
        expect(retreivedProduct).toHaveProperty("subtype");
        expect(retreivedProduct).toHaveProperty("tpc");
        expect(retreivedProduct).toHaveProperty("type");
        expect(retreivedProduct).toHaveProperty("uom");
        expect(retreivedProduct).toHaveProperty("upc");
        expect(retreivedProduct).toHaveProperty("updatedAt");
        expect(retreivedProduct).toHaveProperty("visible");
    });
});
