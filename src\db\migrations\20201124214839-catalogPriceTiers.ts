import { QueryInterface }               from 'sequelize';
import {
    autoPKField,
    createUpdateColumns,
    jsonField,
    notNull,
    intField,
    foreignKey,
}                                       from '@treez/dev-pack/db/fieldTypes';

const catalogPriceTiers = 'catalogPriceTiers';

const id          = 'id';
const catalogId   = 'catalogId';
const priceTierId = 'priceTierId';

const catalogIdAndPriceTierIdUniqueConstraintonCatalogPriceTiers = 'catalogIdAndPriceTierIdUniqueConstraintonCatalogPriceTiers';
const catalogIdIndexOnCatalogPriceTiers                          = 'catalogIdIndexOnCatalogPriceTiers';
const priceTierIdIndexOnCatalogPriceTiers                        = 'priceTierIdIndexOnCatalogPriceTiers';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.createTable(catalogPriceTiers, {
            id:                         notNull(autoPKField),
            catalogId:                  notNull(foreignKey(intField, 'catalogs', id)),
            priceTierId:                notNull(foreignKey(intField, 'priceTiers', id)),
            priceTierOverrides:         jsonField,

            ...createUpdateColumns,
        })

        await queryInterface.addConstraint(catalogPriceTiers, {
            type: 'unique',
            fields: [catalogId, priceTierId],
            name: catalogIdAndPriceTierIdUniqueConstraintonCatalogPriceTiers
        })

        await queryInterface.addIndex(catalogPriceTiers, [catalogId], {
            name: catalogIdIndexOnCatalogPriceTiers
        })

        await queryInterface.addIndex(catalogPriceTiers, [priceTierId], {
            name: priceTierIdIndexOnCatalogPriceTiers
        })
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.dropTable(catalogPriceTiers);
    }
}
