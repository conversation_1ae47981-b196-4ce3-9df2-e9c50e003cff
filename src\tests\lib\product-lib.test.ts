import {
    CatalogProductStatus,
    OwnerType,
    UOM,
}                               from "@treez/commons/sharedTypings/product";
import { parseNumericFields }   from "../../lib/product";
import CatalogProduct           from "../../models/CatalogProduct";
import Product                  from "../../models/Product";

describe(`parse numeric fields`, () => {
    it('returns the numeric fields of a product or catalog Product', () => {
        const catalogProduct = new CatalogProduct({
            productId: 1,
            catalogId: 2,
            price: '.99',
            status: CatalogProductStatus.ACTIVE
        });

        const result = parseNumericFields(catalogProduct);

        expect(result).toMatchObject({
            productId: 1,
            catalogId: 2,
            price: .99,
            status: CatalogProductStatus.ACTIVE
        })
    });

    it(`it handles nested values`, () => {
        const product = new Product({
            amount:             "1.000000",
            brandName:          "Treez Tester",
            eCommerceName:      "Blue Gummies",
            name:               "<PERSON> Gummies",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS3",
            subtype:            "Modest Nonsense",
            type:               "Nonsense",
            tpc:                "YeahOkSure3",
            uom:                UOM.EACH,
            productOverrides:   {
                amount: "2.000"
            }            
        });

        const result = parseNumericFields(product);

        expect(result).toMatchObject({
            amount:             1,
            brandName:          "Treez Tester",
            eCommerceName:      "Blue Gummies",
            name:               "Blue Gummies",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
            size:               "50 piece bucket",
            sku:                "SKUXOZEHXKS3",
            subtype:            "Modest Nonsense",
            type:               "Nonsense",
            tpc:                "YeahOkSure3",
            uom:                UOM.EACH,
            productOverrides: {amount: 2.00}
        })
    })
})
