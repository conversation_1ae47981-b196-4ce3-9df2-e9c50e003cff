import { QueryInterface }               from 'sequelize';
import { OwnerType }                    from '@treez/commons/sharedTypings/product';
import {
    stringField,
    enumField,
    notNull,
}                                       from '@treez/dev-pack/db/fieldTypes';

const externalReferences = 'externalReferences';
const ownerId            = 'ownerId';
const ownerType          = 'ownerType';

export = {
    up: async function( queryInterface: QueryInterface ) {
        await queryInterface.removeColumn(externalReferences, ownerId);
        await queryInterface.removeColumn(externalReferences, ownerType);
    },
    down: async function( queryInterface: QueryInterface ) {
        await queryInterface.addColumn(externalReferences, ownerId, notNull(stringField));
        await queryInterface.addColumn(externalReferences, ownerType, notNull(enumField(OwnerType)));

        await queryInterface.sequelize.query(`
            UPDATE "externalReferences"
            JOIN "products" on "externalReferences"."productId" = "products"."id"
            SET
                "externalReferences"."ownerId"   = products."ownerId",
                "externalReferences"."ownerType" = products."ownerType"
            WHERE "externalReferences"."ownerId" IS NULL;
        `); 
    }
};
