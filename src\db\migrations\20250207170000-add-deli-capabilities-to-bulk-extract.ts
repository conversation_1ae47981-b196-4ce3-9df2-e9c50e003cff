import { QueryInterface } from 'sequelize';

export = {
    up: async function (queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            INSERT INTO "capabilities" (
                "type",
                "subtype",
                "capability",
                "rules",
                "createdAt",
                "updatedAt"
            )
            VALUES ('EXTRACT', 'BULK EXTRACT', 'deli', null, NOW(), NOW());
        `);
    },

    down: async function (queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            DELETE FROM "capabilities"
            WHERE "type" = 'EXTRACT'
              AND "subtype" = 'BULK EXTRACT'
              AND "capability" = 'deli';
        `);
    },
};