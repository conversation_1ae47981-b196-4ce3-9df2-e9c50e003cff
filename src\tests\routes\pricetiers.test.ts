import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode,
}                                       from '@treez/commons/sharedTypings/priceTier';

import {
    OwnerType,
    UOM,
}                                       from '@treez/commons/sharedTypings/product';
import { StatusCodes }                  from 'http-status-codes';
import _                                from 'lodash';
import supertest                        from 'supertest';
import Catalog                          from '../../models/Catalog';
import CatalogPriceTier                 from '../../models/CatalogPriceTier';
import { addProductToCatalog }          from '../../models/CatalogProduct';
import PriceTier                        from '../../models/PriceTier';
import Product                          from '../../models/Product';
import CatalogProduct                   from '../../models/CatalogProduct';
import app                              from './testApp';

const thresholdsTemplate = [
    {
        value: 4,
        start: 1,
        end: 3.5
    },
    {
        value: 3,
        start: 3.5,
        end: 5
    },
    {
        value: 1.5,
        start: 5,
        end: null
    }
];

const PriceTierTemplate = {
    label: "RipOff Tier",
    ownerId: 91,
    ownerType: OwnerType.ORG,
    isActive: true,
    rangeMode: RangeMode.FIXED_PRICE,
    thresholdType: PriceTierThresholdType.FLAT,
    method: PriceTierMethod.WEIGHT,
    thresholds: thresholdsTemplate
}

describe("/priceTier endpoints", () => {
    it('Returns a PriceTier with a specific ID in catalog---> GET request to /:id/catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const priceTier = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier.id,
        });

        const response = await supertest(app)
            .get(`/pricetier/${priceTier.id}/catalog/${catalog.id}`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            data: {
                ...PriceTierTemplate,
                hasProducts: false,
            }
        });
    });

    it('Returns a 404 error for a priceTier that doesnt exist in catalog ---> GET request to /:id/catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const priceTierId = 20000;
        const response = await supertest(app)
            .get(`/pricetier/${priceTierId}/catalog/${catalog.id}`);

        expect(response.status).toBe(StatusCodes.NOT_FOUND);
        expect(response.body).toMatchObject({
            code:    StatusCodes.NOT_FOUND,
            message: `Could not find PriceTier in catalog for id: ${priceTierId}`,
        });
    });

    it('Creates a new priceTier for a catalog ---> POST request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const createResponse = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate,
                hasProducts: false,
            });

        expect(createResponse.status).toBe(StatusCodes.CREATED);
    });

    it('Creates a new CatalogPriceTier for a catalog ---> POST request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const createResponse = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate
            });

        const catalogPriceTiers : CatalogPriceTier[] = await CatalogPriceTier.findAll({
            where: {
                catalogId: catalog.id,
            }
        })

        expect(createResponse.status).toBe(StatusCodes.CREATED);
        expect(catalogPriceTiers.length).toBe(1);
    });

    it('Creates a new CatalogPriceTier for children catalog ---> POST request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const childCatalog = await Catalog.create({
            name           : "Test Org Catalog",
            ownerId        : 55,
            ownerType      : OwnerType.STORE,
            parentCatalogId: catalog.id,
        });

        const createResponse = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate
            });

        const catalogPriceTiers : CatalogPriceTier[] = await CatalogPriceTier.findAll({
            where: {
                catalogId: childCatalog.id,
            }
        })

        expect(createResponse.status).toBe(StatusCodes.CREATED);
        expect(catalogPriceTiers.length).toBe(1);
    });

    it('Doesnt create a new Price Tier if there is already an existing one with the same label ---> POST request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const createResponse1 = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate
            });

        const createResponse2 = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate
            });

        expect(createResponse1.status).toBe(StatusCodes.CREATED);
        expect(createResponse2.status).toBe(StatusCodes.BAD_REQUEST);
        expect(createResponse2.body).toMatchObject({
            code:    StatusCodes.BAD_REQUEST,
            message: `Duplicate Price Tier Label`,
        });
    });

    it('Doesnt update an existing price tier to a new label if a duplicate label for CatalogPrice exists ---> PUT request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const priceTier = await PriceTier.create(PriceTierTemplate);
        const priceTier2 = await PriceTier.create({
            ...PriceTierTemplate,
            label: "RipOff Tier 2",
        });

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier.id,
        });

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier2.id,
        });

        const response = await supertest(app)
            .put(`/pricetier/${priceTier.id}/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate,
                label: "RipOff Tier 2",
            });

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
        expect(response.body).toMatchObject({
            code:    StatusCodes.BAD_REQUEST,
            message: `Duplicate Price Tier Label`,
        });
    });

    it('Allow same price tier to update to a same label - bypass duplicate check ---> PUT request to /catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const priceTier = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier.id,
        });

        const response = await supertest(app)
            .put(`/pricetier/${priceTier.id}/catalog/${catalog.id}`)
            .send({
                ...PriceTierTemplate
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED);
    });

    it('Doesnt create a new price tier with invalid thresholds ---> POST request to route /catalog/:catalogId', async () => {
        const thresholds = [
            { end: 4},
            { end: 4},
            { end: 4},
        ];
        const priceTier = {...PriceTierTemplate, thresholds};

        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const response = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send(priceTier);

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Doesnt create a new price tier if label is null---> POST request to route /catalog/:catalogId', async () => {
        const priceTier = {
            ...PriceTierTemplate,
            label: null
        };

        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const response = await supertest(app)
            .post(`/pricetier/catalog/${catalog.id}`)
            .send(priceTier);

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Doesnt update a price tier with an empty body ---> PUT request to /:id/catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const priceTier = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier.id,
        });

        const response = await supertest(app)
            .put(`/pricetier/${priceTier.id}/catalog/${catalog.id}`)
            .send({});

        expect(response.status).toBe(StatusCodes.BAD_REQUEST);
    });

    it('Updates a price tier when given a valid Price Tier object ---> PUT request to /:id/catalog/:catalogId', async () => {
        const catalog = await Catalog.create({
            name     : "Test Org Catalog",
            ownerId  : 55,
            ownerType: OwnerType.ORG,
        });

        const { id } = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: id,
        });

        const thresholds = [...thresholdsTemplate];
        thresholds[2].value = 1.19;
        const priceTier = {
            ...PriceTierTemplate,
            thresholds,
            id,
            label: "New Tier",
        };

        const response = await supertest(app)
            .put(`/pricetier/${id}/catalog/${catalog.id}`)
            .send(priceTier);

        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            data: {
                ...priceTier,
                hasProducts: false,
            }
        });
    });

    it(`Doesn't update a price tier's owner details when updating a price tier ---> PUT request to /:id/catalog/:catalogId`, async () => {
        const catalog = await Catalog.create({
            name     : "Test Catalog",
            ownerId  : 55,
            ownerType: OwnerType.STORE,
        });
    
        const priceTierBody = {
            label: "RipOff Tier",
            ownerId: 55,
            ownerType: OwnerType.STORE,
            isActive: true,
            rangeMode: RangeMode.FIXED_PRICE,
            thresholdType: PriceTierThresholdType.FLAT,
            method: PriceTierMethod.WEIGHT,
            thresholds: [
                {
                    value: 4,
                    start: 1,
                    end: 3.5
                },
                {
                    value: 3,
                    start: 3.5,
                    end: 5
                },
                {
                    value: 1.5,
                    start: 5,
                    end: null
                }
            ]
        }
    
        const { id } = await PriceTier.create(priceTierBody);
    
        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: id,
        });
    
        const priceTierUpdate = {
            ...priceTierBody,
            label: 'updated label for RIPOFF Tier',
            ownerId: 7,
            ownerType: OwnerType.ORG,
        };
    
        const response = await supertest(app)
            .put(`/pricetier/${id}/catalog/${catalog.id}`)
            .send(priceTierUpdate);
    
        expect(response.status).toBe(StatusCodes.ACCEPTED);
        expect(response.body).toMatchObject({
            data: {
                ownerId: 55,
                ownerType: OwnerType.STORE,
                label: 'updated label for RIPOFF Tier',
            }
        });
    });

    it('Returns priceTiers when searching by ownerId ---> POST request to /search', async () => {
        const expectedEntityCount = 5;
        await PriceTier.bulkCreate(_.fill(Array(expectedEntityCount), PriceTierTemplate));

        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                searchString: "",
                filters: [
                    { key: 'ownerId', values: [PriceTierTemplate.ownerId]}
                ]
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(expectedEntityCount);
    });

    it('Returns priceTiers when searching by catalogId ---> POST request to /search', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });


        const priceTiers = await PriceTier.bulkCreate(_.fill(Array(5), PriceTierTemplate));

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: _.get(priceTiers[0], 'id')
        });


        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                searchString: "",
                catalogId: catalog.id,
                filters: [
                    { key: 'ownerId', values: [PriceTierTemplate.ownerId]}
                ]
            });


        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(1);
    });

    it('Returns priceTiers when searching by catalogId, with searchString ---> POST request to /search', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });


        const priceTiers = await PriceTier.bulkCreate(_.fill(Array(5), PriceTierTemplate));

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: _.get(priceTiers[0], 'id')
        });


        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                searchString: "RipOff",
                catalogId: catalog.id,
                filters: [
                    { key: 'label', values: [PriceTierTemplate.label]}
                ]
            });


        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(1);
    });

    it('Returns priceTiers when searching by catalogId, with searchString - case insensitive ---> POST request to /search', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });


        const priceTiers = await PriceTier.bulkCreate(_.fill(Array(5), PriceTierTemplate));

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: _.get(priceTiers[0], 'id')
        });


        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                searchString: "ripoff",
                catalogId: catalog.id,
                filters: [
                    { key: 'label', values: [PriceTierTemplate.label]}
                ]
            });


        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(1);
    });

    it('Returns an empty list of thresholds when searching by ownerId that has no PriceTiers ---> POST request to /search', async () => {
        await PriceTier.bulkCreate(_.fill(Array(5), PriceTierTemplate));

        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                searchString: "",
                filters: []
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(0);
    });

    it('Returns price tiers with a hasProducts flag when searching ---> POST request to /search', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const product = await Product.create({
            amount:             "1.000000",
            brandName:          "Brand Tester",
            eCommerceName:      "Z0M&WHY?!",
            name:               "ZOMGWHY?!",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            price:              "25.000000",
            size:               "50 piece bucket",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        })

        const priceTier = await PriceTier.create(PriceTierTemplate);

        await CatalogPriceTier.create({
            catalogId: catalog.id,
            priceTierId: priceTier.id
        });

        const catalogProduct = await addProductToCatalog(catalog, product.id, { userAuthId: '<EMAIL>' },);

        await catalogProduct.update({
            priceTierId: priceTier.id
        });

        const response = await supertest(app)
            .post('/pricetier/search')
            .send({
                catalogId   : catalog.id,
                filters     : [],
                searchString: "",
            });

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body.data.length).toBe(1);
        expect(response.body.data[0].hasProducts).toBe(true);
    });

    it('Returns a mapping of priceTierIds to catalogIds when searching ---> GET request to /catalogs', async () => {

        const priceTier = await PriceTier.create(PriceTierTemplate);

        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);


        const catalogProduct = await CatalogProduct.build({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: 24,
            priceTierId: priceTier.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        const response = await supertest(app)
            .get('/pricetier/catalogs')
            .query({
                priceTierIds  : '1,2,3'
            });


        expect(response.body.data).toMatchObject({
            [priceTier.id] : [catalogProduct.catalogId],
            2 : [],
            3 : []
        });
        expect(response.status).toBe(StatusCodes.OK);
    });

    it('Returns a list of products that have been assigned to a price tier ---> GET /:id/products', async () => {
        const priceTier = await PriceTier.create(PriceTierTemplate);

        const [
            product,
            orgCatalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const childCatalog1 = await Catalog.create({
            name:               "Test Child Catalog 1",
            ownerId:            57,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        const childCatalog2 = await Catalog.create({
            name:               "Test Child Catalog 2",
            ownerId:            58,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    orgCatalog.id,
        });

        await CatalogProduct.build({
            catalogId: orgCatalog.id,
            productId: product.id,
            price: 24,
            priceTierId: priceTier.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        await CatalogProduct.build({
            catalogId: childCatalog1.id,
            productId: product.id,
            price: 24,
            priceTierId: priceTier.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        await CatalogProduct.build({
            catalogId: childCatalog2.id,
            productId: product.id,
            price: 24,
            priceTierId: priceTier.id,
            status: 'ACTIVE',
            catalogOverrides: {},
        }).save();

        const postResponse = await supertest(app)
            .get(`/pricetier/${priceTier.id}/products`)
            .query({
                orgCatalogId: orgCatalog.id
            });

        expect(postResponse.status).toBe(StatusCodes.OK);
        expect(postResponse.body.data.length).toBe(1);
        expect(postResponse.body.data[0].id).toBe(product.id);

    });

    it(`can accept a string as an ownerId to allow for sellTreez backwards compatability`, async () => {
        const priceTierToAdd = {
            "label": "50",
            "ownerId": "11",
            "ownerType": "store",
            "isActive": true,
            "method": "WEIGHT",
            "thresholdType": "FLAT",
            "rangeMode": "RANGE",
            "thresholds": [
                {
                    "value": 16.475973,
                    "start": 1.000000,
                    "end": 1.750000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 14.000000,
                    "end": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 3.500000,
                    "end": 7.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 16.475973,
                    "start": 0.000000,
                    "end": 1.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 28.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 7.000000,
                    "end": 14.000000,
                    "tolerance": 0.000000
                },
                {
                    "value": 15.691402,
                    "start": 1.750000,
                    "end": 3.500000,
                    "tolerance": 0.000000
                }
            ]
        };

        const catalog = await Catalog.create({
            name:               "Test Store Catalog",
            ownerId:            11,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    null,
        });

        const createResponse = await supertest(app)
        .post(`/pricetier/catalog/${catalog.id}`)
        .send({
            ...priceTierToAdd,
            hasProducts: false,
        });

        expect(createResponse.status).toBe(StatusCodes.CREATED);
    });
});
