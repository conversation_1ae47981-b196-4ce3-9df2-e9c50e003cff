import { brandNameCorrelationScore }    from "../../constants/products";
import { cleanTextForSQL }              from "../../lib/db";
import Product                          from "../../models/Product";

const generateLinkingCondition = (field: string) => (`
    CASE
        WHEN bt.id IS NULL
            THEN p."${field}"
        ELSE
            bt."${field}"
    END AS "${field}",
`);


const generateRetailSearchQuery = (
    uuidCondition    : string,
    statusFilter     : string,
    brandToFilterBy? : string
) => (`
    SELECT
        p.id AS "id",
        cp.id AS "catalogProductId",
        cp.price,
        cp.status,
        cp."catalogOverrides",
        p.amount,
        ${Product.linkableBrandFields.map(generateLinkingCondition).join(' ')}
        p."createdAt",
        p."externalId",
        p."linkedTo",
        p."ownerId",
        p."ownerType",
        p."productOverrides",
        cp."priceTierId",
        p.uom,
        exwm."externalId" as "weedMapsProductVariantId",
        p.visible,
        extz."externalId" AS uuid,
        CASE p."updatedAt" > cp."updatedAt"
            WHEN true THEN p."updatedAt"
            WHEN false THEN cp."updatedAt"
        END AS "updatedAt",
        p."mergedTo",
        count(*) OVER() AS "totalCount"
    FROM products p
    LEFT JOIN products bt ON p."linkedTo" = bt.id
    LEFT JOIN "catalogProducts" cp ON cp."productId" = p.id
    LEFT JOIN "catalogs" c ON c.id = cp."catalogId"
    LEFT JOIN "externalReferences" exwm ON exwm."productId" = p.id
        AND exwm.type = 'weedMapsProductVariantId'
    LEFT JOIN LATERAL (
        SELECT
            "externalId",
            "productId"
        FROM "externalReferences"
        WHERE 
            "type" = 'sellTreezId'
        AND
            "productId" = p.id
    ${uuidCondition}
    ) extz
        ON extz."productId" = p.id
    WHERE
        cp."catalogId" = :catalogId
    ${statusFilter}
    ${brandToFilterBy ? `AND (
            bt."brandName" ILIKE '${cleanTextForSQL(brandToFilterBy)}'
            OR SIMILARITY(p."brandName", '${cleanTextForSQL(brandToFilterBy)}') > ${brandNameCorrelationScore}
    )`: ''}
    ${uuidCondition === `ORDER BY "createdAt" DESC LIMIT 1` ? ''
    : `AND extz."externalId" IS NOT NULL`
    } 
`)

export default generateRetailSearchQuery;
