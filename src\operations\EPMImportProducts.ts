import _ from "lodash";
import Catalog from "../models/Catalog";
import CatalogProduct, { addProductToCatalog } from "../models/CatalogProduct";
import Product, { ProductInformation } from "../models/Product";
import logger from '@treez/dev-pack/logger';
import ExternalReference from "../models/ExternalReference";
import { assertNotNull } from "../lib/commonFunctions";
import { connection } from "@treez/dev-pack/db";
import { __ } from "lodash/fp";
import { OwnerType } from "@treez/commons/sharedTypings/product";
import { v4 as uuid } from 'uuid';



export class EPMProductUploadRequest {
    catalogId: number;
    productUploadList: ProductInformation[];
}

type ExternalReferenceIdResult = { externalId: string }[];

const duplicatesExternalReferencesQuery = `
select
	"externalId"
from
	"externalReferences" er
where
	"externalId" in (:externalIds)
group by
	"externalId"
having
	count("externalId") > 1;
`;

const buildProductToPriceMap = (productIds: number[], productUploadList: ProductInformation[]): Map<number, number> => {
    const priceMap = new Map<number, number>();
    productUploadList.forEach((product, index) => {
        priceMap.set(productIds[index], Number(product.price))
    });
    return priceMap;
}

export const excludeDuplicateExternalId = async (productsData: ProductInformation[]): Promise<ProductInformation[]> => {

    const externalIds = productsData.map((product) => product.uuid)
        .filter((id) => !_.isEmpty(id));

    if (externalIds.length === 0) {
        return productsData;
    }

    const [result] = await connection.query(duplicatesExternalReferencesQuery, {
        raw: true,
        replacements: {
            externalIds
        }
    }) as [ExternalReferenceIdResult, []];

    const duplicatedExternalReferenceIds = new Set(result.map(record => record.externalId));

    const filterUniqueExternalReferences = (product: ProductInformation) =>
        !product.uuid || !duplicatedExternalReferenceIds.has(product.uuid);

    return productsData.filter(filterUniqueExternalReferences);
}

const buildExternalIdToProductIdMap = (externalReferences: ExternalReference[]): Map<string, number> => {
    const productMap = new Map<string, number>();
    externalReferences.forEach(externalReference => {
        productMap.set(externalReference.externalId, externalReference.productId);
    });
    return productMap;
}

export const bulkCreateProductsIfNotExists = async (productsData: ProductInformation[], ownerId: number): Promise<Product[]> => {
    const references = await ExternalReference.findAll({
        where: {
            externalId: productsData
                .filter(productData => productData.uuid)
                .map(productData => productData.uuid!)
        }
    });

    const externalIdProductIdsMap = buildExternalIdToProductIdMap(references);

    const productRecords = productsData.map((uploadedProduct) =>
        ({ ...uploadedProduct, ownerId, ownerType: OwnerType.ORG, id: externalIdProductIdsMap.get(uploadedProduct.uuid!) || null })
    );

    const products = await Product.bulkCreate(productRecords, {
        // ignore creation if product id exists
        ignoreDuplicates: true,
    });

    const productsWithExternalReferences = new Set(references.map(reference => reference.productId));
    const productsWithoutExternalReference = products.filter(product => !productsWithExternalReferences.has(product.id));

    const newExternalReferences = productsWithoutExternalReference.map(product => ({
        type: 'sellTreezId',
        externalId: uuid(),
        productId: product.id,
    }));

    ExternalReference.bulkCreate(newExternalReferences);

    return products;
}

/**
 * Uploads new / existings products to EPM
 * 
 * 1) If not exist create a new product
 * 2) If it is store product update price
 * 3) If it is org product do nothing
 * 
 * @param request: EPMProductUploadRequest the request
 * @param userInfo: UserInfo The requestor information
 * 
*/
export const uploadProducts = async (request: EPMProductUploadRequest, userInfo: UserInfo) => {
    const { productUploadList, catalogId } = request;

    logger.debug("request received:", request)

    const storeCatalog = await Catalog.findByPk(catalogId);
    const orgCatalog = await Catalog.findByPk(storeCatalog?.parentCatalogId);

    assertNotNull(storeCatalog, "Store catalog does not exist");
    assertNotNull(orgCatalog, "Org catalog does not exist");

    const filteredProductUploadList = await excludeDuplicateExternalId(productUploadList);

    const products = await bulkCreateProductsIfNotExists(filteredProductUploadList, orgCatalog.ownerId);

    const productsIds = products.map(product => product.id);

    const priceMap = buildProductToPriceMap(productsIds, filteredProductUploadList);

    const orgCatalogProducts = await CatalogProduct.findAll({
        where: {
            productId: productsIds,
            catalogId: orgCatalog.id
        }
    });

    const orgProductIds = new Set(orgCatalogProducts.map(catalogProduct => catalogProduct.productId));

    const catalogProductInStore = await CatalogProduct.findAll({
        where: {
            productId: productsIds,
            catalogId: catalogId
        }
    });

    const storeProductIds = new Set(catalogProductInStore.map(catalogProduct => catalogProduct.productId));

    const filterProductsWithoutCatalogProduct = (id: number) => !orgProductIds.has(id) && !storeProductIds.has(id);
    const productsWithoutCatalogProduct = productsIds.filter(filterProductsWithoutCatalogProduct);

    productsWithoutCatalogProduct.forEach(async id => {
        const price = priceMap.get(id);
        await addProductToCatalog(storeCatalog, id, userInfo, {
            price
        });
    })

    catalogProductInStore.forEach(async (catalogProduct) => {
        const price = priceMap.get(catalogProduct.productId)

        catalogProduct.set({ price });
        await catalogProduct.save();
    });
}

