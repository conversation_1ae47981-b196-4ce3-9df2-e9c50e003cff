import {PriceTierMethod}                from "@treez/commons/sharedTypings/priceTier";
import CatalogProduct                   from "../models/CatalogProduct";
import Product                          from "../models/Product";
import PriceTier                        from "../models/PriceTier";
import {PriceTierThreshold}             from "@treez/commons/sharedTypings/priceTier";

export const updateProductsInTier = async (priceTierId: number) => {

    if ( !priceTierId ){ return; }

    const catalogProducts = await CatalogProduct.findAll({
        raw: true,
        nest: true,
        attributes: [["id", "catalogProductId"], "productId", "catalogId", "priceTierId", "price"],
        where: {
            priceTierId: priceTierId
        },
        include: [
            {
                model: Product,
                as: 'product',
                attributes: ["amount", "uom"]
            },
            {
                model: PriceTier,
                as: 'priceTier',
                attributes: ["method", "thresholds"]
            }
        ]
    });

    for (let i = 0; i < catalogProducts.length; i++) {

        const eachProductCatalogTier = Object.assign(new catalogProductTier(), catalogProducts[i]);

        const product = eachProductCatalogTier.product;
        const priceTier = eachProductCatalogTier.priceTier;

        const thresholds = priceTier.thresholds as PriceTierThreshold[];
        const isUnitBased = priceTier.method === PriceTierMethod.UNIT;

        if(!isUnitBased){
            if( product.amount === null || product.uom === null ){
                return 0;
            }
        }

        const quantity = isUnitBased ? 1 : product.amount;

        const price = await findThresholdPrice(quantity!, thresholds);

        await updateCatalogProductPrice(price!, eachProductCatalogTier.catalogProductId);
    }
}

export const findThresholdPrice = async (quantity: number, thresholds: PriceTierThreshold[]) => {

    for (let i = 0; i < thresholds.length; i++) {

        const threshold = thresholds[i]

        if ( threshold && threshold.start ){

            if( (quantity >= threshold.start && threshold.end === null) || (quantity >= threshold.start && quantity <= threshold.end!) ){

                return threshold.value * quantity;
            }
        }
    }
}

export const updateCatalogProductPrice = async (price: number, catalogProductId: number) => {
    return await CatalogProduct.update({
           price: price
       }, {
           where: {
               id: catalogProductId
           }
       });
}

export class catalogProductTier {
    catalogProductId: number;
    productId       : number;
    catalogId       : number;
    priceTierId     : number;
    price           : number;
    product         : Partial<Product>;
    priceTier       : Partial<PriceTier>;
}
