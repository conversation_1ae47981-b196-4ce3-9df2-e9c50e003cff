import config               from 'config';
import { unflatten }        from 'flat';
import { SearchRunner }     from '../../search/searchRunner';
import {
    applyOverrides,
    parseAttribute,
    parseDetail,
    isValidFilterField,
    SearchType,
}                           from '../../search/searchUtils';
import Product              from '../../models/Product';
import { ProductField }     from '@treez/commons/sharedTypings/product';

const mockFilter = (field: string)=> ({field, fuzzy:false, values:[], not:false});
const NORMAL_FIELD = mockFilter('normalField');
const ATTRIBUTE_THE_ATTRIBUTE = mockFilter('attribute.theAttribute');
const DETAILS_THE_DETAIL = mockFilter('details.theDetail');
const UUID = "uuid";
const SOURCE = "source";
const UUID_WITH_BARCODE = "uuidWithBarcode"
const TREEZ_BRAND = "TREEZ";

describe(`Search Function`, () => {
    it('Parses attributes', async ()=>{
        expect(parseAttribute(ATTRIBUTE_THE_ATTRIBUTE)).toBe('theAttribute')
        expect(parseDetail(ATTRIBUTE_THE_ATTRIBUTE)).toBe(null)
    });

    it('Parses details', async ()=>{
        expect(parseAttribute(DETAILS_THE_DETAIL)).toBe(null)
        expect(parseDetail(DETAILS_THE_DETAIL)).toBe('theDetail')
    });

    it('Ignores normal fields', async ()=>{
        expect(parseAttribute(NORMAL_FIELD)).toBe(null)
        expect(parseDetail(NORMAL_FIELD)).toBe(null)
    });

    const productOverrides = {
        attributes: {
            flavor: ['CHOCOLATE-OVERRIDE']
        },
        details: {
            doses: 2,
        },
        name: 'NAME_PRODUCT_OVERRIDE',
        subtype: 'PRE_PACK',
    }

    const catalogOverrides = {
        attributes: {
            flavor: ['VANILLA-OVERRIDE']
        },
        classification: 'HYBRID',
        details: {
            doses: 4,
        },
        name: 'NAME_CATALOG_OVERRIDE',
    }

    const getMockProduct = (productOverrides: any, catalogOverrides: any) => ({
        dataValues: {
            attributes: {
                flavor: ['CHOCOLATE'],
                general: ['YUMMY'],
            },
            brandName: 'TEST_BRAND_NAME',
            catalogOverrides: catalogOverrides,
            details: {
                thcMg: 10,
            },
            name: 'TEST_PRODUCT_NAME',
            productOverrides: productOverrides,
        }
    });

    it('apply product overrides', async ()=>{
        const mockProduct = getMockProduct(productOverrides, {});

        const finalProduct: Product = unflatten(applyOverrides(mockProduct))

        expect(finalProduct).toStrictEqual({
            attributes: {
                flavor: ['CHOCOLATE-OVERRIDE'],
                general: ['YUMMY'],
            },
            baseProduct: mockProduct.dataValues,
            brandName: 'TEST_BRAND_NAME',
            catalogOverrides: {},
            details: {
                doses: 2,
                thcMg: 10,
            },
            name: 'NAME_PRODUCT_OVERRIDE',
            productOverrides: productOverrides,
            subtype: 'PRE_PACK',
        });
    });

    it('apply catalog overrides', async ()=>{
        const mockProduct = getMockProduct({}, catalogOverrides);

        const finalProduct: Product = unflatten(applyOverrides(mockProduct))

        expect(finalProduct).toStrictEqual({
            attributes: {
                flavor: ['VANILLA-OVERRIDE'],
                general: ['YUMMY'],
            },
            baseProduct: mockProduct.dataValues,
            brandName: 'TEST_BRAND_NAME',
            catalogOverrides: catalogOverrides,
            classification: 'HYBRID',
            details: {
                doses: 4,
                thcMg: 10,
            },
            name: 'NAME_CATALOG_OVERRIDE',
            productOverrides: {},
        });
    });

    it('applies product and catalog overrides', async ()=>{
        const mockProduct = getMockProduct(productOverrides, catalogOverrides);

        const finalProduct: Product = unflatten(applyOverrides(mockProduct))

        expect(finalProduct).toStrictEqual({
            attributes: {
                flavor: ['VANILLA-OVERRIDE'],
                general: ['YUMMY'],
            },
            baseProduct: mockProduct.dataValues,
            brandName: 'TEST_BRAND_NAME',
            catalogOverrides: catalogOverrides,
            classification: 'HYBRID',
            details: {
                doses: 4,
                thcMg: 10,
            },
            name: 'NAME_CATALOG_OVERRIDE',
            productOverrides: productOverrides,
            subtype: 'PRE_PACK',
        });
    });

    it('applies product and catalog attributes overrides null', async () => {
        const catalogOverrides = {
            attributes: {
                flavor: ['VANILLA']
            },
        }

        const productOverrides = {
            attributes: {
                general: ['YUMMY']
            },
        }

        const mockProduct = {
            dataValues: {
                attributes: null,
                catalogOverrides: catalogOverrides,
                productOverrides: productOverrides,
            }
        }

        const finalProduct: Product = unflatten(applyOverrides(mockProduct), { overwrite: true })

        expect(finalProduct.attributes).toStrictEqual(
            {
                flavor: ['VANILLA'],
                general: ['YUMMY']
            }
        );
    });

    it('accepts uuid/source/uuidWithBarcode as a filter field', async () => {
        [UUID, SOURCE, UUID_WITH_BARCODE].forEach(filter => expect(isValidFilterField(filter)).toBe(true));
    })

    it(`applies a default page size of 50 for getting brand products`, async () => {

        const search = new SearchRunner({});

        search.buildPaging();

        expect(search.replacements.pageSize).toBe(50);
    });

    it(`applies a default page size of 50 for getting products within a catalog`, async () => {
        const search = new SearchRunner({
            catalogId: 1,
        });

        search.buildPaging();

        expect(search.replacements.pageSize).toBe(50);
    });

    it(`it applies the max page size on requests that are larger than mwhat is allowed getting brand products`, async () => {
        const pageSizeOverMaximum: number = config.search.maxPageSize + 1000;

        const search = new SearchRunner({
            pageSize: pageSizeOverMaximum
        });

        search.buildPaging();

        expect(search.replacements.pageSize).toBe(config.search.maxPageSize);
    });

    it(`it applies the max page size on requests that are larger than mwhat is allowed getting products within a catalog`, async () => {
        const pageSizeOverMaximum: number = config.search.maxPageSize + 1000;

        const search = new SearchRunner({
            catalogId: 1,
            pageSize : pageSizeOverMaximum
        });

        search.buildPaging();

        expect(search.replacements.pageSize).toBe(config.search.maxPageSize);
    });

    it(`defaults to a ${SearchType.Retail} for any searches that don't explicitly state the type but include a catalogId`, async () => {
        const search = new SearchRunner({
            catalogId: 1,
        });

        expect(search.searchType).toBe(SearchType.Retail);
    });

    it('applies "ORDER BY createdAt" when uuidFilter is empty', async () => {
        const uuidCondition = 'ORDER BY "createdAt" DESC LIMIT 1';
        const search = new SearchRunner({
            catalogId: 1,
        });

        search.buildBaseQuery();
        expect(search.baseQuery).toMatch(uuidCondition);
        expect(search.replacements.uuidFilterValues).toHaveLength(0);
    });

    it('applies externalId filter when uuidFilter is not empty', async () => {
        const uuidCondition = 'AND extz."externalId" IS NOT NULL';
        const productUUID = "1";
        const search = new SearchRunner({
            catalogId: 1,
            filters: [{
                field: UUID,
                fuzzy: false,
                not: false,
                values: [productUUID],
            }],
        });

        search.buildBaseQuery();
        expect(search.baseQuery).toMatch(uuidCondition);
        expect(search.replacements.uuidFilterValues).toHaveLength(1);
        expect(search.replacements.uuidFilterValues).toContain(productUUID);
    });

    it('applies uuidWithBarcode replacement in buildSearchQuery() when uuidWithBarcodeFilter is not empty, sourceFilter is empty and a search string is provided', async () => {
        const productUUID = "1";
        const search = new SearchRunner({
            catalogId: 1,
            searchString: TREEZ_BRAND,
            filters: [{
                field: UUID_WITH_BARCODE,
                fuzzy: false,
                not: false,
                values: [productUUID],
            }],
        });

        search.buildSearchQuery();
        expect(search.replacements.uuidWithBarcodeFilterValues).toHaveLength(1);
        expect(search.replacements.uuidWithBarcodeFilterValues).toContain(productUUID);
    });

    it('does not apply uuidWithBarcode replacement in buildSearchQuery() when uuidFilter and search string provided', async () => {
        const productUUID = "1";
        const search = new SearchRunner({
            catalogId: 1,
            searchString: TREEZ_BRAND,
            filters: [
                {
                    field: UUID,
                    fuzzy: false,
                    not: false,
                    values: [productUUID],
                },
                {
                    field: SOURCE,
                    fuzzy: false,
                    not: false,
                    values: ['POS'],
                }
            ]
        });

        search.buildSearchQuery();
        expect(search.replacements).not.toContain('uuidWithBarcodeFilterValues');
    });

    it('does not apply uuidWithBarcode replacement in buildSearchQuery() when uuidWithBarcodeFilter and search string provided, and source is POS', async () => {
        const productUUID = "1";
        const search = new SearchRunner({
            catalogId: 1,
            searchString: TREEZ_BRAND,
            filters: [
                {
                    field: UUID_WITH_BARCODE,
                    fuzzy: false,
                    not: false,
                    values: [productUUID],
                },
                {
                    field: SOURCE,
                    fuzzy: false,
                    not: false,
                    values: ['POS'],
                }
            ]
        });

        search.buildSearchQuery();
        expect(search.replacements).not.toContain('uuidWithBarcodeFilterValues');
    });

    it('applies amount filter', async () => {
        const filteringQuery = '\"amount\" = ANY (array[:filterKey0])';
        const search = new SearchRunner({
            catalogId: 1,
            filters: [
                {
                    field: ProductField.AMOUNT.toString(),
                    fuzzy: false,
                    not: false,
                    values: ['1.25', '7.14']
                }
            ]
        });

        search.buildFilters();
        expect(search.replacements).toHaveProperty('filterKey0');
        expect(search.replacements['filterKey0']).toEqual([1.25, 7.14]);
        expect(search.filtering).toContain(`p.${filteringQuery}`);
        expect(search.filtering).toContain(`bt.${filteringQuery}`);
    });

    it('applies amount filter with not', async () => {
        const filteringQuery = '\"amount\" = ANY (array[:filterKey0])';
        const search = new SearchRunner({
            catalogId: 1,
            filters: [
                {
                    field: ProductField.AMOUNT.toString(),
                    fuzzy: false,
                    not: true,
                    values: ['1.25', '7.14']
                }
            ]
        });

        search.buildFilters();
        expect(search.replacements).toHaveProperty('filterKey0');
        expect(search.replacements['filterKey0']).toEqual([1.25, 7.14]);
        expect(search.filtering).toContain(`NOT p.${filteringQuery}`);
        expect(search.filtering).toContain(`NOT bt.${filteringQuery}`);
    });
})
