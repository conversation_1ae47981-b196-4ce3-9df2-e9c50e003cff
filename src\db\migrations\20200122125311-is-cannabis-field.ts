import _                                from 'lodash';
import {
    booleanField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const CANNABIS = 'cannabis';
const PRODUCTS = 'products';


export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(PRODUCTS, CANNABIS, booleanField);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeColumn(PRODUCTS, CANNABIS);
    },
}
