import {
    UOM,
    OwnerType,
}                                   from '@treez/commons/sharedTypings/product';
import Assortment, {
    AssortmentType
}                                   from '../../models/Assortment';
import AssortmentProduct, {
    decorateProductsWithAssortments
}                                   from '../../models/AssortmentProduct';
import Catalog                      from '../../models/Catalog';
import Product                      from '../../models/Product';
import { ExternalReferenceType }    from '../../models/ExternalReference';

describe('AssortmentProduct model', () => {
    it(`Bulk create assortment products`, async () => {
        const [
            product,
            catalog
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "<PERSON>'s Wonder Weed",
                eCommerceName:      "<PERSON>'s Wonder Weed",
                name:               "<PERSON>'s Wonder Weed",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const externalReference = await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        const assortmentProducts = await AssortmentProduct.bulkCreateAssortmentProducts(assortment.id, ['abcde'], catalog.id);

        expect(externalReference).toBeDefined();
        expect(assortmentProducts).toBeDefined();
        expect(assortmentProducts).toHaveLength(1);
    });

    it(`Doesnt error when no products are found on bulk update`, async () => {
        const ownerId = 1000;
        const otherOwnerId = 2000;
        const [
            product,
            catalog
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            ownerId,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            otherOwnerId,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        try {
            await AssortmentProduct.bulkCreateAssortmentProducts(assortment.id, ['abcde'], catalog.id);
        } catch (error) {
            fail(error);
        }
    });

    it(`Delete an assortment product`, async () => {
        const [
            product,
            catalog
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const externalReference = await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        await AssortmentProduct.create({
            assortmentId: assortment.id,
            productId: product.id
        });

        await AssortmentProduct.removeAssortmentFromProduct(assortment.id, 'abcde', catalog.id);

        const deletedAssortmentProduct = await AssortmentProduct.findOne({
            where: {
                assortmentId: assortment.id,
                productId: product.id
            }
        });

        expect(externalReference).toBeDefined();
        expect(deletedAssortmentProduct).toBeNull();
    });

    it(`Delete an assortment product doesnt fail if no products are found`, async () => {
        const ownerId = 1000;
        const otherOwnerId = 2000;
        const [
            product,
            catalog
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            ownerId,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            otherOwnerId,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        await AssortmentProduct.create({
            assortmentId: assortment.id,
            productId: product.id
        });

        try {
            await AssortmentProduct.removeAssortmentFromProduct(assortment.id, 'abcde', catalog.id);
        } catch (error) {
            fail(error);
        }
    });

    it(`Create assortmentProduct having a reference with multiple products`, async () => {
        const [
            product,
            product2,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Hastings' Herb",
                displayName:        "Wade's Wonder Weed",
                eCommerceName:      "Wade's Wonder Weed",
                name:               "Wade's Wonder Weed",
                ownerId:            1111,
                ownerType:          OwnerType.ORG,
                price:              "25.000000",
                size:               "Dime Baggie",
                subtype:            "Sungrown",
                type:               "Flower",
                uom:                UOM.EACH,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1000,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            1111,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);
        const externalReference = await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );
        await product2.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );
        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });
        const assortmentProducts = await AssortmentProduct.bulkCreateAssortmentProducts(assortment.id, ['abcde'], catalog.id);
        expect(externalReference).toBeDefined();
        expect(assortmentProducts).toBeDefined();
        expect(assortmentProducts).toHaveLength(1);
    });

    it(`Find an assortmentProduct that has an org product`, async () => {
        const parentCatalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1000,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const catalog = await Catalog.create({
            name:               "Test Store Catalog",
            ownerId:            1234,
            ownerType:          OwnerType.STORE,
            parentCatalogId:    parentCatalog.id,
        });

        const product = await Product.create({
            amount:             "1.000000",
            brandName:          "Hastings' Herb",
            displayName:        "Wade's Wonder Weed",
            eCommerceName:      "Wade's Wonder Weed",
            name:               "Wade's Wonder Weed",
            ownerId:            1000,
            ownerType:          OwnerType.ORG,
            price:              "25.000000",
            size:               "Dime Baggie",
            subtype:            "Sungrown",
            type:               "Flower",
            uom:                UOM.EACH,
        });

        await product.addExternalReference(
            ExternalReferenceType.SELL_TREEZ_ID,
            'abcde'
        );

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        await AssortmentProduct.bulkCreateAssortmentProducts(assortment.id, ['abcde'], catalog.id);
        const assortmentProducts = await AssortmentProduct.findAssortmentProductsByProductIds(catalog.id, AssortmentType.COLLECTION, ['abcde']);

        expect(assortmentProducts).toBeDefined();
        expect(assortmentProducts).toHaveLength(1);
    });

    it(`Decorate products with assortments`, async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            1000,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const [
            product,
            product2,
            product3,
            assortment,
            assortment2,
            assortment3
        ] = await Promise.all([
            Product.create({
                amount:             '1.000000',
                name:               'Wonderful Cookie',
                type:               'EDIBLE',
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                uom:                'g',
            }),
            Product.create({
                amount:             '1.000000',
                name:               'Happy Pill',
                type:               'PILL',
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                uom:                'g',
            }),
            Product.create({
                amount:             '1.000000',
                name:               'Tasty Cookie',
                type:               'EDIBLE',
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                uom:                'g',
            }),
            Assortment.create({
                catalogId:          catalog.id,
                type:               AssortmentType.COLLECTION,
                name:               'Example',
                icon:               'example.svg',
                order:              0
            }),
            Assortment.create({
                catalogId:          catalog.id,
                type:               AssortmentType.COLLECTION,
                name:               'Deals',
                icon:               'deals.svg',
                order:              0
            }),
            Assortment.create({
                catalogId:          catalog.id,
                type:               AssortmentType.COLLECTION,
                name:               '36 North',
                icon:               'north_36.svg',
                order:              0
            })
        ]);

        await Promise.all([
            AssortmentProduct.create({
                assortmentId:   assortment.id,
                productId:      product.id
            }),
            AssortmentProduct.create({
                assortmentId:   assortment2.id,
                productId:      product2.id
            }),
            AssortmentProduct.create({
                assortmentId:   assortment3.id,
                productId:      product2.id
            })
        ]);

        expect(product.assortments).toBe(undefined);
        expect(product2.assortments).toBe(undefined);
        expect(product3.assortments).toBe(undefined);

        const products: Product[] = await Product.findAll({
            where: {
                id: [product.id, product2.id, product3.id],
            },
            raw: true
        });

        await decorateProductsWithAssortments(products);
        expect(products).toBeDefined();
        expect(products).toHaveLength(3);
        expect(products.map(p => p.id)).toEqual(expect.arrayContaining([product.id, product2.id, product3.id]));

        const productWithAssortment = products.find(p => p.id === product.id);
        const product2WithAssortment = products.find(p => p.id === product2.id);
        const product3WithAssortment = products.find(p => p.id === product3.id);
        expect(productWithAssortment!.assortments).toHaveLength(1);
        expect(product2WithAssortment!.assortments).toHaveLength(2);
        expect(product3WithAssortment!.assortments).toBe(undefined);
        expect(productWithAssortment!.assortments[0].id).toEqual(assortment.id);
        expect(product2WithAssortment!.assortments.map(a => a.id)).toEqual(expect.arrayContaining([assortment2.id, assortment3.id]));
    });
});
