import {
    ReasonPhrases,
    StatusCodes,
}                                   from 'http-status-codes';
import {
    NextFunction,
    Response,
}                                   from "express";
import {
    AuthenticatedRequest,
    hasPermissions,
    TreezClearance,
}                                   from "@treez/dev-pack/auth";
import { ErrorResponse }            from "@treez/dev-pack/errors";
import { checkTreezAuthorization }  from "@treez/dev-pack/middleware/checkRequestorPermissions";
import {
    getIdFromRequestPath,
    getIdsFromRequestQuery,
    getValueFromRequestBody,
}                                   from "../lib/expressUtils";
import { ProductPermission }        from "../lib/permissions";
import Catalog                      from "../models/Catalog";
import Product                      from "../models/Product";
import PriceTier                    from "../models/PriceTier";

export const multipleResourceChecks = (
    getOwnerIds: (request: AuthenticatedRequest) => Promise<number[]> | number[],
    permissionPrescription: ProductPermission[][],
    treezClearance = TreezClearance.Any) => async (
    request: AuthenticatedRequest,
    response: Response,
    next: NextFunction,
) => {
    try {
        const {
            requestor,
            userPermissions,
        } = request;

        const hasTreezClearance = checkTreezAuthorization(requestor, treezClearance);

        if (hasTreezClearance) {
            return next()
        }

        const ownerIds = await getOwnerIds(request);

        return ownerIds.every((ownerId) => hasPermissions(
            userPermissions,
            ownerId,
            permissionPrescription
        ))
        ? next()
        : next(new ErrorResponse(StatusCodes.FORBIDDEN, ReasonPhrases.FORBIDDEN));
    }
    catch (error) {
        return next(error);
    }
}

export const resourcePermissionCheck = (
    getOwnerId: (request: AuthenticatedRequest) => Promise<number> | number,
    permissionPrescription: ProductPermission[][],
    treezClearance = TreezClearance.Any
) => async (
    request: AuthenticatedRequest,
    response: Response,
    next: NextFunction,
) => {
    try {
        const {
            requestor,
            userPermissions
        } = request;

        const hasTreezClearance = checkTreezAuthorization(requestor, treezClearance);

        if (hasTreezClearance) {
            return next();
        }

        const ownerId = await getOwnerId(request);

        return hasPermissions(userPermissions, ownerId, permissionPrescription)
        ?
        next()
        : next(new ErrorResponse(StatusCodes.FORBIDDEN, ReasonPhrases.FORBIDDEN));
    }
    catch (error) {
        return next(error);
    }
}

const getProductOwner = (pathParam: string) => async (request: AuthenticatedRequest) => {
    const productId = getIdFromRequestPath(pathParam, request);

    const product = await Product.findByPk(productId, {attributes: ['ownerId']});

    if (product == null) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, ReasonPhrases.BAD_REQUEST);
    }

    return product.ownerId;
};

const getCatalogOwner = (pathParam: string) => async (request: AuthenticatedRequest) => {
    const catalogId = getIdFromRequestPath(pathParam, request);

    const catalog = await Catalog.findByPk(catalogId, {attributes: ['ownerId']});

    if (catalog == null) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, ReasonPhrases.BAD_REQUEST);
    }

    return catalog.ownerId
};

const getPriceTierOwner = (pathParam: string) => async (request: AuthenticatedRequest) => {
    const priceTierId = getIdFromRequestPath(pathParam, request);

    const priceTier = await PriceTier.findByPk(priceTierId, {attributes: ['ownerId']});

    if (priceTier == null) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, ReasonPhrases.BAD_REQUEST);
    }

    return priceTier.ownerId;
};

const getOwnerIdsForMultipleProducts = (
    location: 'body' | 'query',
    key: string,
) => async (request: AuthenticatedRequest) => {
    const productIds = location === 'body' ?
    getValueFromRequestBody<number[]>(key, request, [])
    : getIdsFromRequestQuery(key, request);

    const products = await Product.findAll({
        where: {id: productIds},
        attributes: ['ownerId']
    });

    return products.map(({ownerId}) => ownerId)
};

const getOwnerIdsForMultipleCatalogs = (
    location: 'body' | 'query',
    key: string,
) => async (request: AuthenticatedRequest) => {
    const catalogIds = location === 'body' ?
    getValueFromRequestBody<number[]>(key, request, [])
    : getIdsFromRequestQuery(key, request);

    const catalogs = await Catalog.findAll({
        where: {id: catalogIds},
        attributes: ['ownerId']
    });

    return catalogs.map(({ownerId}) => ownerId)
};

const getOwnerIdsForMultiplePriceTiers = (
    location: 'body' | 'query',
    key: string,
) => async (request: AuthenticatedRequest) => {
    const priceTierIds = location === 'body' ?
    getValueFromRequestBody<number[]>(key, request, [])
    : getIdsFromRequestQuery(key, request);

    const priceTiers = await PriceTier.findAll({
        where: {id: priceTierIds},
        attributes: ['ownerId']
    });

    return priceTiers.map(({ownerId}) => ownerId);
}

export const checkProductPermissions = (
    pathParam: string,
    permissionPerscription: ProductPermission[][],
    treezClearance = TreezClearance.Any
) => resourcePermissionCheck(
    getProductOwner(pathParam),
    permissionPerscription,
    treezClearance,
);

export const checkCatalogPermissions = (
    pathParam: string,
    permissionPerscription: ProductPermission[][],
    treezClearance = TreezClearance.Any
) => resourcePermissionCheck(
    getCatalogOwner(pathParam),
    permissionPerscription,
    treezClearance,
);

export const checkPriceTiersPermissions = (
    pathParam: string,
    permissionPerscription: ProductPermission[][],
    treezClearance = TreezClearance.Any,
) => resourcePermissionCheck(
    getPriceTierOwner(pathParam),
    permissionPerscription,
    treezClearance,
);

export const checkPermissionsOnMultipleProducts = (
    location: 'body' | 'query',
    key: string,
    permissionPerscription: ProductPermission[][],
    treezClearance = TreezClearance.Any,
) => multipleResourceChecks(
    getOwnerIdsForMultipleProducts(location, key),
    permissionPerscription,
    treezClearance,
);

export const checkPermissionsOnMultipleCatalogs = (
    location: 'body' | 'query',
    key: string,
    permissionsPrescription: ProductPermission[][],
    treezClearance = TreezClearance.Any,
) => multipleResourceChecks(
    getOwnerIdsForMultipleCatalogs(location, key),
    permissionsPrescription,
    treezClearance,
);

export const checkPermissionsOnMultiplePriceTiers = (
    location: 'body' | 'query',
    key: string,
    permissionsPrescription: ProductPermission[][],
    treezClearance = TreezClearance.Any,
) => multipleResourceChecks(
    getOwnerIdsForMultiplePriceTiers(location, key),
    permissionsPrescription,
    treezClearance,
)

