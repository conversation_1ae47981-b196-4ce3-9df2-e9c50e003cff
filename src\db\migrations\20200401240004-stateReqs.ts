import { QueryInterface }   from 'sequelize';
import { OwnerType }        from '@treez/commons/sharedTypings/product';
import {
    autoPKField,
    jsonField,
    createUpdateColumns,
    intField,
    stringField,
    foreignKey,
    enumField,
}                           from '@treez/dev-pack/db/fieldTypes';

const id = "id";

const catalogs = "catalogs";
const productRequirements = "productRequirements";
const productRequirementsToCatalogs = "productRequirementsToCatalogs";
const ownerId = 'ownerId';
const ownerType = 'ownerType';
const catalogId = 'catalogId';
const productRequirementsId = 'productRequirementsId';

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.createTable(productRequirements, {
            id             : autoPKField,
            description    : stringField,
            ownerId        : stringField,
            ownerType      : enumField(OwnerType),
            requirements   : jsonField,
            ...createUpdateColumns
        });

        await queryInterface.addIndex(productRequirements, [ownerId]);
        await queryInterface.addIndex(productRequirements, [ownerType]);

        await queryInterface.createTable(productRequirementsToCatalogs, {
            id: autoPKField,
            catalogId: foreignKey(intField, catalogs, id),
            productRequirementsId: foreignKey(intField, productRequirements, id),
            ...createUpdateColumns,
        });

        await queryInterface.addIndex(productRequirementsToCatalogs, [catalogId]);
        await queryInterface.addIndex(productRequirementsToCatalogs, [productRequirementsId]);

    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.dropTable(productRequirementsToCatalogs);
        await queryInterface.dropTable(productRequirements);
    },
}
