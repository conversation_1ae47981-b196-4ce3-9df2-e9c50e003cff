import { StatusCodes }              from 'http-status-codes';
import _, {
    Dictionary,
}                                   from 'lodash';
import {
    AutoIncrement,
    BeforeBulkCreate,
    BeforeCreate,
    BeforeUpdate,
    BelongsTo,
    Column,
    Default,
    ForeignKey,
    PrimaryKey,
    Table,
}                                   from 'sequelize-typescript';
import { FindOptions }              from 'sequelize/types';
import { Op }                       from 'sequelize';
import {
    OwnerType,
    ProductData,
    ProductField,
}                                   from '@treez/commons/sharedTypings/product';
import { TreezModel }               from '@treez/dev-pack/models/TreezBaseModel';
import { ErrorResponse }            from '@treez/dev-pack/errors';
import {
    enumField,
    jsonField,
    preciseMonetaryField,
}                                   from '@treez/dev-pack/db/fieldTypes';
import {
    connection,
    transaction,
}                                   from '@treez/dev-pack/db';
import { isNilOrEmptyObject }       from '../lib/commonFunctions';
import {
    parseNumericFields,
    getProductFieldPaths,
}                                   from '../lib/product';
import { CatalogProductAction }     from '../lib/sharedInterfaces';
import Catalog, {
    findCatalogById,
    findChildCatalogIds,
}                                   from './Catalog';
import {
    recordCatalogProductChange,
    bulkRecordCatalogProductChanges
}                                   from './CatalogProductChange';
import Product, {
    findProductById,
    ProductInCatalog,
    ProductRecord,
}                                   from './Product';
import {
    ExternalReferenceType,
    findWeedMapsProductVariantId,
}                                   from './ExternalReference';
import PriceTier                    from './PriceTier';

const {
    CREATE,
    UPDATE,
    DELETE,
    DEACTIVATE
} = CatalogProductAction;

export enum CatalogProductStatus {
    ACTIVE      = 'ACTIVE',
    DELETED     = 'DELETED',
    DEACTIVATED = 'DEACTIVATED'
}

/**
 * @swagger
 *  components:
 *      schemas:
 *          CatalogProduct:
 *              properties:
 *                  id:
 *                      title: Catalog Product Id
 *                      type: integer
 *                  catalogid:
 *                      title: Catalog Id
 *                      type: integer
 *                  catalogOverrides:
 *                      description: Catalog Specific information for the product
 *                      title: Catalog Overrides
 *                      $ref: '#/components/schemas/Product'
 *                  price:
 *                      title: Price
 *                      type: number
 *                      minimum: 0
 *                  priceTierId:
 *                      title: Price Tier Id
 *                      type: integer
 *                  productId:
 *                      title: Product Id
 *                      type: integer
 *                  status:
 *                      title: Product Status
 *                      type: string
 *                      enum: [ACTIVE, DEACTIVATED, DELETED]
 *          ProductInCatalog:
 *              allOf:
 *                - $ref: '#/components/schemas/CatalogProduct'
 *                - $ref: '#/components/schemas/Product'
 */

export interface CatalogProductRecord {
    id              : number,
    catalogId       : number,
    catalogOverrides: Partial<ProductRecord>,
    price           : number,
    priceTierId     : number,
    productId       : number,
    status          : CatalogProductStatus,
}


@Table({tableName: 'catalogProducts'})
export default class CatalogProduct extends TreezModel<CatalogProduct> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                             number;

    @ForeignKey(() => Catalog)
    @Column
    catalogId:                      number;

    @Column(jsonField)
    catalogOverrides:               Partial<ProductRecord>;

    @Column(preciseMonetaryField)
    price:                          number;

    @ForeignKey(() => Product)
    @Column
    productId:                      number;

    @Default(CatalogProductStatus.ACTIVE)
    @Column(enumField(CatalogProductStatus))
    status:                         CatalogProductStatus;

    @ForeignKey(() => PriceTier)
    @Column
    priceTierId?:                    number;

    @BelongsTo(() => Product)
    product:                        Product;

    @BelongsTo(() => Catalog)
    catalog:                        Catalog;

    @BelongsTo(() => PriceTier)
    priceTier?:                      PriceTier;

    @BeforeCreate
    static async setPrice(instance: CatalogProduct): Promise<void> {
        if(!instance.price) {
            await setPriceToParentCatalogPriceIfStoreProduct(instance);
        }
    }

    @BeforeBulkCreate
    static async setBulkPrice(instances: CatalogProduct[]): Promise<void> {
        await Promise.all(
            _.map(instances, async (instance) => {
                if(!instance.price) {
                    return await setPriceToParentCatalogPriceIfStoreProduct(instance);
                }
            })
        );
    }

    @BeforeCreate
    @BeforeUpdate
    static async trimFields(instance: CatalogProduct) {
        const catalogOverrides = {
            ...instance.catalogOverrides
        };

        const {
            brandName,
            eCommerceName,
        } = catalogOverrides;

        if (brandName) {
            catalogOverrides.brandName = brandName.trim();
        }

        if (eCommerceName) {
            catalogOverrides.eCommerceName = eCommerceName.trim();
        }

        instance.set('catalogOverrides', catalogOverrides);
    }

    public clearOverrides = async (fields : ProductData[]) => {
        const catalogProduct : CatalogProduct = this;

        const productFieldPaths = getProductFieldPaths(fields);

        let catalogOverrides : Partial<ProductRecord> = _.omit(catalogProduct.catalogOverrides, productFieldPaths);

        if (_.isEmpty(catalogOverrides.details)) {
            catalogOverrides = _.omit(catalogOverrides, 'details');
        }
        if (_.isEmpty(catalogOverrides.attributes)) {
            catalogOverrides = _.omit(catalogOverrides, 'attributes');
        }

        await catalogProduct.update({
            catalogOverrides,
        });
    }

    public async getProductCatalogPresence (excludeLinkedProduct = false): Promise<ProductInCatalog> {
        const catalogProduct = _.omit(this.get(), 'catalogOverrides') as CatalogProduct;
        const catalogOverrides = this.catalogOverrides;
        const product = await findProductById(this.productId);

        let productValues: Partial<Product>;

        if (excludeLinkedProduct === true) {
            productValues = product.get();
        }
        else {
            productValues = await product.applyOverrides();
        }

        const catalogOverridesAttributes = _.get(catalogOverrides, ProductField.ATTRIBUTES, {});
        const catalogOverridesDetails = _.get(catalogOverrides, ProductField.DETAILS, {});

        const appliedAttributes = {
            ...productValues.attributes,
            ...catalogOverridesAttributes,
        };

        const appliedDetails = {
            ...productValues.details,
            ...catalogOverridesDetails,
        };

        const weedMapsProductVariantId = await findWeedMapsProductVariantId(this.productId);

        if (excludeLinkedProduct) {
            return {
                ...productValues,
                ...catalogProduct,
                ...catalogOverrides,
                attributes: _.isEmpty(appliedAttributes) ? null : appliedAttributes,
                details: _.isEmpty(appliedDetails) ? null : appliedDetails,
                catalogProductId: catalogProduct.id,
                id: product.id,
            } as ProductInCatalog
        }
        else {
            return {
                ...productValues,
                ...catalogProduct,
                ...catalogOverrides,
                attributes: _.isEmpty(appliedAttributes) ? null : appliedAttributes,
                details: _.isEmpty(appliedDetails) ? null : appliedDetails,
                catalogProductId: catalogProduct.id,
                id: product.id,
                [ExternalReferenceType.WEED_MAPS_PRODUCT_VARIANT_ID]: weedMapsProductVariantId,
            } as ProductInCatalog;
        }
    }

    public isEqual(otherCatalogProduct: CatalogProduct, nilIsEmpty = false) {
        const catalogProductParsed = parseNumericFields(this);
        const otherCatalogProductParsed = parseNumericFields(otherCatalogProduct);

        return _.isEqualWith(catalogProductParsed, otherCatalogProductParsed, (objValue, othValue, key) => {
            if (nilIsEmpty && (
                (objValue === othValue) ||
                (isNilOrEmptyObject(objValue) && isNilOrEmptyObject(othValue))
            )) {
                return true;
            }

            if (key === 'catalogOverrides') {
                return _.isEqual(objValue, othValue);
            }

            return undefined; // default comparison logic
        });
    }
}

export const defaultCatalogProductAttributes = [
    'id',
    'status',
    'catalogId',
    'productId',
    'catalogOverrides',
    'price',
    'priceTierId',
]

export const findCatalogProduct = async (productId: number, catalogId: number) => {
    const catalogProduct = await CatalogProduct.findOne({
        where: {
            productId,
            catalogId,
        }
    });

    if (catalogProduct == null) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Product with the id ${productId} could not be found in catalog ${catalogId}`
        )
    }

    return catalogProduct
}

const setPriceToParentCatalogPriceIfStoreProduct = async (instance : CatalogProduct) =>{
    const catalog: Catalog = await Catalog.findByPk(instance.catalogId) as Catalog;

    if(catalog && catalog.ownerType === OwnerType.STORE) {

        if(catalog.parentCatalogId != null) {
            const parentCatalogProduct = await CatalogProduct.findOne({
                where: {
                    productId: instance.productId,
                    catalogId: catalog.parentCatalogId
                }
            });

            if(parentCatalogProduct != null && parentCatalogProduct.price != null) {
                instance.price = parentCatalogProduct.price;
                return;
            }
        }
        instance.price = 0;
    }
}

export const getCatalogProduct = async (options: FindOptions) => {
    const queryOptions = {
        attributes: defaultCatalogProductAttributes,
        ...options,
    }

    const catalogProduct = await CatalogProduct.findOne(queryOptions);

    if ( catalogProduct == null ) {
        throw new ErrorResponse(StatusCodes.NOT_FOUND, `Requested CatalogProduct does not exist`);
    }

    return catalogProduct;
};

export const addProductToCatalog = async (
    catalog: Catalog,
    productId: number,
    userInfo: UserInfo,
    options: Partial<CatalogProduct> = {
        status: CatalogProductStatus.ACTIVE,
    },
) => {
    const catalogProduct = await CatalogProduct.findOne({
        where: {
            catalogId: catalog.id,
            productId
        }
    });

    if (catalogProduct == null) {
        const newCatalogProduct = await CatalogProduct.create({
            productId,
            catalogId: catalog.id,
            ..._.pick(options, [
                'catalogOverrides',
                'price',
                'priceTierId',
                'status',
            ])
        });

        await recordCatalogProductChange(
            newCatalogProduct.id,
            CREATE,
            userInfo,
            newCatalogProduct,
        );

        if (catalog.parentCatalogId) {
            const parentCatalog = await findCatalogById(catalog.parentCatalogId);

            await addProductToCatalog(parentCatalog, productId, userInfo, options);
        }

        return newCatalogProduct;
    }

    return catalogProduct;
}

export const addProductToEpm = async (
    catalog: Catalog,
    productId: number,
    userInfo: UserInfo,
    options: Partial<CatalogProduct> = {
        status: CatalogProductStatus.ACTIVE,
    },
) => {
    if (catalog.parentCatalogId) {
        const parentCatalog = await findCatalogById(catalog.parentCatalogId);
        return await addProductToCatalog(parentCatalog, productId, userInfo, options);
    }
    return await addProductToCatalog(catalog, productId, userInfo, options);
}

export const removeProductFromCatalog = async (
    catalogId: number,
    productId: number,
    userInfo: UserInfo,
) => {
    const catalogProduct = await CatalogProduct.findOne({
        where: {
            catalogId,
            productId,
        }
    });

    if (catalogProduct != null) {
        const previousValues = {...catalogProduct.get()};

        await catalogProduct.update({
            status: CatalogProductStatus.DELETED
        });

        const newValues = catalogProduct.get();

        await recordCatalogProductChange(
            catalogProduct.id,
            DELETE,
            userInfo,
            newValues,
            previousValues,
        );

        return catalogProduct;
    }
    return;
}


const getMergedCatalogProductUpdates = (
    catalogProduct: CatalogProduct,
    updates: Partial<CatalogProductRecord>
) => {
    const whiteListedUpdate = _.pick(
        updates, [
            'price',
            'priceTierId',
            'status',
        ]
    );

    const whiteListedCatalogOverrides = _.pick(
        _.get(updates, 'catalogOverrides'), Product.validPatchFields
    );

    const details = {
        ..._.get(catalogProduct, 'catalogOverrides.details'),
        ...whiteListedCatalogOverrides.details,
    }

    const attributes = {
        ..._.get(catalogProduct, 'catalogOverrides.attributes'),
        ...whiteListedCatalogOverrides.attributes,
    }

    const catalogOverrides = {
        ...catalogProduct.catalogOverrides,
        ...whiteListedCatalogOverrides,
        attributes,
        details,
    };
    _.forEach(catalogOverrides, (value, key) => {
        if (_.isObject(value) && _.isEmpty(value)) {
            delete (catalogOverrides as Record<string, any>)[key];
        }
    });

    const updatesWithJsonFieldsMerged = {
        ...whiteListedUpdate,
        catalogOverrides,
    };

    return updatesWithJsonFieldsMerged;
}

export const findCatalogProductByCatalogId = async (
    catalogIds?: number[],
): Promise<CatalogProduct[]> => {
    if(_.isNil(catalogIds)) {
        return [];
    }
    return await CatalogProduct.findAll({
        attributes: defaultCatalogProductAttributes,
        where: {
            catalogId: catalogIds,
            status: {
                [Op.not]: 'DELETED'
            }
        }
    })
}

export const bulkDeleteCatalogProducts = async (
    catalogProducts: CatalogProduct[] = [],
    userInfo: UserInfo)
    : Promise<number> => {

    const catalogProductsIds = _.map(catalogProducts, "id");

    await CatalogProduct.update({
        status:CatalogProductStatus.DELETED
    }, {
        where: {
            id: catalogProductsIds
        }
    });

    const recordChanges = catalogProducts.map((oldCatalogProduct) => {
        const values = oldCatalogProduct.toJSON();
       const set = {
           catalogProductId:  oldCatalogProduct.id,
           actionType: DELETE,
           newCatalogProduct: { ...values, status: CatalogProductStatus.DELETED },
           oldCatalogProduct: values
       }

       return set;
    });

    await bulkRecordCatalogProductChanges(userInfo, recordChanges);

   return catalogProducts.length;
}

//All Updates to Catalog Products should run through this route.
/*
Logic:
 1. Get Catalog Product
 2. Whitelist update Values
 3. Update Catalog Product if it's different; skip and return original catalog product otherwise
 4. Get Catalog
 5. If Catalog has a parent catalog and status has changed, get all sibling catalogs
 6. If sibling catalogs, get all their catalog products
 7. If all sibling catalog Products statuses are "inactive" and central catalog product is "active", make central catalog product "inactive"
 8. If there is an active product in the sibling catalogs and central catalog is inactive, make central catalog "active",
 9. Record changes/history
10. Return the updated catalog product that initiated the changes.
*/
export const updateCatalogProduct = async (
    catalogId: number,
    productId: number,
    userInfo: UserInfo,
    updates: Partial<CatalogProductRecord>,
    shouldUpdateParentCatalogStatus: boolean = true
) => {
    return await transaction( async () => {
        const catalogProduct = await findCatalogProduct(productId, catalogId);

        const previousValues = catalogProduct.toJSON();

        const updatesWithJsonFieldsMerged = getMergedCatalogProductUpdates(catalogProduct, updates);

        const newCatalogProduct = new CatalogProduct({
            ...catalogProduct.get(),
            ...updatesWithJsonFieldsMerged,
        });

        if (catalogProduct.isEqual(newCatalogProduct, true)) {
            return catalogProduct;
        }

        const updateStatus = _.get(updates, 'status');

        const statusIsBeingUpdated = updateStatus != null && updateStatus !== catalogProduct.status;

        const [
            catalog,
            updatedCatalogProduct,
        ] = await Promise.all([
            findCatalogById(catalogId),
            catalogProduct.update(updatesWithJsonFieldsMerged),
        ]);

        const parentCatalogId = catalog.parentCatalogId;

        if (parentCatalogId && statusIsBeingUpdated && shouldUpdateParentCatalogStatus) {
            const siblingCatalogs = await Catalog.findAll({
                where: {
                    parentCatalogId: catalog.parentCatalogId
                }
            });

            if (_.size(siblingCatalogs)) {
                const [
                    parentCatalogProduct,
                    activeProductsCount
                ] = await Promise.all([
                    CatalogProduct.findOne({
                        where: {
                            productId,
                            catalogId: parentCatalogId,
                        }
                    }),
                    CatalogProduct.count({
                        where: {
                            productId,
                            catalogId: _.map(siblingCatalogs, 'id'),
                            status: CatalogProductStatus.ACTIVE
                        }
                    })
                ]);

                if (parentCatalogProduct == null) {
                    throw new ErrorResponse(
                        StatusCodes.NOT_FOUND,
                        `No Parent Catalog Product was found for the product id ${productId}`
                    );
                }

                const allProductsAreInactive = activeProductsCount === 0;

                //if all siblings
                if (allProductsAreInactive && parentCatalogProduct.status === CatalogProductStatus.ACTIVE) {
                    await updateCatalogProduct(parentCatalogId, productId, userInfo, {
                        status: CatalogProductStatus.DEACTIVATED
                    });
                }
                else if (!allProductsAreInactive && parentCatalogProduct.status === CatalogProductStatus.DEACTIVATED) {
                    await updateCatalogProduct(parentCatalogId, productId, userInfo, {
                        status: CatalogProductStatus.ACTIVE
                    });
                }
            }
        }

        // reloads [numeric] fields in the db standard to be consistently fed back to consumers
        await updatedCatalogProduct.reload();

        const newValues = updatedCatalogProduct.toJSON();

        const changeActionType = updatesWithJsonFieldsMerged.status === CatalogProductStatus.DEACTIVATED ? DEACTIVATE : UPDATE;

        await recordCatalogProductChange(
            catalogProduct.id,
            changeActionType,
            userInfo,
            newValues,
            previousValues,
        );

        return updatedCatalogProduct;
    });
}

export const getDistinctCatalogProductValues = async (
    attributes  : string,
    catalogId   : string,
    fields      : string,
    productIds? : string,
) => {
    return await transaction(async () => {
        let query : string = '';

        let replacements = {
            catalogId,
        } as {
            catalogId  : string,
            productIds?: Array<string>
        };

        let attributeNameQueries : Array<string> = [];
        if (attributes) {
            const attributeNames = (attributes as string).split(',');

            attributeNameQueries = attributeNames.map(attribute => `
                (
                    SELECT
                        p.id,
                        '${attribute}' AS key,
                        jsonb_array_elements_text(p.attributes-> '${attribute}') AS value
                    FROM products p
                    INNER JOIN "catalogProducts"
                        ON "catalogProducts"."productId" = p."id"
                        AND "catalogProducts"."catalogId" = :catalogId
                        AND "catalogProducts".status = 'ACTIVE'
                    UNION
                    SELECT
                        p.id,
                        '${attribute}' as key,
                        jsonb_array_elements_text(lp.attributes-> '${attribute}') AS "values"
                    FROM "products" p
                    INNER JOIN "catalogProducts"
                        ON "catalogProducts"."productId" = p."id"
                        AND "catalogProducts"."catalogId" = :catalogId
                        AND "catalogProducts".status = 'ACTIVE'
                    INNER JOIN "products" lp
                        ON p."linkedTo" = lp."id"
                )
            `)
        }

        let fieldNameQueries : Array<string> = [];
        if (fields) {
            const fieldNames = (fields as string).split(',');

            const invalidFields = _.filter(fieldNames, (fieldName) => {
                return !Boolean(fieldName in Product.rawAttributes)
            });

            if (_.size(invalidFields) > 0) {
                throw new ErrorResponse(StatusCodes.BAD_REQUEST, `Invalid field(s) ${invalidFields}`);
            }

            fieldNameQueries = fieldNames.map(field => `
                (
                    SELECT
                        p.id,
                        '${field}' AS key,
                        "${field}" AS value
                    FROM products p
                    INNER JOIN "catalogProducts"
                        ON "catalogProducts"."productId" = p."id"
                        AND "catalogProducts"."catalogId" = :catalogId
                        AND "catalogProducts".status = 'ACTIVE'
                    UNION
                    SELECT
                        p.id,
                        '${field}' as key,
                        lp."${field}" AS "values"
                    FROM "products" p
                    INNER JOIN "catalogProducts"
                        ON "catalogProducts"."productId" = p."id"
                        AND "catalogProducts"."catalogId" = :catalogId
                        AND "catalogProducts".status = 'ACTIVE'
                    INNER JOIN "products" lp
                        ON p."linkedTo" = lp."id"
                )
            `);
        }

        const unionQueries : Array<string> = _.concat(attributeNameQueries, fieldNameQueries);

        query += `
            SELECT
                "productUnions"."key",
                ARRAY_AGG(DISTINCT "productUnions"."value")
                AS "values"
            FROM (${unionQueries.join(' UNION ')}) AS "productUnions"
        `;

        if (productIds) {
            query += `
                INNER JOIN "externalReferences"
                    ON "externalReferences"."productId" = "productUnions"."id"
                    AND "externalReferences"."externalId" IN (:productIds)
            `;
            replacements.productIds = productIds.split(',');
        }

        query += `
            GROUP BY "productUnions"."key"
        `;

        const responseData = await connection.query(query, {
            model: Product,
            replacements,
        });

        return _.reduce(
            responseData,
            (result : Dictionary<string[]>, row) => {
                let key = _.get(row, 'dataValues.key');
                let values = _.get(row, 'dataValues.values');

                result[key] = values;

                return result;
            },
            {} as Dictionary<string[]>
        );
    });
}

export const getPriceTierCatalogPresence = async (
    priceTierIds: number[]
) : Promise<Record<number, number[]>> => {
    const presences = await CatalogProduct.findAll({
        where: {
            priceTierId : {
                [Op.in] : priceTierIds
            }
        }
    });

    const groupedCatalogProducts = _.groupBy(presences,'priceTierId');

    return _.mapValues(
        groupedCatalogProducts,
        (catalogProducts: CatalogProduct[]) =>
            _.uniq(catalogProducts.map(product => product.catalogId))
    );
}

type priceTierId = number;
type productId = number;

export const bulkReassignProductPriceTiers = async (
    productsToReassign: Record<priceTierId, productId[]>,
    orgCatalogId: number
) => {
    const childCatalogIds = await findChildCatalogIds(String(orgCatalogId));

    for (const tierId in productsToReassign) {
        const productIds = productsToReassign[tierId];
        const priceTierId = Number(tierId);

        if (_.isNaN(priceTierId) || priceTierId < 0 || productIds === null) {
            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                'Invalid priceTierId or catalogProductIds'
            );
        }

        await CatalogProduct.update({ priceTierId } , {
            where: {
                productId: {
                    [Op.in] : productIds
                },
                catalogId: {
                    [Op.in] : [orgCatalogId, ...childCatalogIds]
                }
            }
        })
    }
}

export const getProductsAssignedToPriceTier = async(
    priceTierId: number,
    orgCatalogId: number
): Promise<Product[]> => {
    const childCatalogIds = await findChildCatalogIds(String(orgCatalogId));

    const catalogProducts = await CatalogProduct.findAll({
        where: {
            priceTierId,
            catalogId: {
                [Op.in] : [orgCatalogId, ...childCatalogIds]
            },
            status: {
                [Op.not]: CatalogProductStatus.DELETED,
            }
        },
        include: [{ model: Product }]
    });

    return _.chain(catalogProducts)
        .map(catalogProduct => catalogProduct.product)
        .uniqBy(product => product.id)
        .value();
}

export const getProductIdWithCatalogIdAndExternalReference = async (catalogId : number, externalId : string) : Promise<number> => {

    const catalogProducts : CatalogProduct[] = await connection.query(
        `SELECT "catalogProducts"."productId"
            FROM "catalogProducts"
            JOIN "externalReferences"
                ON "externalReferences"."productId" = "catalogProducts"."productId"
                AND "externalReferences"."type" = :type
            WHERE "catalogProducts"."catalogId" = :catalogId
                AND "catalogProducts"."status" IN (:statuses)
                AND "externalReferences"."externalId" = :externalId;
        `,
        {
            model: CatalogProduct,
            replacements: {
                catalogId,
                externalId,
                statuses    : [CatalogProductStatus.ACTIVE, CatalogProductStatus.DEACTIVATED],
                type        : ExternalReferenceType.SELL_TREEZ_ID,
            }
        },
    );

    if (catalogProducts.length <= 0) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `No product is found with ${externalId} in this catalog.`
        );
    }

    if (catalogProducts.length > 1) {
        throw new ErrorResponse(
            StatusCodes.CONFLICT,
            `More than one product is found with ${externalId} in this catalog.`
        );
    }

    return catalogProducts[0].productId;
}

export const deleteCatalogProduct = async (
    catalogId : number,
    productId : number,
    userInfo: UserInfo,
) => {
    const catalogProduct = await findCatalogProduct(productId, catalogId);

    const oldCatalogProduct = { ...catalogProduct.get() } as CatalogProduct;

    await catalogProduct.set({
        status: CatalogProductStatus.DELETED
    }).save();

    await recordCatalogProductChange(
        catalogProduct.id,
        DELETE,
        userInfo,
        catalogProduct,
        oldCatalogProduct,
    );
};

export const deleteUnusedProductsFromOrgCatalog = async (
    orgCatalog : Catalog,
) => {
    const catalogList = await Catalog.findAll({
        attributes: ['id'],
        where: {
            parentCatalogId: orgCatalog.id
        }
    });
    const catalogIdlist = catalogList.map(obj => obj.id);

    const productListStore = await CatalogProduct.findAll({
        attributes: ['id','productId','catalogId'],
        where: {
            catalogId: catalogIdlist,
            status: CatalogProductStatus.ACTIVE
        },
    });

    const productListOrg = await CatalogProduct.findAll({
        attributes: ['id','productId','catalogId'],
        where: {
            catalogId: orgCatalog.id,
            status: CatalogProductStatus.ACTIVE
        },
    });

    const productIdListStore = productListStore.map(obj => obj.productId);
    const productIdListOrg = productListOrg.map(obj => obj.productId);

    const storeProductIds = new Set(productIdListStore);
    const productListToDelete = productIdListOrg.filter(x => !storeProductIds.has(x));

    const updated = await CatalogProduct.update({
        status: CatalogProductStatus.DELETED
    }, {
        where: {
            productId: {
            [Op.in]: Array.from(productListToDelete)
            },
            catalogId: orgCatalog.id
        }
    });
    if (!updated) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Could not delete products with ids ${productListToDelete}`
        )
    }else{
        return productListToDelete;
    }
};

export const deleteUnusedProductsFromStoreCatalog = async (
    storeCatalog : Catalog,
) => {
    const childCatalogList = await Catalog.findAll({
        attributes: ['id', 'parentCatalogId'],
        where: {
            id: {
                [Op.not]: storeCatalog.id
            },
            parentCatalogId: storeCatalog.parentCatalogId
        }
    });
    const childCatalogIdlist = childCatalogList.map(childCatalog => childCatalog.id);

    const storeProductList = await CatalogProduct.findAll({
        attributes: ['productId'],
        where: {
            catalogId: storeCatalog.id,
            status: CatalogProductStatus.ACTIVE
        },
        include: [{
            model: Product,
            attributes: [],
            where: {
                ownerId: storeCatalog.ownerId,
                ownerType: OwnerType.STORE
            }
        }]
    });
    const storeProductIdList = storeProductList.map(storeProduct => storeProduct.productId);

    const childStoresCatalogProductList = await CatalogProduct.findAll({
        attributes: ['productId'],
        where: {
            productId: {
                [Op.in]: storeProductIdList
            },
            catalogId: {
                [Op.in]: childCatalogIdlist
            },
            status: CatalogProductStatus.ACTIVE
        },
    });
    const childStoresCatalogProductIdSet = new Set(childStoresCatalogProductList.map(storeCatalogProduct => storeCatalogProduct.productId));
    const storeProductsToDeleteSet = new Set(
        [...storeProductIdList].filter(storeProductId => !childStoresCatalogProductIdSet.has(storeProductId))
    );

    let updated = await CatalogProduct.update({
        status: CatalogProductStatus.DELETED
    }, {
        where: {
            productId:{
                [Op.in]: Array.from(storeProductsToDeleteSet)
            },
            catalogId: [storeCatalog.parentCatalogId, storeCatalog.id],
        }
    });
    if (updated === null || updated === undefined) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Could not delete unused products of the store with id ${storeCatalog.id}`
        )
    }
}
