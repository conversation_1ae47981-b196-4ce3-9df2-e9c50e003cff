import { OwnerType }                    from '@treez/commons/sharedTypings/product';
import { stringField }                  from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';
import _                                from 'lodash';

const CATALOG_TABLE = 'catalogs';
const PRODUCT_TABLE = 'products';
const UNIQUE_CONSTRAINT = 'brandId_sku_unique_constraint';

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.sequelize.query(`ALTER TABLE ${PRODUCT_TABLE} DROP CONSTRAINT IF EXISTS ${UNIQUE_CONSTRAINT}`);
        await queryInterface.removeColumn(PRODUCT_TABLE, 'brandId');
        await queryInterface.changeColumn(PRODUCT_TABLE, 'ownerType', {
            ...stringField,
            allowNull: false,
            values: _.values(OwnerType),
        });
        await queryInterface.changeColumn(CATALOG_TABLE, 'ownerType', {
            ...stringField,
            allowNull: false,
            values: _.values(OwnerType),
        });
        await queryInterface.changeColumn(PRODUCT_TABLE, 'sku', {
            ...stringField,
            allowNull: true,
        });
        // The ORM knew type wasn't nullable, but the initial migration missed it.
        await queryInterface.changeColumn(PRODUCT_TABLE, 'type', {
            ...stringField,
            allowNull: false,
        });
    },

    /**
     * Note: As brandId is removed, replacing it will result in empty or default
     * values, so the constraint can't be reapplied.  This is not a fully reversable
     * migration.
     * */
    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.changeColumn(PRODUCT_TABLE, 'sku', {
            ...stringField,
            allowNull: false,
        });
        await queryInterface.changeColumn(CATALOG_TABLE, 'ownerType', {
            ...stringField,
            allowNull: false,
            defaultValue: OwnerType.STORE,
            values: _.values(OwnerType),
        });
        await queryInterface.changeColumn(PRODUCT_TABLE, 'ownerType', {
            ...stringField,
            allowNull: false,
            defaultValue: OwnerType.BRAND,
            values: _.values(OwnerType),
        });
        await queryInterface.addColumn(PRODUCT_TABLE, 'brandId', stringField);
    },
}
