import {
    Response,
    NextFunction,
}                               from 'express';
import {
    AuthenticatedRequest,
    Requestor,
}                               from "@treez/dev-pack/auth";
import {
    ReasonPhrases,
    StatusCodes,
}                               from 'http-status-codes';
import { ErrorResponse }        from '@treez/dev-pack/errors';

export const checkRequestorIsTreez = (requestor: Requestor | null): boolean => requestor?.isTreez() ?? false;

export const requestorIsTreez = (
    request: AuthenticatedRequest,
    response: Response,
    next: NextFunction,
) => {
    const {requestor} = request;

    return checkRequestorIsTreez(requestor)
    ? next()
    : next(new ErrorResponse(StatusCodes.FORBIDDEN, ReasonPhrases.FORBIDDEN))
}

export default requestorIsTreez;
