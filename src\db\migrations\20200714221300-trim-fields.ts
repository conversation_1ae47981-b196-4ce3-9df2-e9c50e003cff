import { QueryInterface }   from 'sequelize';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET "brandName" = TRIM("brandName"),
                "displayName" = TRIM("displayName"),
                "eCommerceName" = TRIM("eCommerceName"),
                "name" = TRIM("name")
        `);
    },

    down: async function(queryInterface: QueryInterface) {},
};