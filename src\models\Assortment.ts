import { StatusCodes }                  from 'http-status-codes';
import _                                from 'lodash';
import { ValidationOptions } from 'sequelize/types/instance-validator';
import {
    AutoIncrement,
    Column,
    Default,
    PrimaryKey,
    Table,
    ForeignKey,
    BelongsTo,
}                                       from 'sequelize-typescript';
import {
    enumField,
    notNull,
    stringField,
    booleanField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { ErrorResponse }                from '@treez/dev-pack/errors';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import Catalog                          from './Catalog';
import logger                           from '@treez/dev-pack/logger';

export enum AssortmentType {
    COLLECTION    = 'COLLECTION',
    PRODUCT_LINE  = 'PRODUCT_LINE',
}

export enum AssortmentStatus {
    ACTIVE    = 'ACTIVE',
    INACTIVE  = 'INACTIVE'
}

@Table({tableName: 'assortments'})
export default class Assortment extends TreezModel<Assortment> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                             number;

    @ForeignKey(() => Catalog)
    @Column
    catalogId:                      number;

    @BelongsTo(() => Catalog)
    catalog:                        Catalog;

    @Column(notNull(enumField(AssortmentType)))
    type:                           AssortmentType;

    @Column(notNull(stringField))
    name:                           string;

    @Column(stringField)
    icon:                           string;

    @Column(notNull(stringField))
    order:                          number;

    @Default(false)
    @Column(booleanField)
    principal:                      boolean | null;

    @Default(AssortmentStatus.ACTIVE)
    @Column(notNull(enumField(AssortmentStatus)))
    status:                         AssortmentStatus;

    static defaultAttributes = [
        'id',
        'catalogId',
        'type',
        'name',
        'icon',
        'order',
        'status'
    ];

    static validPatchFields = [
        'name',
        'icon',
        'order'
    ];

    static saveAssortment = async (
        assortment: Partial<Assortment>,
        catalogId: number
    ) => {
        return await Assortment.create({
            ..._.pick(assortment, Assortment.defaultAttributes),
            catalogId
        });
    }

    static updateAssortment = async (
        assortmentId: number,
        catalogId: number,
        updates: Partial<Assortment>
    ) => {
        const assortment = await Assortment.findAssortmentById(assortmentId, catalogId);

        return await assortment.set(
            _.pick(updates, Assortment.validPatchFields)
        ).save();
    }

    static bulkUpdateAssortments = async (
        assortments: Partial<Assortment>[],
        catalogId: number
    ) => {
        return await Promise.all(assortments.map(async (assortment) => {
            try {
                await Assortment.updateAssortment(Number(assortment.id), catalogId, assortment)
            } catch (e) {
                logger.error("Error bulk updating assortments: ", e);
                throw e;
            }
        }));
    }

    static findAssortmentById = async (
        assortmentId: number,
        catalogId: number
    ) => {
        try {
            const assortment = await Assortment.findOne({
                where: {
                    id: assortmentId,
                    catalogId
                }
            });

            if (assortment == null) {
                throw new ErrorResponse(
                    StatusCodes.NOT_FOUND,
                    `Assortment with the id of ${assortmentId} could not be found`
                )
            }

            return assortment;
        } catch (e) {
            logger.error("Error finding assortment by id: ", e);
            throw e;
        }
    }

    static removeAssortment = async (
        assortmentId: number,
        catalogId: number
    ) => {
        const assortment = await Assortment.findAssortmentById(assortmentId, catalogId);

        return await assortment.destroy();
    }

    public validate(options?: ValidationOptions) {
        if (!_.includes(Object.values(AssortmentType), this.type)) {
            throw new ErrorResponse(
                    StatusCodes.UNPROCESSABLE_ENTITY,
                    `Invalid enum value "${this.type}" for assortmentType!`
                );
        } else if (!_.includes(Object.values(AssortmentStatus), this.status)) {
            throw new ErrorResponse(
                    StatusCodes.UNPROCESSABLE_ENTITY,
                    `Invalid enum value "${this.status}" for assortmentStatus!`
                );
        }
        else {
            return super.validate(options);
        }
    }
}
