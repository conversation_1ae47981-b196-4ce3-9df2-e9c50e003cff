{
    db: {
        host:      'product-db-demo.treez.io',
        name:      'product-db-demo',
        namespace: 'product-db-demo',
        password:  'ssm:/apps/product/envs/demo/db/product-demo/password',
        username:  'product-demo',
    },
    logger: {
        name: 'product-api-demo',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serviceName: 'product-api-demo',
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        topic: {
            productUpdate: 'persistent://product-api-demo/product/productUpdate'
        }
    }
}
