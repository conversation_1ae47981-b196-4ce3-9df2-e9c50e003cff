{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Program",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "env": {
                "NODE_PATH": "${workspaceFolder}/dist"
            },
            "program": "${workspaceFolder}/dist/server.js",
            "preLaunchTask": "tsc: build - tsconfig.json",
            "outFiles": [
                "${workspaceFolder}/dist/**/*.js"
            ],
            "outputCapture": "std"
        },
        {
            "name": "Debug Jest Tests",
            "type": "node",
            "request": "launch",
            "runtimeArgs": [
                "--inspect-brk",
                "${workspaceRoot}/node_modules/.bin/jest",
                "--config=jest.config.js",
                "--runInBand",
                "${file}"
            ],
            "env": {
                "NODE_ENV": "test"
            },
            /*
             * uncomment to build source before debugging, at the expense of breaking vscode source map
             */
            // "preLaunchTask": "tsc: build - tsconfig.json",
            // "outFiles": [
            //     "${workspaceFolder}/dist/**/*.js"
            // ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen"
        }
    ]
}
