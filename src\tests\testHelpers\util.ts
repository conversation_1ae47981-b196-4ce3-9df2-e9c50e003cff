import {
    RoleWithPermissions,
    User,
}                                       from '@treez/dev-pack/auth';
import * as permissionsFunctions        from '@treez/dev-pack/auth/permissions';
import * as isTreezMiddleware           from '../../middleware/requestorIsTreez';
import { resolveUserSpy }               from '../routes/testApp';
import * as pulsarProducer              from '../../pulsar/producer';

export const sleep = (millis: number) => {
    return new Promise((resolve) => {
        setTimeout(resolve, millis);
    });
};

export const hasPermissionsSpy = jest.spyOn(permissionsFunctions, 'hasPermissions');
export const getUserPermissionsSpy = jest.spyOn(permissionsFunctions, 'getPermissionsFromJwt');
export const hasTreezAccessSpy = jest.spyOn(isTreezMiddleware, 'checkRequestorIsTreez');
export const sendProductUpdateMessageSpy = jest.spyOn(pulsarProducer, 'sendProductUpdateMessage');

export const unmockPermissionsCheckAndGiveUserPermissions = (userPermissions: RoleWithPermissions[]) => {
    hasPermissionsSpy.mockReset();

    getUserPermissionsSpy.mockReset();

    getUserPermissionsSpy.mockImplementation(() =>userPermissions);
}

export const mockTreezAccess = () => hasTreezAccessSpy.mockImplementation(() => true);

export const mockUserResolution = (user: User) => {
    resolveUserSpy.mockImplementation(async () => user)
}
