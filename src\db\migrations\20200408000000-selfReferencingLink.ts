import { QueryInterface }               from 'sequelize';
import {
    jsonField,
    foreignKey,
    intField,
}                                       from '@treez/dev-pack/db/fieldTypes';

const products         = 'products';
const linkedTo         = 'linkedTo';
const productOverrides = 'productOverrides';
const catalogOverrides = 'catalogOverrides';
const catalogProducts  = 'catalogProducts';
const overrides        = 'overrides';
const originalId       = 'originalId'

export = {
    up: async function( queryInterface: QueryInterface ) {
        await queryInterface.addColumn(products, linkedTo, foreignKey(intField, products, 'id'));
        await queryInterface.addColumn(products, productOverrides, jsonField);
        await queryInterface.renameColumn(catalogProducts, overrides, catalogOverrides);
        await queryInterface.removeColumn(catalogProducts, originalId)
    },
    down: async function( queryInterface: QueryInterface ) {
        await queryInterface.addColumn(catalogProducts, originalId, intField)
        await queryInterface.renameColumn(catalogProducts, catalogOverrides, overrides);
        await queryInterface.removeColumn(products, productOverrides);
        await queryInterface.removeColumn(products, linkedTo);
    }
};
