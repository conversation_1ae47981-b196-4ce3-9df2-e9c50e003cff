import _ from 'lodash';
import { QueryInterface } from 'sequelize';

const productChanges = 'productChanges';
const catalogProductChanges = 'catalogProductChanges';

const productConstantsMap = {
    CREATE  : 'Created Product',
    DELETE  : 'Deleted Product',
    LINK    : 'Linked Product',
    MERGE   : 'Merged Product',
    UNLINK  : 'Unlinked Product',
    UPDATE  : 'Updated Product',
};

const catalogProductConstantsMap = {
    CREATE      : 'Added to Catalog',
    DEACTIVATE  : 'Made Inactive in Catalog',
    DELETE      : 'Removed from Catalog',
    UPDATE      : 'Updated',
};

const mapConstantsToUpdates = (
    queryInterface: QueryInterface,
    tableName: string,
    constantMap: Record<string, string>,
) => Object.entries(constantMap)
    .map(([newConstant, oldValue]) => queryInterface.bulkUpdate(
        tableName,
        { actionType: newConstant },
        { actionType: oldValue },
    ));

const mapConstantsRollbackUpdates = (
    queryInterface: QueryInterface,
    tableName: string,
    constantMap: Record<string, string>,
) => Object.entries(constantMap)
    .map(([newConstant, valueToRevertTo]) => queryInterface.bulkUpdate(
        tableName,
        { actionType: valueToRevertTo },
        { actionType: newConstant },
    ));

export = {
    up: function (queryInterface: QueryInterface) {
        return Promise.all([
            ...mapConstantsToUpdates(queryInterface, productChanges, productConstantsMap),
            ...mapConstantsToUpdates(queryInterface, catalogProductChanges, catalogProductConstantsMap),
        ]);
    },

    down: function (queryInterface: QueryInterface) {
        return Promise.all([
            ...mapConstantsRollbackUpdates(queryInterface, productChanges, productConstantsMap),
            ...mapConstantsRollbackUpdates(queryInterface, catalogProductChanges, catalogProductConstantsMap),
        ]);
    },
}
