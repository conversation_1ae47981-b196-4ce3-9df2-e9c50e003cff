import { QueryInterface }             from 'sequelize';
import { Sequelize }                  from 'sequelize-typescript';

const capabilities  = 'capabilities';
const type          = 'type';
const subtype       = 'subtype';
const capability    = 'capability';
const rules         = 'rules';
const updatedAt     = 'updatedAt';
const currentRules  = '"EXTRACT":["ISOLATE","RSO","FSO","DIAMONDS","CAVIAR","MOON ROCKS","POWDER HASH","SORBET","TEMPLE BALL","BADDER","BUDDER","CRUMBLE","CRYSTALLINE","CURED RESIN","DISTILLATE","DRY SIFT","HASH","HASH ROSIN","ICE WATER HASH","ICE WAX","JELLY","LIVE RESIN","LIVE RESIN BADDER","LIVE RESIN BUDDER","LIVE RESIN SUGAR","LIVE ROSIN","<PERSON><PERSON>","<PERSON>TH<PERSON>","<PERSON><PERSON><PERSON> BUDDER","SAUC<PERSON>","SHATTE<PERSON>","SIFT ROSIN","SUGA<PERSON>","THC-A","WAX","HASH ROSIN"]';
const newRules      = `{"toTypes": {${currentRules}, "CARTRIDGE": ["READY TO USE","POD","SAUCE","510 THREAD","DIAMOND","DIAMOND LINE","GEM","GEM LINE","PAX","PAX POD","LIVE RESIN","DISTILLATE","CURED RESIN","HASH"]}}`;

export = {
    up: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        await queryInterface.sequelize.query(
            `UPDATE "${capabilities}" SET "${rules}" = '${newRules}', "${updatedAt}" = NOW()
                 WHERE "${type}" = 'EXTRACT' AND "${subtype}" = 'BULK EXTRACT' AND "${capability}" = 'packaging'`
        );
    },

    down: async (queryInterface: QueryInterface, sequelize: Sequelize) => {
        await queryInterface.sequelize.query(
            `UPDATE "${capabilities}" SET "${rules}" = '{"toTypes:" {${currentRules}}}'
                 WHERE "${type}" = 'EXTRACT' AND "${subtype}" = 'BULK EXTRACT' AND "${capability}" = 'packaging'`
        );
    }
}
