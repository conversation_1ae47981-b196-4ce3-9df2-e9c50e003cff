import { ErrorResponse } from '@treez/dev-pack/errors';
import { StatusCodes } from 'http-status-codes';
import _            from 'lodash';

/**
 * Determines if a value is `null`, `undefined`, an empty array or an object without enumerable keys.
 * @param value value to check
 */
export const isNilOrEmptyObject = (value?: any): value is null | undefined | { length: 0 } | {} => {
    return _.isNil(value) || (_.isObject(value) && _.isEmpty(value));
};

/**
 * Determines if a value is `null`, `undefined`, an empty string, an empty array or an object without enumerable keys.
 * @param value value to check
 */
export const isNilOrEmpty = (value?: any): value is null | undefined | { length: 0 } | {} | '' => {
    return isNilOrEmptyObject(value) || (_.isString(value) && !value);
}


/**
 * throws and exception if all not null
 * @param objects objects to evaluate
 * @param message message in the exception
*/
export function assertAllNotNull (objects: unknown[] = [], message: string): asserts objects  {
    if (objects.every(_.isNull)) {
        throw new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            message
        );
    }
}

/**
 * throws and exception if all not null and empty
 * @param objects objects to evaluate
 * @param message message in the exception
*/
export function assertAllNotNil (objects: unknown[] = [], message: string): asserts objects is NonNullable<unknown>[] {
    if (objects.findIndex(_.isNil) !== -1) {
        throw new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            message
        );
    }
}

export const assertNotBlank = (str: string, message: string) => {
    if(_.isNil(str) || str.trim().length === 0) {
        throw new ErrorResponse(
            StatusCodes.UNPROCESSABLE_ENTITY,
            message
        );
    }
}

export const assertAllNotNaN = (numbers: number[], message: string) => {
    if(numbers.every(e => !Number.isNaN(e))) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            message
        )
    }
}

export const assertNotNaN = (number: number, message: string) => {
    if(Number.isNaN(number)) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            message
        );
    }
}


export function assertNotNull (val: unknown, message: string): asserts val {
    if(_.isNull(val)) {
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            message
        )
    }
}