import _                                from 'lodash';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';

const BRAND_NAME = 'brandName';
const BRAND_NAME_INDEX = 'products_brand_name';
const CLASSIFICATION = 'classification';
const CLASSIFICATION_INDEX = 'products_classification';
const TYPE = 'type';
const TYPE_INDEX = 'products_type';
const PRODUCTS = 'products';


export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addIndex(PRODUCTS, [BRAND_NAME]);
        await queryInterface.addIndex(PRODUCTS, [CLASSIFICATION]);
        await queryInterface.addIndex(PRODUCTS, [TYPE]);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeIndex(PRODUCTS, BRAND_NAME_INDEX);
        await queryInterface.removeIndex(PRODUCTS, CLASSIFICATION_INDEX);
        await queryInterface.removeIndex(PRODUCTS, TYPE_INDEX);
    },
}
