import { QueryInterface }   from 'sequelize';
import { stringField }      from '@treez/dev-pack/db/fieldTypes';

const description        = 'description';
const externalReferences = "externalReferences";

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.addColumn(externalReferences, description, stringField);
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.removeColumn(externalReferences, description);
    },
}
