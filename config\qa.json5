{
    db: {
        name:      'product-db-qa',
        namespace: 'product-db-qa',
        password:  'ssm:/apps/product/envs/qa/db/product-qa/password',
        replication: {
            read: [
                {
                    host: 'product-db-qa-ro.treez.io',
                },
            ],
            write: {
                host: 'product-db-qa.treez.io',
            },
        },
        username:  'product-qa',
    },
    logger: {
        name: 'product-api-qa',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    serviceName: 'product-api-qa',
                    logLevel: 'trace'
                },
                enabled: true,
            },
        },
    },
    pulsar: {
        topic: {
            productUpdate: 'persistent://product-api-qa/product/productUpdate'
        }
    }
}
