import _                            from 'lodash';
import {
    UOM,
    ProductType,
    OwnerType,
}                                   from '@treez/commons/sharedTypings/product';
import { ProductAction }            from '../../lib/sharedInterfaces';
import Product, {
    createProduct,
    updateProduct,
    createProductForCatalog,
    ProductInCatalog,
}                                   from '../../models/Product';
import ProductChange                from '../../models/ProductChange';
import Catalog                      from '../../models/Catalog';
import { addProductToCatalog }      from '../../models/CatalogProduct';
import SuggestedLink, {
    MatchType
}                                   from '../../models/SuggestedLink';
import { sleep }                    from '../../tests/testHelpers/util';

const {
    BRAND,
    ORG,
    STORE,
} = OwnerType;

const brandProduct = {
    id:                 123,
    amount:             1.000000,
    barcodes:           [{sku: "SKUXOZEHXKS"}],
    brandName:          "Brand Tester",
    eCommerceName:      "<PERSON>OMG",
    externalId:         "8675309",
    name:               "ZOMGWHY?!",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
    packageTracked:     false,
    cannabis:           true,
    msrp:               25.000000,
    size:               "50 piece bucket",
    sku:                "SKUXOZEHXKSJ",
    subtype:            "Utter Nonsense",
    type:               ProductType.BEVERAGE,
    uom:                UOM.EACH,
}

const brandCatalog = {
    name:               "Test Brand Catalog",
    ownerId:            7224,
    ownerType:          OwnerType.BRAND,
}

const testUser = {
    userAuthId: '<EMAIL>',
};

describe('Product model', () => {
    it('Can tell if products are identical with isEqual.', async () => {
        const product1 = await Product.build(brandProduct).save();

        expect(product1.isEqual(product1)).toBe(true);
    });

    it('Can tell if products are different with isEqual.', async () => {
        const product1 = await Product.build(brandProduct).save();
        const product2 = _.merge(new Product(), product1, {
            amount: "1.000000",
            msrp:   "24.000000",
        });

        expect(product1.isEqual(product2)).toBe(false);
    });

    it('isEqual correctly parses numbers and correctly determines equality', async () => {
        const product1 = await Product.build(brandProduct).save();
        const product2 = _.merge(new Product(), product1, {
            amount: 1,
            msrp:   25,
        });

        expect(product1.isEqual(product2)).toBe(true);
    });

    it('isEqual correct parses numbers and correctly determines inequality', async () => {
        const product1 = await Product.build(brandProduct).save();
        const product2 = _.merge(new Product(), product1, {
            amount: 1,
            msrp:   24,
        });

        expect(product1.isEqual(product2)).toBe(false);
    });

    it('isEqual correctly determines equality with instance cloned using spread operator', async () => {
        const product1 = await Product.build(brandProduct).save();
        const product2 = new Product({
            ...product1.get(),
        });

        expect(product1.isEqual(product2)).toBe(true);
    });

    it('isEqual ignores attributes order in comparison', async () => {
        const redundantChanges = {
            details: {
                strain: 'A Wild One',
                extractionMethod: 'Kindly Asking',
            },
            eCommerceName: 'Same Same',
        } as Partial<Product>;
        const originalProduct = await Product.build({
            ...brandProduct,
            ...redundantChanges,
        }).save();

        const sameSameButDifferentProduct = _.merge(new Product(), originalProduct, {
            eCommerceName: 'Same Same',
            details: {
                strain: 'A Wild One',
            },
        });

        expect(originalProduct.isEqual(sameSameButDifferentProduct)).toBe(true);
    });

    it(`isEqual correctly detects null and empty objects as different`, async () => {
        const originalProduct = await Product.build({
            ...brandProduct,
            attributes: null,
        }).save();

        const newProduct = _.merge(new Product(), originalProduct, {
            attributes: {},
        });

        expect(originalProduct.isEqual(newProduct)).toBe(false);
    });

    it(`isEqual correctly detects null and empty objects as equal`, async () => {
        const originalProduct = await Product.build({
            ...brandProduct,
            attributes: null,
        }).save();

        const newProduct = _.merge(new Product(), originalProduct, {
            attributes: {},
        });

        expect(originalProduct.isEqual(newProduct, true)).toBe(true);
    });

    it('createProductForCatalog records product creation.', async () => {
        const catalog = await Catalog.create(brandCatalog);
        const product = await createProductForCatalog(
            catalog.id,
            brandProduct,
            testUser,
            false,
        ) as Partial<ProductInCatalog>;

        const productId = product.id as number;

        const productChange = await ProductChange.findOne({
            where: {
                productId,
                version: 1,
            }
        }) as ProductChange;

        expect(productChange).not.toBeNull();
        expect(productChange.actionType).toBe(ProductAction.CREATE);
        expect(productChange.productId).toEqual(product.id);
        expect(productChange.changes).toHaveLength(0);
        expect(productChange.oldProduct).toBeNull();
        // other attributes are modified right after creation so we can't compare this recorded change alone with the resulting product
        expect(productChange.newProduct).toMatchObject({
            id:                 productId,
            brandName:          "Brand Tester",
            eCommerceName:      "ZOMG",
            externalId:         "8675309",
            name:               "ZOMGWHY?!",
            ownerId:            7224,
            ownerType:          OwnerType.BRAND,
        });
    });

    it('createProductForEpm records product creation.', async () => {
        const catalog = await Catalog.create(brandCatalog);
        const product = await createProductForCatalog(
            catalog.id,
            brandProduct,
            testUser,
            true,
        ) as Partial<ProductInCatalog>;

        const productId = product.id as number;

        const productChange = await ProductChange.findOne({
            where: {
                productId,
                version: 1,
            }
        }) as ProductChange;

        expect(productChange).not.toBeNull();
        expect(productChange.actionType).toBe(ProductAction.CREATE);
        expect(productChange.productId).toEqual(product.id);
        expect(productChange.changes).toHaveLength(0);
        expect(productChange.oldProduct).toBeNull();
        // other attributes are modified right after creation so we can't compare this recorded change alone with the resulting product
        expect(productChange.newProduct).toMatchObject({
            id: productId,
            brandName: "Brand Tester",
            eCommerceName: "ZOMG",
            externalId: "8675309",
            name: "ZOMGWHY?!",
            ownerId: 7224,
            ownerType: OwnerType.ORG,
        });
    });

    it('createProduct records product creation.', async () => {
        const product = await createProduct(
            brandProduct,
            testUser,
        );

        const productChange = await ProductChange.findOne({
            where: {
                productId: product.id
            }
        }) as ProductChange;

        expect(productChange).not.toBeNull();
        expect(productChange.productId).toEqual(product.id);
        expect(productChange.actionType).toBe(ProductAction.CREATE);
        expect(productChange.changes).toHaveLength(0);
        const normalizedProduct = JSON.parse(JSON.stringify(product));
        expect(productChange.newProduct).toMatchObject(normalizedProduct);
    });

    it('updateProduct records product changes.', async () => {
        const product = await Product.build(brandProduct).save();
        const updates = {
            msrp: 20.0005,
            name: 'BECOZi',
        };

        await updateProduct(
            product.id,
            updates,
            testUser,
        );

        const productChange = await ProductChange.findOne({
            where: {
                productId: product.id,
                actionType: ProductAction.UPDATE,
            }
        }) as ProductChange;

        expect(productChange).not.toBeNull();
        const {changes} = productChange;
        expect(changes).toHaveLength(3);

        const msrpChange = (changes as PropertyChange[]).find(c => c.property === 'msrp');
        expect(msrpChange).toMatchObject({
            property: 'msrp',
            oldValue: '25.000000',
            newValue: '20.000500',
            description: 'MSRP changed from 25 to 20.0005',
        });

        const nameChange = (changes as PropertyChange[]).find(c => c.property === 'name');
        expect(nameChange).toMatchObject({
            property: 'name',
            oldValue: 'ZOMGWHY?!',
            newValue: 'BECOZi',
            description: `Name changed from 'ZOMGWHY?!' to 'BECOZi'`,
        });

        const vectorChange = (changes as PropertyChange[]).find(c => c.property === 'searchVector');
        expect(vectorChange).toBeDefined();
    });

    it('correctly versions changes in a product', async () => {
        const product = await createProduct(brandProduct, { userAuthId: '<EMAIL>' });

        const updates1 = {
            msrp: 20,
        };

        await updateProduct(product.id, updates1, { userAuthId: '<EMAIL>' });

        const updates2 = {
            eCommerceName: 'Plz Buy',
            size: 'Very Sizeable',
            subtype: 'Even More Nonsense',
        };

        await updateProduct(product.id, updates2, { userAuthId: '<EMAIL>' });

        const recordedChanges = await ProductChange.findAll({
            where: {
                productId: product.id,
            },
            order: [['createdAt', 'ASC']]
        });

        expect(recordedChanges).toHaveLength(3);

        expect(recordedChanges[0].version).toBe(1);
        expect(recordedChanges[0].actionType).toBe(ProductAction.CREATE);
        expect(recordedChanges[0].userAuthId).toBe('<EMAIL>');

        expect(recordedChanges[1].version).toBe(2);
        expect(recordedChanges[1].actionType).toBe(ProductAction.UPDATE);
        expect(recordedChanges[1].userAuthId).toBe('<EMAIL>');

        expect(recordedChanges[2].version).toBe(3);
        expect(recordedChanges[2].actionType).toBe(ProductAction.UPDATE);
        expect(recordedChanges[2].userAuthId).toBe('<EMAIL>');

        expect(recordedChanges[2].oldProduct).toMatchObject({
            brandName: 'Brand Tester',
            eCommerceName: 'ZOMG',
            msrp: '20.000000',
            size: '50 piece bucket',
            subtype: 'Utter Nonsense',
        });
        expect(recordedChanges[2].newProduct).toMatchObject({
            brandName: 'Brand Tester',
            eCommerceName: 'Plz Buy',
            msrp: '20.000000',
            size: 'Very Sizeable',
            subtype: 'Even More Nonsense',
        });
    });

    it('removes an image with updateProduct', async () => {
        const brandProductWithImages = {
            images: [
                {
                    url: "example.com",
                    default: true
                },
                {
                    url: "example.com",
                    default: false
                }
            ],
            ...brandProduct,
        }
        const product = await Product.create(brandProductWithImages);
        const updates = {
            images: [
                {
                    url: "example.com",
                    default: true
                }
            ],
            ...brandProduct
        };

        await updateProduct(
            product.id,
            updates,
            testUser,
        );

        const updatedProduct = await Product.findByPk(product.id);
        expect(_.get(updatedProduct, "images")).toEqual(updates.images);
    });

    it('validates product attributes with create product.', async () => {

        const brandProductWithAttributes = {
            attributes: {
                effects: ['E1', 'E2'],
            },
            ...brandProduct,
        }
        let caught;
        try {
            await Product.create(brandProductWithAttributes);
        }
        catch (error) {
            caught = error;
        }

        expect(caught.message).toBe(`Validation error: Product attributes were an incorrect format data should be equal to one of the allowed values, data property name 'effects' is invalid`);
    });

    it('Trims spaces before create', async () => {
        const product = {
            ...brandProduct,
            brandName:     " Hastings' Herb ",
            displayName:   "   Wade's Wonder Weed  ",
            eCommerceName: "   Wade's Wonder Weed  ",
            name:          "   Wade's Wonder Weed  ",
        };

        const createdProduct = await Product.build(product).save();

        expect(_.get(createdProduct, 'brandName')).toEqual("Hastings' Herb");
        expect(_.get(createdProduct, 'displayName')).toEqual("Wade's Wonder Weed");
        expect(_.get(createdProduct, 'eCommerceName')).toEqual("Wade's Wonder Weed");
        expect(_.get(createdProduct, 'name')).toEqual("Wade's Wonder Weed");
    });

    it('Trims spaces before update', async () => {
        const createdProduct = await createProduct(
            brandProduct,
            testUser,
        );

        const updates = {
            brandName:     " Hastings' Herb ",
            displayName:   "   Wade's Wonder Weed  ",
            eCommerceName: "   Wade's Wonder Weed  ",
            name:          "   Wade's Wonder Weed  ",
        };

        await updateProduct(
            createdProduct.id,
            updates,
            testUser,
        );

        const updatedProduct = await Product.findOne({
            where: {
                id: createdProduct.id
            }
        });

        expect(_.get(updatedProduct, 'brandName')).toEqual("Hastings' Herb");
        expect(_.get(updatedProduct, 'displayName')).toEqual("Wade's Wonder Weed");
        expect(_.get(updatedProduct, 'eCommerceName')).toEqual("Wade's Wonder Weed");
        expect(_.get(updatedProduct, 'name')).toEqual("Wade's Wonder Weed");
    });

    it('updateProduct merges JSON fields', async () => {
        const product = {
            ...brandProduct,
            attributes: {
                general: ['ABC'],
            },
            details: {
                doses: 2,
            }
        };

        const createdProduct = await Product.build(product).save();

        const updates = {
            attributes: {
                ingredient: ['XYZ']
            },
            details: {
                thcMg: 10,
            }
        };

        await updateProduct(
            createdProduct.id,
            updates,
            testUser,
        );

        const updatedProduct = await Product.findOne({
            where: {
                id: createdProduct.id
            }
        });

        expect(_.get(updatedProduct, 'attributes')).toEqual({
            general: ['ABC'],
            ingredient: ['XYZ'],
        });
        expect(_.get(updatedProduct, 'details')).toEqual({
            doses: 2,
            thcMg: 10,
        });
    });

    it(`updateProduct skips update when resulting product doesn't change`, async () => {
        const product = await createProduct(
            {
                ...brandProduct,
                brandName: 'We Shall Remain',
            },
            testUser,
        );
        const updatedTimeBeforeUpdate = JSON.stringify(product.updatedAt);

        await sleep(1); // 1ms just to make sure test detects errors on super computers 🙃

        const updatedProduct = await updateProduct(
            product.id,
            { brandName: 'We Shall Remain' },
            testUser,
        );

        const updatedTimeAfterUpdate = JSON.stringify(updatedProduct.updatedAt);

        expect(updatedTimeAfterUpdate).toEqual(updatedTimeBeforeUpdate);
    });

    it(`retreives all products linked to a product using the getProductIdsLinkedToProduct static method`, async () => {
        const linkedBrandProduct = await Product.create({
            amount       : "1.000000",
            brandName    : "A",
            eCommerceName: "Alice's Cookies",
            name         : "Alice's Cookies",
            ownerId      : 1,
            ownerType    : BRAND,
            size         : "LARGE",
            subtype      : "OTHER",
            type         : "EDIBLE",
            uom          : UOM.EACH,
        });

        await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                linkedTo     : linkedBrandProduct.id,
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                linkedTo     : linkedBrandProduct.id,
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : STORE,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                linkedTo     : linkedBrandProduct.id,
                name         : "Alice's Cookies",
                ownerId      : 2,
                ownerType    : ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                linkedTo     : linkedBrandProduct.id,
                ownerId      : 2,
                ownerType    : STORE,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
        ]);

        const idsOfAllLinkedProducts = await Product.getProductIdsLinkedToProduct(linkedBrandProduct.id);

        expect(idsOfAllLinkedProducts).toHaveLength(4);
    });

    it(`defaults visible to true on create`, async () => {
        const orgProduct = await Product.create({
            amount:             "1.000000",
            brandName:          "A",
            eCommerceName:      "A",
            name:               "Alice Apples",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            size:               "50 piece bucket",
            subtype:            "Utter Nonsense",
            type:               "Nonsense",
            uom:                UOM.EACH,
        });

        expect(orgProduct.visible).toBe(true);
    });

    it(`gets brand matches for a product`, async () => {
        const [
            orgProduct,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's Second Brand",
                name:               "Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
        ]);

        const brandMatches = await orgProduct.getBrandMatches();

        const sortedBrandMatches = brandMatches.sort((a,b) => a.name < b.name ? -1 : 1);

        expect(brandMatches).toHaveLength(2);
        expect(sortedBrandMatches).toMatchObject([
            {
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            },
            {
                amount:             "1.000000",
                brandName:          "Nacho's Second Brand",
                name:               "Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            },
        ])
    });

    it(`gets brand matches for a product isolated by a brandName`, async () => {
        const [
            orgProduct,
        ] = await Promise.all([
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
            Product.create({
                amount:             "1.000000",
                brandName:          "Nacho's Second Brand",
                name:               "Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            }),
        ]);

        const brandMatches = await orgProduct.getBrandMatches(`Nacho's First Brand`);

        expect(brandMatches).toHaveLength(1);
        expect(brandMatches).toMatchObject([
            {
                amount:             "1.000000",
                brandName:          "Nacho's First Brand",
                name:               "Alice Apples",
                ownerId:            200,
                ownerType:          OwnerType.BRAND,
                size:               "50 piece bucket",
                type:               ProductType.BEVERAGE,
                uom:                UOM.EACH,
            },
        ]);
    });

    it(`uses method getLinkedCatalogProductCount to return the number of catalog products linked to it`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
                linkedTo: brandProduct.id,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
                linkedTo: brandProduct.id,
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'})
        ])

        const linkedCount = await brandProduct.getLinkedCatalogProductCount();

        expect(linkedCount).toBe(2);
    });

    it(`getLinkedCatalogProductCount counts a link product once for each catalog that it is in`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
                linkedTo: brandProduct.id,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
                linkedTo: brandProduct.id,
            })
        ]);

        await storeCatalog.update({
            parentCatalogId: orgCatalog.id,
        });

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'})
        ])

        const linkedCount = await brandProduct.getLinkedCatalogProductCount();

        expect(linkedCount).toBe(3);
    });

    it(`uses method getBrandSuggestedLinksCount to get the number of catalog products that have had a brand product suggested to link`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct.id,
                type: MatchType.BrandSuggested,
            }),
        ]);

        const matchCount = await brandProduct.getBrandSuggestedLinksCount();

        expect(matchCount).toBe(2);
    });

    it(`getBrandSuggestedLinksCount counts a brand suggested link once for each catalog that the product is in`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            })
        ]);

        await storeCatalog.update({
            parentCatalogId: orgCatalog.id,
        });

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct.id,
                type: MatchType.BrandSuggested
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct.id,
                type: MatchType.BrandSuggested,
            }),
        ])

        const brandMatchCount = await brandProduct.getBrandSuggestedLinksCount();

        expect(brandMatchCount).toBe(3);
    });

    it(`getWeightedMatches gets the total catalog products that have a wieghted match to a brand product`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            })
        ]);

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct.id,
                type: MatchType.Weighted,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct.id,
                type: MatchType.Weighted,
            }),
        ])

        const brandMatchCount = await brandProduct.getWeightedMatches();

        expect(brandMatchCount).toBe(2);
    });

    it(`getWeightedMatches counts a weighted link once for each catalog that the product is in`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct,
            orgProduct,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            })
        ]);

        await storeCatalog.update({
            parentCatalogId: orgCatalog.id,
        });

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            addProductToCatalog(orgCatalog, orgProduct.id, {userAuthId: 'bananas', sellTreezUserId: 'apples'}),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct.id,
                type: MatchType.Weighted,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct.id,
                type: MatchType.Weighted,
            }),
        ])

        const brandMatchCount = await brandProduct.getWeightedMatches();

        expect(brandMatchCount).toBe(3);
    });


    it(`gets a products adoption rate with getAdoption method`, async () => {
        const brandProduct = await Product.create({
            brandName: 'Bhang',
            ownerId  : 777,
            ownerType: OwnerType.BRAND,
            type     : ProductType.CARTRIDGE,
            amount   : 1,
            uom      : UOM.G,
            name     : 'Hella High 311',
        });

        const [
            orgCatalog,
            storeCatalog,
            storeProduct1,
            orgProduct1,
            orgProduct2,
            storeProduct2,
        ] = await Promise.all([
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
            }),
            Catalog.create({
                name     : 'Retail Catalog',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Hella High',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Tears de Maxi',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Product to Weight',
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Banana Dutch Sauce',
            }),
        ]);

        const [
            linkedOrgProduct,
            linkedStoreProduct,
        ] = await Promise.all([
            Product.create({
                brandName: 'Bhang',
                ownerId  : 13,
                ownerType: OwnerType.STORE,
                type     : ProductType.CARTRIDGE,
                amount   : 1,
                uom      : UOM.G,
                name     : 'Linked Tasty Cartridge',
                linkedTo : brandProduct.id,
            }),
            Product.create({
                brandName: 'Bhang',
                ownerId  : 20,
                ownerType: OwnerType.ORG,
                type     : ProductType.CARTRIDGE,
                amount   : .5,
                uom      : UOM.G,
                name     : 'Linked Stupid Cartridge',
                linkedTo : brandProduct.id,
            }),
            await storeCatalog.update({
                parentCatalogId: orgCatalog.id,
            }),
        ]);

        const userInfo = {userAuthId: 'bananas', sellTreezUserId: 'apples'};

        await Promise.all([
            addProductToCatalog(storeCatalog, storeProduct1.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct1.id, userInfo),
            addProductToCatalog(storeCatalog, storeProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, orgProduct2.id, userInfo),
            addProductToCatalog(orgCatalog, linkedOrgProduct.id, userInfo),
            addProductToCatalog(storeCatalog, linkedStoreProduct.id, userInfo),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct1.id,
                type: MatchType.BrandSuggested,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: orgProduct2.id,
                type: MatchType.Weighted,
            }),
            SuggestedLink.create({
                brandProductId: brandProduct.id,
                productToLinkId: storeProduct2.id,
                type: MatchType.Weighted,
            }),
        ]);

        const adoption = await brandProduct.getAdoption();

        expect(adoption).toMatchObject({
            productId               : brandProduct.id,
            linkedRetailCount       : 3,
            brandSuggestedLinksCount: 3,
            totalCount              : 9,
            weightedMatches         : 3,
            linkAdoption            : .33,
            brandSuggestedAdoption  : .33,
        });
    });
});
