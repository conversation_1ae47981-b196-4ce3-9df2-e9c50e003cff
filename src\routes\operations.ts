import {
    Response,
    Router,
    NextFunction
}                                               from 'express';
import { StatusCodes }                          from 'http-status-codes';
import _                                        from 'lodash';
import { OwnerType }                            from '@treez/commons/sharedTypings/product';
import {
    AuthenticatedRequest,
    User,
}                                               from '@treez/dev-pack/auth';
import logger                                   from '@treez/dev-pack/logger';
import {
    sendErrorResponse,
    ErrorResponse,
}                                               from '@treez/dev-pack/errors';
import { transaction }                          from '@treez/dev-pack/db';
import { getUserInfo }                          from '../lib/expressUtils';
import { 
    checkPermissionsOnMultipleCatalogs,
    requestorIsTreez
}                                               from '../middleware';
import Catalog, {
    addStoreCatalogsToOrganization,
    createCentralCatalog,
}                                               from '../models/Catalog';
import { performExternalReferenceMergeCleanup } from '../operations/ExternalReferenceMergeCleanup';
import { assertAllNotNull, assertNotBlank, assertNotNaN }                     from '../lib/commonFunctions';
import { ProductPermission }                    from '../lib/permissions';
import { 
    bulkDeleteCatalogProducts,
    findCatalogProductByCatalogId 
}                                              from '../models/CatalogProduct';
import {
    CatalogExportData,
    exportChunkGenerator
}                                               from '../operations/catalogExport';
import { UploadedFile, FileArray }              from 'express-fileupload';
import { CatalogImportHandler } from '../operations/catalogImport';
import { UploadedFileReader } from '../lib/fileReader';

const {
    DeleteCatalog,
} = ProductPermission;

const route = Router();

/*
    Confluence Doc: https://im360us.atlassian.net/wiki/spaces/EN/pages/1344372849/ProductAPI+-+Merge+Refactor
*/
route.post('/externalReferenceMergeCleanup',
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
    try {
        const {
            requestor,
            query,
        } = request;

        const productIdsParam = query.productIds as string;

        const productIds = productIdsParam.split(',').map(Number);

        logger.info(`${(requestor as User).email} initiated externalReferenceMergeCleanup for productIds ${productIds}`);

        const result = await transaction( async () => {
            return await performExternalReferenceMergeCleanup(productIds);
        })

        return response.status(StatusCodes.CREATED).json(result);
    }
    catch (error) {
        logger.error(`Error performing external reference merge cleanup in product-api`, error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 * components:
 *      requestBodies:
 *          CreateOrganizationRequestBody:
 *              description: Object that consists of the store catalog ids that are to create the central catalog along with an optional promote catalog value that makes all products in a particular catalog organization products.
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              name:
 *                                  title: "Organization Catalog Name"
 *                                  type: string
 *                              storeCatalogIds:
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              catalogIdToPromote:
 *                                  type: number
 *                          required:
 *                          - name
 *                          - storeCatalogIds
 */
interface CreateOrganizationRequestBody {
    name               : string;
    storeCatalogIds    : number[];
    catalogIdToPromote?: number;
}

/**
 * @swagger
 *
 * /operations/organization/${organizationId}:
 *  post:
 *      summary: Creates a new central catalog.  This takes the central catalog name to create a new organization catalog with ownerId of the organizationId param and assigns all store owned products to it. An optional parameter of promoting a catalog is available.
 *      tags:
 *          - operations
 *          - treez-only
 *      parameters:
 *      - name: organizationId
 *        in: path
 *        required: true
 *        description: the id of the organization as saved in Directory-API
 *        schema:
 *                  type: string
 *      requestBody:
 *          $ref: "#/components/requestBodies/CreateOrganizationRequestBody"
 *      responses:
 *          201:
 *              description: Organization central catalog created
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/Catalog'
 *          400:
 *              description: Could be for bad catalogIdToPromote where the catalog identified isn't also in the storeCatalogIds, could also be that the organization already has a catalog. If that is the case you should use that /operations/${organizationId}/addStores route
 *          403:
 *              description: Must be Treez
 *
 */
route.post(`/organization/:organizationId`,
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
    try {
        const {
            body,
            params,
        } = request;

        const { organizationId } = params;

        const {
            name,
            storeCatalogIds,
            catalogIdToPromote,
        } = body as CreateOrganizationRequestBody;

        const result = await transaction(async () => {
            const centralCatalog = await createCentralCatalog(
                Number(organizationId),
                storeCatalogIds,
                name,
                catalogIdToPromote,
            );

            return centralCatalog;
        });

        response.status(StatusCodes.CREATED).send(result);
    }
    catch (error) {
        logger.error(`Error creating organizational catalog in product-api`, error);
        return sendErrorResponse(response, error);
    }
});


/**
 * @swagger
 *  components:
 *      schemas:
 *          CloneCatalogRequest:
 *              properties:
 *                  name:
 *                      title: name of the new catalog
 *                      type: string
 *                  ownerId:
 *                      title: The owner id
 *                      type: number
 *                  ownerType:
 *                      title: the ownerType
 *                      type: string
 *                      enum: [store, organization]
 *                  parentCatalogId:
 *                      title: The parent Catalog Id
 *                      type: number
 */
export class CloneCatalogRequest {
    name: string;
    ownerId: number;
    ownerType: OwnerType;
    parentCatalogId: number;
}

/**
 * @swagger
 *
 * /operations/cloneCatalog/${id}:
 *  post:
 *      summary: creates a clone of a catalog.  Catalog being cloned must have a parent catalog. All ownership of products in the catalog that is being cloned will be passed to the organization. Ownership is passed to the organization as cloning a catalog, or passing a store product to another store, is an implicit adoption of that product at the organizational level.
 *      parameters:
 *          - name: id
 *            in: path
 *            required: true
 *            description: the id of the catalog to be saved.
 *            schema:
 *              type: string
 *          - name: cloneRequest
 *            in: body
 *            required: true
 *            description: the body of the cloning request
 *            schema:
 *              $ref: '#/components/schemas/CloneCatalogRequest'
 *      consumes:
 *          - application/json
 *      responses:
 *          201:
 *              description: new catalog created
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/Catalog'
 *          400:
 *              description: Could be because the catalog that was being cloned did not have a parent catalog
 *          403:
 *              description: Must be Treez
 *
 */
route.post('/cloneCatalog/:id',
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
    const { id } = request.params;
    const newCatalogDetails = request.body as CloneCatalogRequest;
    if(!request.is('application/json')) {
        return response.sendStatus(StatusCodes.NOT_ACCEPTABLE);
    }
    try {
        const newCatalog = await Catalog.cloneCatalog(Number(id), newCatalogDetails, getUserInfo(request))
        response.status(StatusCodes.CREATED).send(newCatalog);
    }
    catch (error) {
        logger.error(`Error bulk cloning catalog ${id}`, error);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *
 * /operations/export_catalog/${id}:
 *  get:
 *      summary: export all catalog data into JSON format 
 *      parameters:
 *          - name: id
 *            in: path
 *            required: true
 *            description: the id of the catalog to be exported
 *            schema:
 *              type: number
 *      responses:
 *          201:
 *              description: catalog successfully exported
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/Catalog'
 *          404:
 *              description: Could be because the catalog that was being cloned does not exists
 *          403:
 *              description: Must be Treez
 *
 */
route.get('/export_catalog/:id',
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
   const { id } = request.params;

   try {
    for await (let chunk of exportChunkGenerator(Number(id))) {
        response.write(JSON.stringify(chunk))
        response.write('\n');
    }
    response.status(StatusCodes.OK);
    response.end();
   } catch (error) {
       logger.error(`error exporting catalog: ${id}`, error);
       return sendErrorResponse(response, error);
   }
});

/**
 * @swagger
 *
 * /operations/import_catalog:
 *  post:
 *      summary: import catalog data from json to the storage
 *      responses:
 *          201:
 *              description: catalog successfully exported
 *              content:
 *                  multipart/form-data:
 *                      schema:
 *                         ownerId:
 *                            type: number
 *                         catalogId:
 *                            type: number
 *                         catalogName:
 *                            type: string
 *                         file:
 *                            type: string
 *                          
 *          400:
 *              description: Could be because the targetCatalogId already exists
 *          403:
 *              description: Must be Treez
 *
 */
route.post('/import_catalog',
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
     try {
        request.setTimeout(0);
        response.setTimeout(0);
        const files = request.files as FileArray;
        const { ownerId, catalogId, catalogName } = request.body;
        const keepExternalIds = (request.body.keepExternalIds === 'true');
     
        if(catalogId) {
            assertNotBlank(catalogName, 'catalogName cannot be null given a catalogId');
            assertNotNaN(Number(catalogId), 'catalogId must be a number');
        }

        if(ownerId) {
            assertNotNaN(Number(ownerId), 'ownerId must be a number');
        }
     
         assertAllNotNull([files, files.file], 'No file was uploaded.');
     
         const file = files.file as UploadedFile;
      
        const importHandler = new CatalogImportHandler({
            targetCatalogId: catalogId,
            targetCatalogName: catalogName,
            ownerId,
            keepExternalIds
        });

        const reader = new UploadedFileReader(file);

        if(keepExternalIds && catalogId) {
            await reader.readLines(async (line) => {
                const chunk = JSON.parse(line) as CatalogExportData;
                if(chunk.externalReferences) {
                    await importHandler.validateExternalReference(chunk.externalReferences);
                }
            })
        }


        await reader.readLines(async (line) => {
            await importHandler.append(JSON.parse(line));
        })
        
        response.statusCode = 200;
        response.send(importHandler.getExportReport());
     } catch (error) {
        response.statusCode = 500;
        response.send(error)
     }
});

/**
 * @swagger
 *  components:
 *      requestBodies:
 *          AddStoresToOrganizationCatalog:
 *              description: Catalog Ids to add to organization's central catalog
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: array
 *                          items:
 *                              type: number
 */

/**
 * @swagger
 *
 * /operations/organization/${organizationId}/stores:
 *  post:
 *      summary: adds stores to a central catalog
 *      tags:
 *          - operations
 *          - treez-only
 *      parameters:
 *      - name: organizationId
 *        in: path
 *        required: true
 *        description: the id organization whose catalog should be the parent of the store catalog ids identified in the body
 *        schema:
 *            type: string
 *      requestBody:
 *          $ref: '#/components/requestBodies/AddStoresToOrganizationCatalog'
 *      responses:
 *          201:
 *              description: new catalog created
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/Catalog'
 *          400:
 *              description: Could be because the catalog that was being cloned did not have a parent catalog
 *          403:
 *              description: Must be Treez
 *
 */
route.post(`/organization/:organizationId/stores`,
requestorIsTreez,
async (request: AuthenticatedRequest, response: Response) => {
    const {
        body,
        params,
    } = request;

    try {

        const { organizationId } = params;

        const {storeCatalogIds} = body as {storeCatalogIds: number[]};

        const orgCatalog = await Catalog.findOne({
            where: {
                ownerId  : Number(organizationId),
                ownerType: OwnerType.ORG,
            }
        });

        if (orgCatalog == null) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Organization Catalog not found. Please specify corrected organization Id`
            );
        }

        const result = await addStoreCatalogsToOrganization(orgCatalog.id, storeCatalogIds);

        response.status(StatusCodes.ACCEPTED).send(result);
    }
    catch (error) {
        logger.error(`Error bulk adding Store Catalogs to Org Catalogs`, error, body, params);
        return sendErrorResponse(response, error);
    }
});

/**
 * @swagger
 *  components:
 *      requestBodies:
 *          CatalogProductsStatusUpdate:
 *              description: Object to communicate what products in which catalogs to update to a singular status
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              catalogIds:
 *                                  title: Catalog Ids
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              productIds:
 *                                  title: Product Ids
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              status:
 *                                  title: Product Status
 *                                  type: string
 *                                  enum: [ACTIVE, DELETED, DEACTIVED]
 */
 interface CatalogProductsDelete {
    catalogIds?: number[];
}
/**
 * @swagger
 *  delete:
 *      /catalog_products:
 *          delete:
 *              summary: Marks a list of catalogProducts DELETED and records a catalogProductChange
 *              parameters:
 *                - in: path
 *                  name: catalogId
 *                  schema:
 *                      type: integer
 *                  required: true
 *                - in: path
 *                  name: productId
 *                  schema:
 *                      type: integer
 *                  required: true
 */
route.delete('/catalog_products',
checkPermissionsOnMultipleCatalogs('body', 'catalogIds', [[DeleteCatalog]]),
async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {    
    try {
        const {
            catalogIds
        } = request.body as CatalogProductsDelete;
        
        const userInfo = getUserInfo(request);

        assertAllNotNull([catalogIds],
            'Request body may "catalogIds"');

        const deleted = await transaction(async () => {
            let catalogProducts = await findCatalogProductByCatalogId(catalogIds);
            
            return await bulkDeleteCatalogProducts(catalogProducts, userInfo);
        });

        return response.json({ deleted });
    }
    catch (error) {
        logger.error("Error updating products' status on catalogProduct api: ", error);
        return sendErrorResponse(response, error);
    }
})

export default route;
