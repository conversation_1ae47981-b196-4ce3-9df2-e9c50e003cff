import { StatusCodes }                  from 'http-status-codes';
import supertest                        from 'supertest';
import {
    OwnerType,
    ProductType,
    ProductField,
}                                       from '@treez/commons/sharedTypings/product';
import { ValidationType }               from '../../lib/sharedInterfaces';
import Catalog                          from '../../models/Catalog';
import ProductRequirements, {
    mapJsonSchemaOnRequirements,
}                                       from '../../models/ProductRequirements';
import app                              from './testApp';

describe('/productRequirements endpoints', () => {
    it(`correctly maps conditons and requirements to json schema`, async () => {
        const productRequirements = await ProductRequirements.build({
            description: "descriptions can be used to outline what type of requirements are being added",
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    requiredField: true,
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                                requiredField: true,
                            },
                            [ProductField.CANNABIS]: {
                                dataType: ValidationType.BOOLEAN
                            }
                        }
                    }
                }
            }
        }).save();

        const schema = mapJsonSchemaOnRequirements(productRequirements.requirements);

        expect(schema).toMatchObject({
            properties: {
                [ProductField.TYPE]: {
                    enum: [ProductType.BEVERAGE, ProductType.EDIBLE]
                }
            },
            allOf: [
                {
                    "if": {
                        properties: { [ProductField.TYPE]: {"const" : ProductType.BEVERAGE} }
                    },
                    "then": {
                        properties: {
                            [ProductField.PRICE]: {
                                type: ValidationType.NUMBER
                            },
                            [ProductField.CANNABIS]: {
                                type: ValidationType.BOOLEAN
                            }
                        },
                        required: [ProductField.PRICE],
                    }
                },
            ],
            required: [ProductField.TYPE],
        });
    });

    it(`saves requirements`, async () => {
        const response = await supertest(app)
            .post(`/productRequirements`)
            .send({
                description: "descriptions can be used to outline what type of requirements are being added",
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                }
                            }
                        }
                    }
                }
            });

        expect(response.status).toBe(StatusCodes.CREATED);
        expect(response.body).toMatchObject({
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                }
            }
        });
    });

    it(`rejects requirements that do not follow the right structure`, async () => {
        const response = await supertest(app)
            .post(`/productRequirements`)
            .send({
                description: "descriptions can be used to outline what type of requirements are being added",
                requirements: {
                    "productTypesList": {
                        dataType: "rainbow",
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                }
                            }
                        }
                    }
                }
            });

        expect(response.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY);
    })

    it(`gets requirements posted to a catalog`, async () => {
        const requirements = await ProductRequirements.build({
                description: "descriptions can be used to outline what type of requirements are being added",
                requirements: {
                    [ProductField.TYPE]: {
                        dataType: ValidationType.STRING,
                        options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                        conditionalRequirements: {
                            [ProductType.BEVERAGE]: {
                                [ProductField.PRICE]: {
                                    dataType: ValidationType.NUMBER,
                                }
                            }
                        }
                    }
                }
            }).save();

        const response = await supertest(app)
            .get(`/productRequirements/${requirements.id}`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject({
            description: "descriptions can be used to outline what type of requirements are being added",
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                }
            }
        });
    });

    it(`gets requirements by catalogId`, async () => {
        const ownerId = 420;
        Catalog.build({
            name:               "Test Org Catalog",
            ownerId:            ownerId,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        }).save();

        const requirement =
        {
            ownerId: ownerId,
            ownerType: OwnerType.ORG,
            description: "descriptions can be used to outline what type of requirements are being added",
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                }
            }
        }

        await ProductRequirements.build(requirement).save();

        const response = await supertest(app)
            .get(`/productRequirements/catalog/1`);

        expect(response.status).toBe(StatusCodes.OK);
        expect(response.body).toMatchObject([requirement]);
    });

    it('allows you to edit requirements', async () => {
        const requirements = await ProductRequirements.build({
            description: "descriptions can be used to outline what type of requirements are being added",
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                }
            }
        }).save();

        const response = await supertest(app)
            .patch(`/productRequirements/${requirements.id}`)
            .send({
                description: 'shorter description',
                requirements: {
                    [ProductField.PRICE]: {
                        dataType: ValidationType.NUMBER
                    }
                }
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED)
        expect(response.body).toMatchObject({
            description: 'shorter description',
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                },
                [ProductField.PRICE]: {
                    dataType: ValidationType.NUMBER
                }
            }
        })
    });

    it('allows you to edit requirements', async () => {
        const requirements = await ProductRequirements.build({
            description: "descriptions can be used to outline what type of requirements are being added",
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                }
            }
        }).save();

        const response = await supertest(app)
            .patch(`/productRequirements/${requirements.id}`)
            .send({
                description: 'shorter description',
                requirements: {
                    [ProductField.PRICE]: {
                        dataType: ValidationType.NUMBER
                    }
                }
            });

        expect(response.status).toBe(StatusCodes.ACCEPTED)
        expect(response.body).toMatchObject({
            description: 'shorter description',
            requirements: {
                [ProductField.TYPE]: {
                    dataType: ValidationType.STRING,
                    options: [ProductType.BEVERAGE, ProductType.EDIBLE],
                    conditionalRequirements: {
                        [ProductType.BEVERAGE]: {
                            [ProductField.PRICE]: {
                                dataType: ValidationType.NUMBER,
                            }
                        }
                    }
                },
                [ProductField.PRICE]: {
                    dataType: ValidationType.NUMBER
                }
            }
        })
    });
});
