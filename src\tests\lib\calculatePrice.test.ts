import {updateProductsInTier}                                      from '../../lib/calculatePrice';
import Product                                                     from "../../models/Product";
import {OwnerType, UOM}                                            from "@treez/commons/sharedTypings/product";
import Catalog                                                     from "../../models/Catalog";
import CatalogProduct, {addProductToCatalog, CatalogProductStatus} from "../../models/CatalogProduct";
import PriceTier                                                   from "../../models/PriceTier";
import {PriceTierMethod, RangeMode}                                from "@treez/commons/sharedTypings/priceTier";

describe('updateProductsInTier',  () => {

    const fakeUser: UserInfo = { userAuthId: '<EMAIL>' };

    it('check to see if calculatePrice functions can handle a product with no amount and method Weight', async () => {
        const [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             null,
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.G,
            }),
            PriceTier.create({
                label: "First Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.WEIGHT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                }],
            }),
        ])

        await addProductToCatalog(catalog, product.id, fakeUser, {
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });

        await updateProductsInTier(priceTier.id)
        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        expect(catalogProduct).not.toBe(null);
        if(catalogProduct){
            expect(catalogProduct.priceTierId).toBe(priceTier.id);
            expect(catalogProduct.price).toBe(null);
        }

    });

    it('check to see if calculatePrice functions can handle a product with method as weight and amount which falls in first teir', async () => {
        const [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             2,
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.G,
            }),
            PriceTier.create({
                label: "Sample Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.WEIGHT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                },{
                    value: 3,
                    start: 3,
                    end: 5
                },{
                    value: 2,
                    start: 6,
                    end: 9
                }],
            }),
        ])

        await addProductToCatalog(catalog, product.id, fakeUser, {
            price: 10.00,
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });

        await updateProductsInTier(priceTier.id)

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        if(catalogProduct){
            expect(catalogProduct.priceTierId).toBe(priceTier.id);
            expect(catalogProduct.price).toBe("8.000000");
        }

    });

    it('check to see if calculatePrice functions can handle a product with method as weight and amount which falls in second teir', async () => {
        const [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             4,
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.G,
            }),
            PriceTier.create({
                label: "Sample Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.WEIGHT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                },{
                    value: 3,
                    start: 3,
                    end: 5
                },{
                    value: 2,
                    start: 6,
                    end: 9
                }],
            }),
        ])

        await addProductToCatalog(catalog, product.id, fakeUser, {
            price: 10.00,
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });


        await updateProductsInTier(priceTier.id)

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        if(catalogProduct){
            expect(catalogProduct.priceTierId).toBe(priceTier.id);
            expect(catalogProduct.price).toBe("12.000000");
        }

    });

    it('check to see if calculatePrice functions can handle a product with method as weight and amount which falls in third teir', async () => {
        const [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             9,
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.G,
            }),
            PriceTier.create({
                label: "Sample Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.WEIGHT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                },{
                    value: 3,
                    start: 3,
                    end: 5
                },{
                    value: 2,
                    start: 6,
                    end: null
                }],
            }),
        ])

        await addProductToCatalog(catalog, product.id, fakeUser, {
            price: 10.00,
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });

        await updateProductsInTier(priceTier.id)

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        if(catalogProduct){
            expect(catalogProduct.priceTierId).toBe(priceTier.id);
            expect(catalogProduct.price).toBe("18.000000");
        }

    });

    it('check to see if calculatePrice functions can handle a product with method type unit', async () => {
        const [
            catalog,
            product,
            priceTier,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
            Product.create({
                amount:             3,
                brandName:          "Brand Tester",
                eCommerceName:      "Z0M&WHY?!",
                name:               "ZOMGWHY?!",
                ownerId:            1,
                ownerType:          OwnerType.ORG,
                msrp:               "25.000000",
                size:               "50 piece bucket",
                subtype:            "Utter Nonsense",
                type:               "Nonsense",
                uom:                UOM.EACH,
            }),
            PriceTier.create({
                label: "First Tier",
                ownerId: 1,
                ownerType: OwnerType.ORG,
                isActive: true,
                method: PriceTierMethod.UNIT,
                rangeMode: RangeMode.FIXED_PRICE,
                thresholdType: "TIER",
                thresholds: [{
                    value: 4,
                    start: 1,
                    end: 2
                },{
                    value: 3,
                    start: 3,
                    end: 5
                },{
                    value: 2,
                    start: 6,
                    end: null
                }],
            }),
        ])

        await addProductToCatalog(catalog, product.id, fakeUser, {
            price: 10.00,
            status: CatalogProductStatus.ACTIVE,
            priceTierId: priceTier.id
        });

        await updateProductsInTier(priceTier.id)

        const catalogProduct = await CatalogProduct.findOne({
            where: {
                catalogId: catalog.id,
                productId: product.id,
            }
        });

        if(catalogProduct){
            expect(catalogProduct.priceTierId).toBe(priceTier.id);
            expect(catalogProduct.price).not.toBe(null);
            expect(catalogProduct.price).toBe("4.000000");
        }

    });

});