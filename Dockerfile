FROM node:12-buster-slim

WORKDIR /app

ARG npmToken
ENV NPM_TREEZ_TOKEN=$npmToken

# Dependencies
RUN apt-get update
RUN apt-get -y install wget
RUN wget -O apache-pulsar-client.deb https://archive.apache.org/dist/pulsar/pulsar-2.10.2/DEB/apache-pulsar-client.deb
RUN wget -O apache-pulsar-client-dev.deb https://archive.apache.org/dist/pulsar/pulsar-2.10.2/DEB/apache-pulsar-client-dev.deb
RUN apt-get -y install ./apache-pulsar-client*.deb python build-essential

#Clean up Packages
RUN apt-get -y remove wget

COPY . .

RUN ls -lha

RUN rm -r */

RUN npm ci

COPY . .

RUN npm run build

RUN ls -lha

ARG dockerTag
ENV DOCKER_TAG ${dockerTag:-<unknown>}

EXPOSE 8303

CMD [ "npm", "start" ]
