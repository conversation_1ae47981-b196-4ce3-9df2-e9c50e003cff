import {
    QueryInterface,
}                                       from 'sequelize';
import logger                           from '@treez/dev-pack/logger';

const ORGANIZATION_ID_OFFSET = 1000;
const STORE_ID_OFFSET        = 2000;
const BRAND_ID_OFFSET        = 3000;

export = {

    up: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await offsetIds(queryInterface, `catalogs`,            '+');
            await offsetIds(queryInterface, `products`,            '+');
            await offsetIds(queryInterface, `priceTiers`,          '+');
            await offsetIds(queryInterface, `productRequirements`, '+');
        });
    },

    down: async(queryInterface: QueryInterface) => {
        await queryInterface.sequelize.transaction(async () => {
            await offsetIds(queryInterface, `productRequirements`, '-');
            await offsetIds(queryInterface, `priceTiers`,          '-');
            await offsetIds(queryInterface, `products`,            '-');
            await offsetIds(queryInterface, `catalogs`,            '-');
        });
    },

};

const offsetIds = async (queryInterface: QueryInterface, tableName: string, direction: '+' | '-') => {

    logger.info(`normalizing entity IDs for ${tableName}...`);

    await queryInterface.sequelize.query(`
        update
            "${tableName}"
        set "ownerId" = (
            CASE
                WHEN "ownerType" = 'organization' THEN
                    CAST ("ownerId" AS integer) ${direction} ${ORGANIZATION_ID_OFFSET}
                WHEN "ownerType" = 'store' THEN
                    CAST ("ownerId" AS integer) ${direction} ${STORE_ID_OFFSET}
                ELSE
                    CAST ("ownerId" AS integer) ${direction} ${BRAND_ID_OFFSET}
            END
        )
        where
            "ownerType" IS NOT NULL
            AND
            "ownerId" IS NOT NULL
        ;
    `);

};
