import {
    NextFunction,
    Response,
}                                   from 'express';
import {
    ReasonPhrases,
    StatusCodes,
}                                   from 'http-status-codes';
import {
    AuthenticatedRequest,
    User,
}                                   from '@treez/dev-pack/auth';
import { ErrorResponse }            from '@treez/dev-pack/errors';
import { ProductPermission }        from '../../lib/permissions';
import {
    multipleResourceChecks,
    resourcePermissionCheck,
}                                   from '../../middleware/permissionChecks';
import { hasPermissionsSpy }        from '../../tests/testHelpers/util';

const { UpdateCatalog } = ProductPermission;

const request = {
    headers: {
        authorization: 'Bearer StubbedInTest'
    }
} as AuthenticatedRequest;

request.requestor = new User({
    email: '<EMAIL>',
    verified: true,
});
const response = {} as Response;

describe('Multiple Resource Checks', () => {
    beforeEach(() => {
        hasPermissionsSpy.mockRestore();
    });

    it('passes when a user has required permissions for all resources requested', () => {
        const next = jest.fn() as NextFunction;

        request.userPermissions = [
            {
                entityId: 1,
                descendentEntityIds: [],
                permissions: [UpdateCatalog],
            },
            {
                entityId: 2,
                descendentEntityIds: [],
                permissions: [UpdateCatalog]
            }
        ];

        multipleResourceChecks(
            async () => [1,2],
            [[UpdateCatalog]],
        )(request, response, next), () => void

        expect(next).toBeCalledWith();
    });

    it('fails when a user does not have required permissions for all resource requested', () => {
        const next = jest.fn() as NextFunction;

        request.userPermissions = [
            {
                entityId: 1,
                descendentEntityIds: [],
                permissions: [UpdateCatalog],
            },
        ];

        multipleResourceChecks(
            async () => [1,2],
            [[UpdateCatalog]],
        )(request, response, next), () => void

        expect(next).toBeCalledWith(new ErrorResponse(StatusCodes.FORBIDDEN, ReasonPhrases.FORBIDDEN));
    });
});

describe('resourcePermissionCheck', () => {
    beforeEach(() => {
        hasPermissionsSpy.mockRestore();
    });

    it('passes when a user has required permissions for the resource requested', () => {
        const next = jest.fn() as NextFunction;

        request.userPermissions = [
            {
                entityId: 1,
                descendentEntityIds: [],
                permissions: [UpdateCatalog],
            },
        ];

        resourcePermissionCheck(
            async () => 1,
            [[UpdateCatalog]],
        )(request, response, next), () => void

        expect(next).toBeCalledWith();
    });

    it('fails when a user does not have required permissions for resource requested', () => {
        const next = jest.fn() as NextFunction;

        request.userPermissions = [
            {
                entityId: 1,
                descendentEntityIds: [],
                permissions: [UpdateCatalog],
            },
        ];

        resourcePermissionCheck(
            async () => 2,
            [[UpdateCatalog]],
        )(request, response, next), () => void

        expect(next).toBeCalledWith(new ErrorResponse(StatusCodes.FORBIDDEN, ReasonPhrases.FORBIDDEN));
    })
})
