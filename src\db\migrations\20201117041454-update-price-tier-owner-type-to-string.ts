import { QueryInterface }               from 'sequelize';
import {
    intField,
    stringField,
    notNull,
}                                           from '@treez/dev-pack/db/fieldTypes';

const OWNER_ID    = 'ownerId';
const PRICE_TIERS = 'priceTiers';

export = {
    up: async (queryInterface: QueryInterface) => {
        await queryInterface.changeColumn(PRICE_TIERS, OWNER_ID, notNull(stringField));
    },
    down: async (queryInterface: QueryInterface) => {
        await queryInterface.changeColumn(PRICE_TIERS, OWNER_ID, notNull(intField));
    }
}
