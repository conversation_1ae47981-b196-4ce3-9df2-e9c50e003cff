import _                        from 'lodash';
import { OwnerType }            from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    PriceTierThresholdType,
    RangeMode,
}                               from '@treez/commons/sharedTypings/priceTier';
import PriceTier                from '../../models/PriceTier';
import * as PriceTierFunctions  from '../../models/PriceTier';

const thresholdsTemplate = [
    {
        value: 4,
        start: 1,
        end: 3.5
    },
    {
        value: 3,
        start: 3.5,
        end: 5
    },
    {
        value: 1.5,
        start: 5,
    }
];

const PriceTierTemplate = {
    label: "RipOff Tier",
    ownerId: 91,
    ownerType: OwnerType.ORG,
    isActive: true,
    method: PriceTierMethod.WEIGHT,
    rangeMode: RangeMode.FIXED_PRICE,
    thresholdType: PriceTierThresholdType.FLAT,
    thresholds: thresholdsTemplate
}

describe("Price Tier Model", () => {
    //  validateThresholds

    it('validates PriceTier with correct PriceTier object and correct thresholds array', async () => {
        const priceTier = {
            ...PriceTierTemplate,
        };

        _.set(priceTier, "isActive", false);

        expect(() => {
            PriceTierFunctions.validatePriceTier(priceTier);
        }).not.toThrow();
    });

    it('validates PriceTier with incorrect PriceTier object and correct thresholds array', async () => {
        const priceTier = await PriceTier.create({
            ...PriceTierTemplate,
        })

        await expect(
            //@ts-ignore
            priceTier.update({ label: null, isActive: null })
        ).rejects.toThrow();
    });

    it('validates PriceTier with correct PriceTier object but incorrect thresholds array', async () => {
        const priceTier = {
            ...PriceTierTemplate,
        }

        _.set(priceTier, "thresholds", [
            {
                value : null
            }
        ]);

        expect(() => {
            PriceTierFunctions.validatePriceTier(priceTier);
        }).toThrow();
    });

    it('validates thresholds on create', async () => {
        const priceTier = {
            ...PriceTierTemplate,
            thresholds: [
                {
                    value: null
                }
            ]
        }

        let error;
        try{
            await PriceTier.create(priceTier);
        }
        catch(e){
            error = e;
        }

        expect(error).not.toBeUndefined();
        expect(error).not.toBeNull();
        expect(error.name).toBe("SequelizeValidationError");
    });

    it('validates thresholds on update', async () => {
        const priceTier = await PriceTier.create(PriceTierTemplate);

        let error;
        try{
            await priceTier.update({
                thresholds: [
                    {
                        // @ts-ignore
                        value: null
                    }
                ]
            });
        }
        catch(e){
            error = e;
        }

        expect(error).not.toBeUndefined();
        expect(error).not.toBeNull();
        expect(error.name).toBe("SequelizeValidationError");
    });

    it('orders thresholds by start on create', async () => {
        const unorderedThresholds = [
            {
                value: 1.5,
                start: 5,
            },
            {
                value: 3,
                start: 3.5,
                end: 5
            },
            {
                value: 4,
                start: 1,
                end: 3.5
            },
        ];

        const priceTier = await PriceTier.create({
            ...PriceTierTemplate,
            thresholds: unorderedThresholds,
        });

        expect(priceTier.thresholds.length).toBe(3);
        expect(priceTier.thresholds[0].start).toBe(1);
        expect(priceTier.thresholds[1].start).toBe(3.5);
        expect(priceTier.thresholds[2].start).toBe(5);
    });

    it('orders thresholds by start on update', async () => {
        const unorderedThresholds = [
            {
                value: 1.5,
                start: 5,
            },
            {
                value: 3,
                start: 3.5,
                end: 5
            },
            {
                value: 4,
                start: 1,
                end: 3.5
            },
        ];

        const priceTier = await PriceTier.create(PriceTierTemplate);

        const updatedPriceTier = await priceTier.update({
            thresholds: unorderedThresholds,
        });

        expect(updatedPriceTier.thresholds.length).toBe(3);
        expect(updatedPriceTier.thresholds[0].start).toBe(1);
        expect(updatedPriceTier.thresholds[1].start).toBe(3.5);
        expect(updatedPriceTier.thresholds[2].start).toBe(5);
    });

    it('Records a change in price tier upon creation', async () => {
        const tier = await PriceTier.create(PriceTierTemplate);

        const priceTierResult = await PriceTier.findAll({
            where: {
                id: tier.id
            }
        });

        expect(priceTierResult.length).toBe(1);
        expect(priceTierResult[0]).toMatchObject({
            label: "RipOff Tier",
            ownerId: 91,
            ownerType: OwnerType.ORG,
        });
    });

    it('Checks to see if validation is called for the thresholds', async () => {
        const priceTierThresholdsValidationFunction = jest.spyOn(PriceTierFunctions, 'validateThresholds');
        await PriceTier.create(PriceTierTemplate);

        expect(priceTierThresholdsValidationFunction).toHaveBeenCalled();
    });

    it('Records an update to price tier upon update', async () => {
        const priceTier = await PriceTier.create(PriceTierTemplate);
        const label = "Kush fraud Tier";

        await priceTier.update({
            label,
        });

        const priceTierResult = await PriceTier.findAll({
            where: {
                id: priceTier.id
            }
        });

        expect(priceTierResult.length).toBe(1);
        expect(priceTierResult[0]).toMatchObject({
            label
        });
    });
});

