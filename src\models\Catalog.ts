import { StatusCodes }                  from 'http-status-codes';
import _                                from 'lodash';
import { ValidationOptions } from 'sequelize/types/instance-validator';
import {
    FindOptions,
    IncludeOptions,
}                                       from 'sequelize/types';
import {
    AutoIncrement,
    BelongsToMany,
    Column,
    PrimaryKey,
    Table,
    ForeignKey,
    BelongsTo,
    HasMany,
    AfterCreate,
}                                       from 'sequelize-typescript';
import {
    connection,
    transaction,
}                                       from '@treez/dev-pack/db';
import {
    enumField,
    intField,
    notNull,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';

import { ErrorResponse }                from '@treez/dev-pack/errors';
import { TreezModel }                   from '@treez/dev-pack/models/TreezBaseModel';
import {
    OwnerType,
    ProductData,
}                                       from '@treez/commons/sharedTypings/product';
import { brandNameCorrelationScore }    from '../constants/products';
import CatalogProduct, {
    addProductToCatalog,
    CatalogProductStatus,
    findCatalogProduct
}                                       from './CatalogProduct';
import Product, {
    addBrandTreezProductToCatalog,
    findProductById,
    updateProduct,
}                                       from './Product';
import ProductRequirements, {
    createProductRequirements,
    findProductRequirementsById,
    validateProductAgainstRequirements,
}                                       from './ProductRequirements';
import ProductRequirementsToCatalogs    from './ProductRequirementsToCatalogs';
import Assortment, {
    AssortmentStatus,
    AssortmentType
}                                       from './Assortment';
import CatalogPriceTier                 from './CatalogPriceTier';
import { MatchType }                    from './SuggestedLink';

const validCatalogPatchFields = [
    'name',
    'ownerId',
    'ownerType',
    'parentCatalogId',
];

/**
 * @swagger
 *  components:
 *      schemas:
 *          CatalogCarryingBrandProduct:
 *              properties:
 *                  brandProducts:
 *                      title: Number of brand products present
 *                      type: number
 *                  catalogId:
 *                      title: The id of the catalog where brand products are present
 *                      type: number
 *                  catalogName:
 *                      title: The name of the catalog
 *                      type: string
 *                  catalogOwnerType:
 *                      title: The type of owner for the catalog
 *                      type: string
 *                      enum: [store, organization]
 */

export interface CatalogCarryingBrandProduct {
    brandProducts   : number;
    catalogId       : number;
    catalogName     : string;
    catalogOwnerType: string;
}

/**
 * @swagger
 *  components:
 *      schemas:
 *          Catalog:
 *              properties:
 *                  id:
 *                      title: Catalog Unique Identifier/Primary Key
 *                      type: integer
 *                  name:
 *                      title: Name of the Catalog
 *                      type: string
 *                  ownerId:
 *                      title: Unique Identifier of the Owner Type
 *                      type: number
 *                  ownerType:
 *                      title: Type of Entity that owns the Catalog
 *                      type: string
 *                      enum:
 *                          - brand
 *                          - organization
 *                          - store
 *                  parentCatalogId:
 *                      title: Parent Catalog Id
 *                      type: integer
 *              required:
 *                  - name
 *                  - ownerId
 *                  - ownerType
 */

@Table({tableName: 'catalogs'})
export default class Catalog extends TreezModel<Catalog> {
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                             number;

    @Column(notNull(stringField))
    name:                           string;

    @Column(notNull(intField))
    ownerId:                        number;

    @Column(notNull(enumField(OwnerType)))
    ownerType:                      OwnerType;

    @ForeignKey(() => Catalog)
    @Column
    parentCatalogId:                number;

    @BelongsTo(() => Catalog)
    parentCatalog:                  Catalog;

    @HasMany(() => Catalog)
    childCatalogs:                  Catalog[];

    @BelongsToMany(() => Product, () => CatalogProduct)
    products:                       Product[];

    @BelongsToMany(() => ProductRequirements, () => ProductRequirementsToCatalogs)
    productRequirements:            ProductRequirements[];

    //Hooks
    @AfterCreate
    static async createDefaultCollection(instance: Catalog) {
        await Assortment.create({
            catalogId: instance.id,
            type: AssortmentType.COLLECTION,
            name: 'Featured',
            icon: 'featured_star.svg',
            order: 0,
            principal: true,
            status: AssortmentStatus.ACTIVE
        });
    };

    static findAssortmentsByCatalog = async (
        catalogId: number,
        type: AssortmentType
    ) => {
        return await Assortment.findAll({
            where: {
                catalogId,
                type,
                status: AssortmentStatus.ACTIVE
            }
        });
    };

    static cloneCatalog = async (
        catalogToCloneId: number,
        newCatalogInformation: Pick<Catalog, 'ownerId' | 'ownerType' | 'name'>,
        userInfo: UserInfo,
    ): Promise<Catalog> => {
        const catalogToClone  = await findCatalogById(catalogToCloneId);

        const {parentCatalogId} = catalogToClone;

        if (parentCatalogId == null) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'The catalog that is being requested to be cloned does not have an organization central catalog associated with it'
            );
        }

        const parentCatalog = await findCatalogById(parentCatalogId);

        const newCatalog = await Catalog.create({
            ...newCatalogInformation,
            parentCatalogId,
        });

        const storeOwnedProducts = await Product.findAll({
            where: {
                ownerId: catalogToClone.ownerId,
                ownerType: OwnerType.STORE,
            }
        });

        await Promise.all(
            storeOwnedProducts.map((productToConvert) => {
                return updateProduct(
                    productToConvert.id,
                    {
                        ownerId: parentCatalog.ownerId,
                        ownerType: parentCatalog.ownerType,
                    },
                    userInfo,
                )
            })
        );

        await connection.query(`
            INSERT INTO "catalogProducts" (
                "catalogId",
                "productId",
                "status",
                "price",
                "catalogOverrides"
            ) (
                SELECT
                    :catalogId,
                    "productId",
                    "status",
                    "price",
                    "catalogOverrides"
                FROM "catalogProducts"
                WHERE "catalogId" = :catalogToCloneId
            )
        `, { replacements: { catalogId: newCatalog.id, catalogToCloneId }});

        return newCatalog;
    };

    public validate(options?: ValidationOptions) {
        if (_.includes(Object.values(OwnerType), this.ownerType)) {
            return super.validate(options);
        }
        else {
            throw new ErrorResponse(StatusCodes.UNPROCESSABLE_ENTITY, `Invalid enum value "${this.ownerType}" for ownerType!`);
        }
    };

    static findAndDeleteOverrides = async (productId : number, catalogId : number, fields : ProductData[]) => {
        if (_.isEmpty(fields)) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'No overridden fields were indicated'
            )
        }

        const [
            catalog,
            product,
            catalogProduct,
        ] = await Promise.all([
            findCatalogById(catalogId),
            findProductById(productId),
            findCatalogProduct(productId, catalogId),
        ]);

        if (
            product.ownerType === catalog.ownerType
            && product.ownerId === catalog.ownerId
        ) {
            await product.clearOverrides(fields);
        }
        else {
            await catalogProduct.clearOverrides(fields);
        }

        return {
            product,
            catalogProduct,
        }
    };

    public getCatalogsWithBrandPresence = async (productStatus = [CatalogProductStatus.ACTIVE]): Promise<CatalogCarryingBrandProduct[]> => {
        if (this.ownerType !== OwnerType.BRAND) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Catalog is not a brand catalog`
            );
        }

        const products = await this.$get('products') as Product[];

        if (products.length <= 0) {
            return [];
        }

        const brandName = products[0].brandName;

        if (brandName == null) {
            return []
        }

        return await connection.query(`
        SELECT
            c."id" AS "catalogId",
            c."name" AS "catalogName",
            c."ownerType" AS "catalogOwnerType",
            ARRAY_AGG(
                DISTINCT(
                    COALESCE (p."linkedTo", sl."brandProductId", p."id")
                )
            ) AS "brandProducts"
        FROM "catalogProducts" cp
        JOIN "catalogs" c
            ON c.id = cp."catalogId"
        JOIN "products" p
            ON p.id = cp."productId"
        LEFT JOIN "suggestedLinks" sl
            ON sl."productToLinkId" = p.id
            AND sl."type" = '${MatchType.BrandSuggested}'
        WHERE SIMILARITY(p."brandName", :brandName) > :brandNameCorrelationScore
        AND p."ownerType" != '${OwnerType.BRAND}'
        AND c."ownerType" != '${OwnerType.BRAND}'
        AND cp."status" IN (:productStatus)
        GROUP BY c."id", c."name";
        `, {type: "SELECT", replacements: { brandName, productStatus, brandNameCorrelationScore }}) as CatalogCarryingBrandProduct[];
    };
}

export const defaultCatalogAttributes = [
    'id',
    'name',
    'ownerId',
    'ownerType',
    'parentCatalogId',
];

export const addProductRequirementsToCatalog = async (
    catalogId: number,
    requirementsId: number,
    childCatalogIds?: number[],
) => {
    return await transaction(async () => {
        const [
            productRequirements,
            catalog,
        ] =
        await Promise.all([
            findProductRequirementsById(requirementsId),
            findCatalogById(catalogId),
        ]);

        if (childCatalogIds) {
            const childCatalogs = await verifyAndGetChildrenCatalogs(childCatalogIds, catalogId);
            await Promise.all([
                ..._.map(childCatalogs, (catalogsToRemoveFrom) => {
                    return catalogsToRemoveFrom.$remove('productRequirements', productRequirements);
                }),
                await catalog.$add('productRequirements', productRequirements)
            ]);
        } else {
            await catalog.$add('productRequirements', productRequirements)
        }
    });
}

export const findCatalogById = async (catalogId: string | number) => {
    const catalog = await Catalog.findByPk(catalogId);

    if (catalog == null) {
        throw new ErrorResponse(StatusCodes.NOT_FOUND, `No catalog was found with the id of ${catalogId}`);
    } else {
        return catalog
    }
}

export const findChildCatalogIds = async (parentCatalogId: string): Promise<number[]> => {
    const catalogs =  await Catalog.findAll({
        attributes: ['id'],
        where: { parentCatalogId }
    });

    return _.map(catalogs, 'id');
}

export const getCatalog =  async (where: {[key: string]: string | number}, includes: string[] = [], attributes = defaultCatalogAttributes) => {
    const catalogs = await getCatalogs(where, includes, attributes);

    if ( catalogs.length == 0 ) {
        throw new ErrorResponse(StatusCodes.NOT_FOUND, `Requested catalog does not exist.`);
    }
    return catalogs[0];
}

export const getCatalogs = async (where: {[key: string]: string | number}, includes: string[] = [], attributes = defaultCatalogAttributes) => {
    const options: FindOptions = {
        attributes,
        where,
    };

    const includeOptions: IncludeOptions[] = [];

    for (const includedModel of includes) {
        if ( includedModel == 'products' ) {
            includeOptions.push({
                as: 'products',
                attributes: Product.defaultAttributes,
                model: Product,
            });
        }
        else if ( includedModel == 'childCatalogs') {
            includeOptions.push({
                as: 'childCatalogs',
                attributes: defaultCatalogAttributes,
                model: Catalog,
            })
        }
        else {
            throw new ErrorResponse(StatusCodes.BAD_REQUEST, `Requested include model ${includedModel} is not supported`);
        }
    }

    if ( includeOptions.length > 0 ) {
        options.include = includeOptions;
    }

    return await Catalog.findAll(options);
}

export const createProductRequirementsForCatalog = async (
    requirements: Partial<ProductRequirements>,
    catalogId: number
) => {
    const catalog = await findCatalogById(catalogId);

    const productRequirements = await createProductRequirements({
        ownerId: catalog.ownerId,
        ownerType: catalog.ownerType,
        ...requirements
    });

    await catalog.$add('productRequirements', productRequirements);

    return productRequirements;
}


export const addExistingProductsToCatalog = async (
    catalogId: number,
    productIds: number[],
    userInfo: UserInfo,
    catalogInformation: Record<number, Partial<CatalogProduct>> = {},
    childCatalogIds: number[] = [],
) : Promise<Product[]> => {
    return await transaction(async () => {
        const catalog = await findCatalogById(catalogId);

        const products = await Product.findAll({
            where: {
                id: productIds
            }
        });

        if (_.size(products) !== _.size(productIds)) {
            const productIdsFound = _.map(products, ({id}) => id);
            const missingProductIds = _.filter(productIds, (productId) => {
                return ! _.includes(productIdsFound, productId)
            });

            throw new ErrorResponse(
                StatusCodes.UNPROCESSABLE_ENTITY,
                `Bad Product Ids provided to add to the catalog, products ${missingProductIds}`
            )
        }

        const childCatalogs = await verifyAndGetChildrenCatalogs(childCatalogIds, catalog.id);

        await Promise.all(
            _.map([catalog, ...childCatalogs], async (catalogToTest) => {
                await Promise.all(
                    _.map(products, async (product) => {
                        return await validateProductAgainstRequirements(product, catalogToTest);
                    })
                );
            })
        );

        const productsToAddToChildrenCatalogs: number[] = [];

        const productsToReturn : Product[] = await Promise.all([
            ..._.map(products, async (product) => {
                if (product.ownerType === OwnerType.BRAND) {
                    const orgBrandProduct = await addBrandTreezProductToCatalog(catalog.id, product.id, userInfo) as Product;
                    productsToAddToChildrenCatalogs.push(orgBrandProduct.id);
                    return orgBrandProduct;
                }
                else {
                    productsToAddToChildrenCatalogs.push(product.id);
                    await addProductToCatalog(
                        catalog,
                        product.id,
                        userInfo,
                    )

                    return product;
                }
            }),
        ]);

        await Promise.all(
            _.map(childCatalogs, ({id}) => {
                return addExistingProductsToCatalog(id, productsToAddToChildrenCatalogs, userInfo, catalogInformation)
            })
        );

        return productsToReturn;
    });
}

export const verifyAndGetChildrenCatalogs = async (
    catalogIds: number [],
    parentCatalogId: number,
) : Promise<Catalog[]> => {
    const childCatalogs = await Catalog.findAll({
        where: {
            id: catalogIds,
            parentCatalogId,
        }
    });

    if (_.size(childCatalogs) < _.size(catalogIds)) {
        throw new ErrorResponse(
            StatusCodes.NOT_FOUND,
            `Child Catalog Ids were incorrect, no catalogs with those Ids could be found with the given parent Id`
        );
    }

    return childCatalogs;
}

export const removeProductsFromCatalog = async (
    catalogId: number,
    productIds: number[],
    childCatalogIds: number [] = [],
) => {
    const childCatalogs = await verifyAndGetChildrenCatalogs(childCatalogIds, catalogId);

    // TODO: why aren't changes recorded?

    return await CatalogProduct.update({
            status: CatalogProductStatus.DELETED
        },
        {
        where: {
            catalogId: [catalogId, ..._.map(childCatalogs, 'id')],
            productId: productIds,
        }
    });
}

export const removeProductRequirementsFromCatalog = async (
    catalogId: number,
    requirementsId: number,
    childCatalogIds: number[] = []
) => {
    const [
        productRequirements,
        catalog,
    ] =
    await Promise.all([
        findProductRequirementsById(requirementsId),
        findCatalogById(catalogId),
    ]);

    const childCatalogs = await verifyAndGetChildrenCatalogs(childCatalogIds, catalogId);

    await Promise.all([
        ..._.map(childCatalogs, async (catalogsToRemoveFrom) => {
            return await catalogsToRemoveFrom.$remove('productRequirements', productRequirements);
        }),
        await catalog.$remove('productRequirements', productRequirements)
    ]);
}

export const createCentralCatalog = async (
    organizationId: number,
    catalogIds: number[],
    catalogName: string,
    catalogIdToPromote?: number,
) => {
    return await transaction(async () => {
        const existingOrgCatalog = await Catalog.findAll({
            where: {
                ownerId  : organizationId,
                ownerType: OwnerType.ORG,
            }
        });

        if ( _.size(existingOrgCatalog) > 0 ) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                `Collision found with org catalog as it already exists`
            );
        }

        const centralCatalog = await Catalog.create({
            name     : catalogName,
            ownerId  : organizationId,
            ownerType: OwnerType.ORG,
        });

        //UPDATE PARENT CATALOG IDS IN CHILDREN CATALOGS
        await addStoreCatalogsToOrganization(centralCatalog.id, catalogIds);

        if (catalogIdToPromote) {
            if (!catalogIds.includes(catalogIdToPromote)) {
                throw new ErrorResponse(
                    StatusCodes.BAD_REQUEST,
                    `catalog to promote with id of ${catalogIdToPromote} must be included in the catalogIds to add to the organization`
                )
            }

            const promotedCatalog = await findCatalogById(catalogIdToPromote);

            await Product.update(
                {
                    ownerId: organizationId,
                    ownerType: OwnerType.ORG,
                },
                {
                    where: {
                        ownerId: promotedCatalog.ownerId,
                        ownerType: OwnerType.STORE,
                    }
                }
            );
        }

        return centralCatalog;
    });
}



export const addStoreCatalogsToOrganization = async (
    centralCatalogId: number,
    storeCatalogIds: number[],
) => {
    return await transaction(async () => {

        if (_.size(storeCatalogIds) < 1) {
            throw new ErrorResponse(
                StatusCodes.BAD_REQUEST,
                'No stores catalogs to add to the central catalog'
            );
        }

        //UPDATE PARENT CATALOG IDS IN CHILDREN CATALOGS

        await connection.query(`
            UPDATE "catalogs"
                SET "parentCatalogId" = :centralCatalogId
            WHERE "id" IN (:storeCatalogIds);
        `, {type: 'UPDATE', replacements: { storeCatalogIds, centralCatalogId }});

        //BRING OVER ALL THE PRODUCTS INTO THE CENTRAL CATALOG

        await connection.query(`
            INSERT INTO "catalogProducts" (
                "productId",
                "catalogId",
                "status"
            ) (
                SELECT
                    "productId",
                    :centralCatalogId,
                    CASE
                        BOOL_OR (
                            CASE cp."status"
                                WHEN '${CatalogProductStatus.ACTIVE}' THEN TRUE
                            ELSE FALSE END
                        )
                    WHEN TRUE
                        THEN '${CatalogProductStatus.ACTIVE}' ::status
                        ELSE '${CatalogProductStatus.DEACTIVATED}' ::status
                    END
                FROM
                    "catalogProducts" cp
                WHERE
                    cp."catalogId" IN (:storeCatalogIds)
                GROUP BY
                    cp."productId"
            );
        `, {
            replacements: {
                storeCatalogIds,
                centralCatalogId
            }
        });

        //ADD ALL THE PRICE TIERS THAT BELONG TO THOSE CATALOGS TO THE PARENT CATALOG VIA THE CATALOG PRICE TIER TABLE
        const catalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: storeCatalogIds,
            },
            attributes: ['priceTierId']
        });

        const priceTiersToAddToCentralCatalog = _.chain(catalogPriceTiers)
            .uniq()
            .map(catalogPriceTier => catalogPriceTier.priceTierId)
            .value();

        if (!_.isEmpty(priceTiersToAddToCentralCatalog)) {
            await CatalogPriceTier.bulkCreate(
                priceTiersToAddToCentralCatalog.map((priceTierId) => {
                    return {
                        catalogId: centralCatalogId,
                        priceTierId,
                    }
                })
            );
        }
    });
}

export const updateCatalog = async (catalogId: number, catalogEdits: Partial<Catalog>) => {
    const catalog = await getCatalog({ id: catalogId });

    return await catalog.set(
        _.pick(catalogEdits, validCatalogPatchFields)
    ).save();
}

export const findOrCreateCatalog = async (
    ownerId: number,
    ownerType: OwnerType,
    orgId?: number,
    catalogName?: string
) => {
    return await transaction(async () => {

        const name = catalogName ? catalogName : `${ownerType + ownerId} Catalog`;

        let catalog = await Catalog.findOne({
            attributes: defaultCatalogAttributes,
            where: {
                ownerId,
                ownerType,
            }
        });

        if ( catalog == null ) {
            let parentCatalogId = null;
            if (orgId) {
                let parentCatalog = await Catalog.findOne({
                    where: {
                        ownerId: orgId,
                        ownerType: OwnerType.ORG,
                    }
                });

                if (parentCatalog != null) {
                    parentCatalogId = parentCatalog.id
                }
                else {
                    parentCatalog = await Catalog.create({
                        name: catalogName || 'Central Catalog',
                        ownerId: orgId,
                        ownerType: OwnerType.ORG,
                    });

                    parentCatalogId = parentCatalog.id
                }
            }

            catalog = await Catalog.create({
                ownerId,
                ownerType,
                name,
                parentCatalogId,
            });
        }

        return catalog
    });
}
