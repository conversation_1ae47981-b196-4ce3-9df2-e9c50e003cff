import { flatten }                      from 'flat';
import { isLeft }                       from 'fp-ts/lib/Either';
import { StatusCodes }                  from 'http-status-codes';
import * as typeCodec                   from 'io-ts';
import { PathReporter }                 from 'io-ts/lib/PathReporter';
import _                                from 'lodash';
import { ProductField }                 from '@treez/commons/sharedTypings/product';
import { ErrorResponse }                from '@treez/dev-pack/errors';

export enum SearchType {
    Retail                    = 'Retail',
    BrandTreez                = 'BrandTreez',
    UnlinkedRetail            = 'UnlinkedRetail',
}

const UUID              = "uuid";
const PRICE_TIER_ID     = "priceTierId";
const SOURCE            = "source";
const UUID_WITH_BARCODE = "uuidWithBarcode";

const isValidProductField = (field: string) => (
    _.includes(ProductField, field)
)

const dateField = typeCodec.union([
    typeCodec.literal("createdAt"),
    typeCodec.literal("updatedAt"),
])

const dateRange = typeCodec.type({
    field:                  dateField,
    start:                  typeCodec.number,
    end:                    typeCodec.number,
})

const orderDirection = typeCodec.union([
    typeCodec.literal("asc"),
    typeCodec.literal("desc"),
]);

const sortBy = typeCodec.type({
    field:                  typeCodec.string,
    order:                  orderDirection,
});

/**
 * @swagger
 * components:
 *  schemas:
 *      SearchFilter:
 *          properties:
 *              field:
 *                  type: string
 *              fuzzy:
 *                  type: boolean
 *              not:
 *                  type: boolean
 *              values:
 *                  type: array
 *                  items:
 *                      type: string
 */
const searchFilter = typeCodec.type({
    field : typeCodec.string,
    fuzzy : typeCodec.boolean,
    not   : typeCodec.boolean,
    values: typeCodec.array(typeCodec.string),
});

/**
 * @swagger
 *  components:
 *      requestBodies:
 *          SearchCriteria:
 *              description: search configuration
 *              content:
 *                  application/json:
 *                      schema:
 *                          type: object
 *                          properties:
 *                              brandMatch:
 *                                  type: string
 *                              catalogId:
 *                                  type: number
 *                              dateRange:
 *                                  type: string
 *                              excludeLinkedProduct:
 *                                  type: boolean
 *                              filters:
 *                                  type: array
 *                                  items:
 *                                      $ref: '#/components/schemas/SearchFilter'
 *                              include:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              offset:
 *                                  type: number
 *                              orderByRank:
 *                                  type: boolean
 *                              orgId:
 *                                  type: number
 *                              page:
 *                                  type: number
 *                              pageSize:
 *                                  type: number
 *                              productIds:
 *                                  type: array
 *                                  items:
 *                                      type: number
 *                              searchBrands:
 *                                  type: boolean
 *                              searchString:
 *                                  type: string
 *                              sortBy:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              status:
 *                                  type: array
 *                                  items:
 *                                      type: string
 *                              weights:
 *                                  type: string
 */
export const searchCriteria = typeCodec.partial({
    brandMatch:             typeCodec.string,
    catalogId:              typeCodec.number,
    dateRange:              dateRange,
    searchType:             typeCodec.string,
    excludeLinkedProduct:   typeCodec.boolean,
    filters:                typeCodec.array(searchFilter),
    include:                typeCodec.array(typeCodec.string),
    offset:                 typeCodec.number,
    orderByRank:            typeCodec.boolean,
    orgId:                  typeCodec.number,
    page:                   typeCodec.number,
    pageSize:               typeCodec.number,
    productIds:             typeCodec.array(typeCodec.number),
    searchBrands:           typeCodec.boolean,
    searchString:           typeCodec.string,
    sortBy:                 typeCodec.union([sortBy, typeCodec.array(sortBy)]),
    status:                 typeCodec.array(typeCodec.string),
    weights:                typeCodec.string,
});

export type SearchCriteria = typeCodec.TypeOf<typeof searchCriteria>;

export type SearchFilter = typeCodec.TypeOf<typeof searchFilter>;

export const parseAttribute = ({field}: SearchFilter): null | string => {
    const matchedAttribute = field.match(/^attribute\.(.+)/);
    return matchedAttribute && matchedAttribute[1];
}

export const parseDetail = ({field}: SearchFilter): null | string => {
    const matchedDetail = field.match(/^details\.(.+)/);
    return matchedDetail && matchedDetail[1];
}

export const isValidFilterField = (field: string) => (
    field === UUID
    || field === PRICE_TIER_ID
    || field === SOURCE
    || field === UUID_WITH_BARCODE
    || isValidProductField(field)
);

export const validateAndFormatCriteria = (criteria: SearchCriteria) => {
    const validationResult = searchCriteria.decode(criteria);
    const errorReport = PathReporter.report(validationResult);

    if (isLeft(validationResult)) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, errorReport.join(','));
    }

    const search = validationResult.right;

    if (search.searchString) {
        if (search.searchString.trim().length === 0) {
            search.searchString = '';
        }
        else {
            // Translation: Put a slash in front of any characters that would break the query
            search.searchString = `${search.searchString}`.replace(/[&)(!:|<]/g, '\\$&');
        }
    }

    // Because empty but extant filters screw everything up
    if (search.filters) {
        search.filters.forEach((filter) => {
            if((parseAttribute(filter) || parseDetail(filter)) && !filter.fuzzy) {
                return;
            }
            if (isValidFilterField(filter.field) == false ) {
                throw new ErrorResponse(StatusCodes.BAD_REQUEST, `Invalid filter field ${filter.field}`);
            }
        });
    }

    if (search.sortBy) {
        let isValidSortBy = true;

        if (Array.isArray(search.sortBy)) {
            const sortFieldValidityAssesment = search.sortBy.map(({field}) => isValidProductField(field));

            isValidSortBy = !_.includes(sortFieldValidityAssesment, false)
        }
        else {
            isValidSortBy = isValidProductField(search.sortBy.field);
        }

        if (!isValidSortBy) {
            throw new ErrorResponse(StatusCodes.BAD_REQUEST, `Invalid sort field`);

        }
    }

    return search;
}

export const applyOverrides = (product: any) => {
    const flattenWrapper = (value: any) => {
        return flatten(value || {} , { safe: true })
    }

    return Object.assign(
        {},
        flattenWrapper(_.get(product, 'dataValues')),
        flattenWrapper(_.get(product, 'dataValues.productOverrides')),
        flattenWrapper(_.get(product, 'dataValues.catalogOverrides')),
        {
            baseProduct: _.get(product, 'dataValues'),
        },
    );
}
