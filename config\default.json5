{
    auth: {
        hostname: 'ssm:/app/auth/env/dev/hostname',
        verify: {
            audience: 'ssm:/app/auth/env/dev/verify/audience',
            issuer: 'ssm:/app/auth/env/dev/verify/issuer',
            keySetUrl: 'ssm:/app/auth/env/dev/verify/keyseturl',
        }
    },
    aws: {
        region: 'us-west-2',
    },
    db: {
        host: 'localhost',
        name: 'product-api',
        namespace: 'product-api',
        pool: {
            max: 10,
            min: 0,
            acquire: 30000,
            idle: 10000,
        },
    },
    logger: {
        name: 'product-api',
        level: 'debug',
    },
    monitoring: {
        elastic: {
            apm: {
                config: {
                    secretToken: 'ssm:/apps/elastic/apm/secret-token',
                    serverUrl:   'https://apm.build.treez.io',
                    transactionIgnoreUrls: [ '/health-check-ping' ],
                    serviceName: 'product-api',
                },
                enabled: false,
            },
        },
    },
    pulsar: {
        compression: 'LZ4',
        serviceUrl: 'pulsar://pulsar-broker-preprod.treez.io:6650',
        unpublishedMessagesDelay: 5000,
    },
    search: {
        maxPageSize: 1000,
    },
    server: {
        port: 8303,
        uploadLimit: '5mb',
        uploadRoutes: [
            '/products/import',
            '/products/import/:storeId',
        ],
    },
}
