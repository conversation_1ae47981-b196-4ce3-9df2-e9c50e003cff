import { QueryInterface }   from 'sequelize';
import {
    stringField,
    notNull,
}                           from '@treez/dev-pack/db/fieldTypes';

//Tables
const externalReferences = "externalReferences";

//Columns ~ Fields
const description = 'description';
const type        = 'type';



export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            UPDATE "externalReferences"
            SET description = 'sellTreezId'
            WHERE description IS NULL;
        `);

        await queryInterface.changeColumn(externalReferences, description, notNull(stringField));
        await queryInterface.renameColumn(externalReferences, description, type);
    },
    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.renameColumn(externalReferences, type, description);
        await queryInterface.changeColumn(externalReferences, description, stringField);
    },
}
