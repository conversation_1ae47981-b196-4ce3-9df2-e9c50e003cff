import { QueryInterface }   from 'sequelize';

//Tables
const externalReferences = "externalReferences";

//Columns ~ Fields
const ownerId = 'ownerId';
const ownerIdIndex = 'ext_ref_ownerId_idx';


export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.addIndex(externalReferences, [ownerId], {
            name: ownerIdIndex
        })
    },
    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.removeIndex(externalReferences, ownerIdIndex)
    },
}
