import _                            from 'lodash';
import {
    OwnerType,
    UOM,
}                                   from '@treez/commons/sharedTypings/product';
import Assortment, {
    AssortmentType
}                                   from '../../models/Assortment';
import Catalog                      from '../../models/Catalog';
import AssortmentProduct            from '../../models/AssortmentProduct';
import Product                      from '../../models/Product';

describe('Assortment model', () => {
    it(`Create a new assortment`, async () => {
        const [
            catalog,
        ] = await Promise.all([
            Catalog.create({
                name:               "Test Org Catalog",
                ownerId:            55,
                ownerType:          OwnerType.ORG,
                parentCatalogId:    null,
            }),
        ]);

        const newAssortment: Partial<Assortment> = {
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        }

        const assortment = await Assortment.saveAssortment(newAssortment, catalog.id);

        expect(assortment.id).toBeDefined();
    });

    it(`Updates an assortment`, async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        await Assortment.updateAssortment(assortment.id, catalog.id, {
            name: 'Example 2'
        });

        const updatedAssortment = await Assortment.findAssortmentById(assortment.id, catalog.id);

        expect(updatedAssortment.name).toEqual('Example 2');
    });

    it(`Remove an assortment with the dependencies`, async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const assortment = await Assortment.create({
            catalogId: catalog.id,
            type: AssortmentType.COLLECTION,
            name: 'Example',
            icon: 'example',
            order: 1
        });

        const product = await Product.create({
            amount:             "1.000000",
            brandName:          "Hastings' Herb",
            displayName:        "Wade's Wonder Weed",
            eCommerceName:      "Wade's Wonder Weed",
            name:               "Wade's Wonder Weed",
            ownerId:            1,
            ownerType:          OwnerType.ORG,
            price:              "25.000000",
            size:               "Dime Baggie",
            subtype:            "Sungrown",
            type:               "Flower",
            uom:                UOM.EACH,
        });

        await AssortmentProduct.create({
            productId: product.id,
            assortmentId: assortment.id
        });

        await Assortment.removeAssortment(assortment.id, catalog.id);

        const assortmentProducts = await AssortmentProduct.findAll({
            where: {
                assortmentId: assortment.id
            }
        });
        let deletedAssortment = null;

        try {
            deletedAssortment = await Assortment.findAssortmentById(assortment.id, catalog.id);
        } catch (error) {
            expect(error.message).toEqual(`Assortment with the id of ${assortment.id} could not be found`);
        } finally {
            expect(deletedAssortment).toBeNull();
            expect(assortmentProducts.length).toEqual(0);
        }
    });

    it('Creates a collection after catalog creation', async () => {
        const catalog = await Catalog.create({
            name:               "Test Org Catalog",
            ownerId:            55,
            ownerType:          OwnerType.ORG,
            parentCatalogId:    null,
        });

        const assortments = await Assortment.findAll({
            where: {
                catalogId: catalog.id
            }
        });

        expect(assortments).toHaveLength(1);
    });
});
