import { QueryInterface }   from 'sequelize';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = '510 THREAD'
            WHERE
                "subtype" = 'DIAMOND'
                OR
                "subtype" = 'DIAMOND LINE'
                OR
                "subtype" = 'GEM'
                OR
                "subtype" = 'GEM LINE';
        `);

        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = 'PAX ERA POD'
            WHERE
                "subtype" = 'PAX'
                OR
                "subtype" = 'PAX POD';
        `);

        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = 'READY TO USE'
            WHERE
                "subtype" = 'DISPOSABLE';
        `);

        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = 'ROSIN'
            WHERE
                "subtype" = '1ST PRESS ROSIN'
                OR
                "subtype" = '2ND PRESS ROSIN'
                OR
                "subtype" = 'L.S. 1ST PRESS ROSIN';
        `);

        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = 'MINTS'
            WHERE
                "subtype" = 'MICRO-DOSE MINTS';
        `);

        await queryInterface.sequelize.query(`
            UPDATE "products"
            SET
                "subtype" = NULL
            WHERE
                "subtype" = 'SAUCE'
                OR
                "subtype" = 'TINCTURE'
                OR
                "subtype" = 'CAPSULE'
                OR
                "subtype" = 'TOPICAL'
                OR
                "subtype" = 'PERSY LIVING SOIL'
                OR
                "subtype" = 'TEMPLE BALL HASH'
                OR
                "subtype" = 'WPFF';
        `);
    },
    down: async function ( queryInterface: QueryInterface) {
        return;
    },
}
