import { Router }                            from 'express';
import catalogs                              from '../../routes/catalogs';
import catalogProducts                       from '../../routes/catalogProducts';
import importRoutes                          from '../../routes/import';
import operationRoutes                       from '../../routes/operations';
import pricetier                             from '../../routes/pricetiers';
import productRequirementsRoutes             from '../../routes/productRequirements';
import products                              from '../../routes/products';
import publicRoutes                          from '../../routes/public';
import fileUpload                            from 'express-fileupload';


const testRoutes = Router();


testRoutes.use(fileUpload({
    limits: { fileSize: 150 * 1024 * 1024 },
    useTempFiles: true,
    tempFileDir : '/tmp/'
}));
testRoutes.use('/catalog_products', catalogProducts);
testRoutes.use('/catalogs', catalogs);
testRoutes.use('/imports', importRoutes);
testRoutes.use('/pricetier', pricetier);
testRoutes.use('/productRequirements', productRequirementsRoutes);
testRoutes.use('/products', products);
testRoutes.use('/public', publicRoutes);
testRoutes.use('/operations', operationRoutes);

export default testRoutes;

