import { QueryInterface }               from 'sequelize';
import {
    autoP<PERSON><PERSON>ield,
    createUpdateColumns,
    enumField,
    jsonField,
    notNull,
    stringField,
    foreignKey,
    intField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { ProductAction }                from '../../lib/sharedInterfaces';

export = {
    up: async function( queryInterface: QueryInterface ) {
        await queryInterface.createTable('productChanges', {
            id:                         notNull(autoPKField),
            actionType:                 notNull(enumField(ProductAction)),
            newProduct:                 jsonField,
            oldProduct:                 jsonField,
            productId:                  foreignKey(intField, 'products', 'id'),
            userAuthId:                 notNull(stringField),

            ...createUpdateColumns
        });

        await queryInterface.sequelize.query(`
            CREATE INDEX "productId"
            ON "productChanges" ("productId");
        `);
    },
    down: async function( queryInterface: QueryInterface ) {
        await queryInterface.removeIndex("productChanges", "productId");
        await queryInterface.dropTable("productChanges");
    }
};
