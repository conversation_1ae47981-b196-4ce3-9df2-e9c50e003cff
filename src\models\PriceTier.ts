import Ajv                          from 'ajv';
import {
    AutoIncrement,
    BeforeCreate,
    BeforeUpdate,
    BelongsToMany,
    Column,
    De<PERSON>ult,
    HasMany,
    Is,
    PrimaryKey,
    Table,
}                                   from 'sequelize-typescript';
import { Op }                       from 'sequelize';
import { StatusCodes }              from 'http-status-codes';
import _                            from 'lodash';
import { OwnerType }                from '@treez/commons/sharedTypings/product';
import {
    PriceTierMethod,
    PriceTierSearchParams,
    PriceTierThreshold,
    PriceTierThresholdType,
    RangeMode
}                                   from '@treez/commons/sharedTypings/priceTier';
import { ErrorResponse }            from '@treez/dev-pack/errors';
import TreezModel                   from '@treez/dev-pack/models/TreezBaseModel';
import {
    intField,
    jsonField,
    notNull,
}                                   from '@treez/dev-pack/db/fieldTypes';
import CatalogPriceTier             from './CatalogPriceTier';
import Catalog, {
    findCatalogById,
    findChildCatalogIds,
}                                   from './Catalog';
import PriceTierChange, {
    PriceTierAction,
}                                   from './PriceTierChange';
import {updateProductsInTier}       from "../lib/calculatePrice";
import CatalogProduct               from './CatalogProduct';

export const validatePriceTier = (priceTier: Partial<PriceTier>) => {
    validateLabel(priceTier.label);

    if(_.isEmpty(priceTier)){
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            'Price tier cannot be empty'
        );
    }


    const priceTierValidation = new Ajv({
        coerceTypes: true,
    });

    const priceTierValidity = priceTierValidation.validate(
        PriceTier.tierSchema,
        priceTier
    );

    if(priceTierValidity === false){
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Price Tier is in an incorrect format ${priceTierValidation.errorsText()}`
        );
    }

    const {
        thresholds = []
    } = priceTier;

    // validate the thresholds as well, to make sure this whole entity is valid.
    return validateThresholds(thresholds);
}

export const validateThresholds = (thresholds: PriceTierThreshold[]) : boolean => {
    if(thresholds == null || thresholds.length < 1){
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Price Tier Threshold was in an incorrect format`
        );
    }

    const priceThresholdsValidation = new Ajv({
        coerceTypes: false
    });

    const thresholdsValidity = priceThresholdsValidation.validate(
        PriceTier.priceTierThresholdsSchema,
        thresholds,
    );

    if(thresholdsValidity === false){
        throw new ErrorResponse(
            StatusCodes.BAD_REQUEST,
            `Price Tier Threshold was in an incorrect format ${priceThresholdsValidation.errorsText()}`
        );
    }

    return true;
}

export const validateLabel = (labelToValidate: any) => {
    const isValidLabel = _.isString(labelToValidate) && labelToValidate.length !== 0;

    if (!isValidLabel) {
        throw new ErrorResponse(StatusCodes.BAD_REQUEST, 'Label Must not be Empty or Null');
    }
}

const orderThresholdsComparator = (threshold1 : PriceTierThreshold, threshold2: PriceTierThreshold) : number => {
    return threshold1.start - threshold2.start;
};

@Table({tableName: 'priceTiers'})
export default class PriceTier extends TreezModel<PriceTier>{
    @AutoIncrement
    @PrimaryKey
    @Column
    id:                                 number;

    @Column
    label:                              string;

    @Column(notNull(intField))
    ownerId:                            number;

    @Column
    ownerType:                          OwnerType;

    @Default(true)
    @Column
    isActive:                           boolean;

    @Default(PriceTierMethod.WEIGHT)
    @Column
    method:                             PriceTierMethod;

    @Default(RangeMode.FIXED_PRICE)
    @Column
    rangeMode:                          RangeMode;

    @Default(PriceTierThresholdType.FLAT)
    @Column
    thresholdType:                      PriceTierThresholdType;

    @Is('validateThresholds', (value) => {
        validateThresholds(value)
    })
    @Column(jsonField)
    thresholds:                          PriceTierThreshold[];

    @BelongsToMany(() => Catalog, () => CatalogProduct)
    catalogs:                           Catalog[];

    @HasMany(() => CatalogPriceTier)
    catalogPriceTiers:                  CatalogPriceTier[]

    @BeforeCreate
    @BeforeUpdate
    static async orderThresholds(instance : PriceTier) {
        if (!_.isEmpty(instance.thresholds)) {
            const orderedThresholds = instance.thresholds.sort(orderThresholdsComparator);

            instance.set('thresholds', orderedThresholds);
        }
    }

    static async searchForPriceTiers(
        searchParams: Omit<PriceTierSearchParams,'catalogId'>
    ) : Promise<PriceTier[]>
    {
        const {
            searchString,
            filters
        } = searchParams;

        if(_.isEmpty(searchString) && _.isEmpty(filters)){
            return [];
        }

        let where: any = {};

        if(!_.isEmpty(searchString)){
            where = {
                [Op.like]: searchString
            }
        }

        const finalWhereClause = {
            ...where,
            [Op.and] : filters.map(filter => ({
                [filter.key] : {
                    [Op.in] : filter.values
                }
            }))
        }

        const priceTiers = await PriceTier.findAll({
            where: {
                ...finalWhereClause
            }
        });

        return priceTiers;
    }

    static async createPriceTier (
        newPriceTier: Partial<PriceTier>,
        catalog: Catalog,
        userInfo: UserInfo,
    ): Promise<PriceTier> {

        const priceTier = await PriceTier.create({
            ...newPriceTier,
            ownerId:   catalog.ownerId,
            ownerType: catalog.ownerType,
        });

        await CatalogPriceTier.create({
            catalogId:   catalog.id,
            priceTierId: priceTier.id,
        })

        let parentCatalog;
        if (catalog.parentCatalogId) {
            parentCatalog = await findCatalogById(catalog.parentCatalogId);
        }

        if (parentCatalog) {
            await CatalogPriceTier.create({
                catalogId:   parentCatalog.id,
                priceTierId: priceTier.id,
            })
        }

        const childCatalogIds = await findChildCatalogIds(String(catalog.id));
        if (!_.isEmpty(childCatalogIds)) {
            await PriceTier.addCatalogPriceTierToCatalogs(
                priceTier.id,
                childCatalogIds
            );
        }

        await PriceTierChange.recordPriceTierChange(
            priceTier.id,
            PriceTierAction.CREATE,
            userInfo,
            priceTier
        );

        return priceTier;
    }

    static async updatePriceTier (
        priceTierId: number,
        catalogId: number,
        priceTierUpdate: Partial<PriceTier>,
        userInfo: UserInfo,
    ): Promise<PriceTier> {
        const catalogPriceTier = await CatalogPriceTier.findOne({
            where: {
                catalogId: catalogId,
                priceTierId: priceTierId,
            },
            include: [{
                model: PriceTier,
            }]
        });

        if (!catalogPriceTier) {
            throw new ErrorResponse(
                StatusCodes.NOT_FOUND,
                `Could not find PriceTier in catalog for id: ${priceTierId}`
            );
        }

        const oldPriceTier = catalogPriceTier.priceTier;

        const validPriceTierUpdates = _.pick(priceTierUpdate, [
            'label',
            'isActive',
            'method',
            'rangeMode',
            'thresholdType',
            'thresholds',
        ]);

        const oldPriceTierSaved : Partial<PriceTier> = {
            ...oldPriceTier.get()
        };

        const updatedPriceTier = await oldPriceTier.update(validPriceTierUpdates);

        //Handling converting store price tier into org price tier
        if (    oldPriceTierSaved.ownerType == OwnerType.STORE
            &&  updatedPriceTier.ownerType == OwnerType.ORG
        ) {
            const childCatalogIds = await findChildCatalogIds(String(catalogId));

            if (!_.isEmpty(childCatalogIds)) {
                await PriceTier.addCatalogPriceTierToCatalogs(
                    priceTierId,
                    childCatalogIds
                );
            }
        }

        await PriceTierChange.recordPriceTierChange(
            oldPriceTier.id,
            PriceTierAction.UPDATE,
            userInfo,
            updatedPriceTier,
            oldPriceTierSaved
        );

        await updateProductsInTier(priceTierId)

        return updatedPriceTier;
    }

    static async addCatalogPriceTierToCatalogs(
        priceTierId: number,
        catalogIds: number[],
    ) {
        const existingCatalogPriceTiers = await CatalogPriceTier.findAll({
            where: {
                catalogId: {
                    [Op.in]: catalogIds
                },
                priceTierId: priceTierId
            }
        });

        const catalogIdsWithExistingCatalogPriceTier = _.map(existingCatalogPriceTiers, catalogPriceTier => {
            return _.get(catalogPriceTier, 'catalogId');
        });

        catalogIds.forEach(async catalogId => {
            if (catalogIdsWithExistingCatalogPriceTier.includes(catalogId) == false) {
                await CatalogPriceTier.create({
                    catalogId,
                    priceTierId,
                })
            }
        })
    }

    static priceTierThresholdsSchema = {
        "type ": "array",
        "items" : {
            "type": "object",
            "properties": {
                "value":{
                    "type": "number",
                    "minimum": 0,
                },
                "start": {
                    "type": "number",
                    "minimum": 0,
                },
                "end": {
                    "type": ["number", "null"],
                    "minimum": 0
                },
                "tolerance": {
                    "type": ["number", "null"],
                    "minimum": 0
                }
            },
            "required": ["value", "start" ]
        },
    }

    static tierSchema = {
        "type": "object",
        "properties" : {
            "label" : {
                "type" : "string",
            },
            "ownerId": {
                "type" : "number",
            },
            "ownerType" : {
                "type" : "string",
            },
            "isActive": {
                "type" : "boolean"
            },
            "method": {
                "type" : "string"
            },
            "rangeMode": {
                "type" : "string"
            },
            "thresholdType": {
                "type" : "string"
            },
            "thresholds": {
                "type" : "array"
            },
        },
        "required": [
            "label",
            "isActive",
            "method",
            "rangeMode",
            "thresholdType",
            "thresholds"
        ]
    }
}
