import { QueryInterface }                from 'sequelize';
import {
    autoPKField,
    intField,
    stringField,
    foreignKey,
    createUpdateColumns,
    enumField,
    notNull,
    booleanField,
}                                        from '@treez/dev-pack/db/fieldTypes';
import {
    AssortmentType,
    AssortmentStatus
}                                        from '../../models/Assortment';

const assortmentId                                      = "assortmentId";
const assortments                                       = "assortments";
const assortmentProducts                                = "assortmentProducts";
const id                                                = "id";
const catalogIdIndexOnAssortments                       = "assortments_catalog_id_idx";
const orderAscIndexOnAssortments                        = "assortments_order_asc_idx";
const catalogIdAndNameUniqueConstraintOnAssortments     = "assortments_catalog_id_name_unique_constraint";
const productIdIndexOnAssortmentProducts                = "assortment_products_product_id_idx";
const assortmentIdIndexOnAssortmentProducts             = "assortment_products_assortment_id_idx";
const catalogId                                         = 'catalogId'
const productId                                         = 'productId'
const products                                          = "products";
const catalogs                                          = "catalogs";
const order                                             = "order";
const name                                              = "name";
const unique                                            = "unique";


export = {
    up: async function( queryInterface: QueryInterface) {
        // assortments
        await queryInterface.createTable(assortments, {
            id          : autoPKField,
            catalogId   : notNull(foreignKey(intField, catalogs, id)),
            type        : notNull(enumField(AssortmentType)),
            name        : notNull(stringField),
            icon        : stringField,
            order       : notNull(intField),
            principal   : notNull(booleanField),
            status      : notNull(enumField(AssortmentStatus)),
            ...createUpdateColumns,
        });

        await queryInterface.addIndex(assortments, [catalogId], {
            name: catalogIdIndexOnAssortments
        });

        await queryInterface.addIndex(assortments, [order], {
            name: orderAscIndexOnAssortments,
            fields: [order]
        });

        await queryInterface.addConstraint(assortments, {
            fields: [catalogId, name],
            type: unique,
            name: catalogIdAndNameUniqueConstraintOnAssortments,
        });

        // assortmentProducts
        await queryInterface.createTable(assortmentProducts, {
            id             : autoPKField,
            productId      : notNull(foreignKey(intField, products, id)),
            assortmentId   : notNull(foreignKey(intField, assortments, id)),
            ...createUpdateColumns,
        });

        await queryInterface.addIndex(assortmentProducts, [productId], {
            name: productIdIndexOnAssortmentProducts
        });

        await queryInterface.addIndex(assortmentProducts, [assortmentId], {
            name: assortmentIdIndexOnAssortmentProducts
        });

        // insert Featured collection for each catalog
        await queryInterface.sequelize.query(`
            INSERT INTO "assortments" ("catalogId", "type", "name", "icon", "order", "principal", "status")
            SELECT
                catalogs.id AS catalogId,
                'COLLECTION' as type, 
                'Featured' as name,
                'featured_star.svg' as icon,
                0 as order,
                true as principal,
                'ACTIVE' as status
            FROM catalogs;
        `);
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.dropTable(assortments);
        await queryInterface.dropTable(assortmentProducts);
    },
}
