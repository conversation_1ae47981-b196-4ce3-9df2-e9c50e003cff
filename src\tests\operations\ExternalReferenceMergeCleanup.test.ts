import {
    UOM,
    OwnerType,
}                               from '@treez/commons/sharedTypings/product';
import Product                  from '../../models/Product';
import Catalog                  from '../../models/Catalog';
import CatalogProduct           from '../../models/CatalogProduct';
import {
    ExternalReferenceType
}                               from '../../models/ExternalReference';
import {
    createMergeProductUpdateOutbox,
    createMergeProductUpdateOutboxes,
}                               from '../../operations/ExternalReferenceMergeCleanup';
import { TypeOfProductChanges } from '../../pulsar/producer';

describe('/ExternalReferenceMergeCleanup Operation Test', () => {

    it('test createMergeProductUpdateOutboxes', async () => {
        const [
            product1,
            product2,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "<PERSON>'s Cookies",
                name         : "<PERSON>'s Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Product.create({
                amount       : "1.000000",
                brandName    : "B",
                eCommerceName: "Bob's Cookies",
                name         : "Bob's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test ORG Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.BRAND,
                parentCatalogId: null,
            })
        ])

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product1.id,
            price:     20,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product2.id,
            price:     20,
        });

        await Promise.all([
            await product1.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'ABC'),
            await product2.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'UVW'),
        ]);

        await product1.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'DEF');
        await product2.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'XYZ');

        const result = await createMergeProductUpdateOutboxes([product1, product2]);

        expect(result.length).toBe(2);
        expect(result[0].payload.changedProductId).toStrictEqual(product1.id);
        expect(result[0].payload.affectedCatalogIds).toStrictEqual([catalog.id]);
        expect(result[0].payload.affectedSellTreezIds).toStrictEqual(['ABC']);
        expect(result[0].payload.changeType).toBe(TypeOfProductChanges.MERGE);

        expect(result[1].payload.changedProductId).toStrictEqual(product2.id);
        expect(result[1].payload.affectedCatalogIds).toStrictEqual([catalog.id]);
        expect(result[1].payload.affectedSellTreezIds).toStrictEqual(['UVW']);
        expect(result[1].payload.changeType).toBe(TypeOfProductChanges.MERGE);
    });

    it('test createMergeProductUpdateOutbox - Organization Product', async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test ORG Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.BRAND,
                parentCatalogId: null,
            })
        ])

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product.id,
            price:     20,
        });

        await Promise.all([
            await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'ABC'),
            await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'DEF'),
        ]);

        await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'XYZ');

        const result = await createMergeProductUpdateOutbox(product);

        expect(result.payload.changedProductId).toBe(product.id);
        expect(result.payload.affectedCatalogIds).toStrictEqual([catalog.id]);
        expect(result.payload.affectedSellTreezIds.sort()).toStrictEqual(['ABC', 'DEF']);
        expect(result.payload.changeType).toBe(TypeOfProductChanges.MERGE);
    });

    it('test createMergeProductUpdateOutbox - Store Product', async () => {
        const [
            product,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.STORE,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test Store Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.STORE,
                parentCatalogId: null,
            }),
        ])

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: product.id,
            price:     20,
        });

        await Promise.all([
            await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'ABC'),
            await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'DEF'),
        ]);

        await product.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'XYZ');

        const result = await createMergeProductUpdateOutbox(product);

        expect(result.payload.changedProductId).toBe(product.id);
        expect(result.payload.affectedCatalogIds).toStrictEqual([catalog.id]);
        expect(result.payload.affectedSellTreezIds.sort()).toStrictEqual(['ABC', 'DEF']);
        expect(result.payload.changeType).toBe(TypeOfProductChanges.MERGE);
    });

    it('test createMergeProductUpdateOutbox accounting for merge styles - [externalReferenceStyleMerge, mergeToStyleMerge]', async () => {
        const [
            orgProduct,
            catalog,
        ] = await Promise.all([
            Product.create({
                amount       : "1.000000",
                brandName    : "A",
                eCommerceName: "Alice's Cookies",
                name         : "Alice's Cookies",
                ownerId      : 1,
                ownerType    : OwnerType.ORG,
                size         : "LARGE",
                subtype      : "OTHER",
                type         : "EDIBLE",
                uom          : UOM.EACH,
            }),
            Catalog.create({
                name           : "Test ORG Catalog",
                ownerId        : 1,
                ownerType      : OwnerType.BRAND,
                parentCatalogId: null,
            })
        ])

        const storeProduct = await Product.create({
            amount       : "1.000000",
            brandName    : "B",
            eCommerceName: "Bob's Bacon",
            name         : "Bob's Bacon",
            ownerId      : 1,
            ownerType    : OwnerType.STORE,
            size         : "LARGE",
            subtype      : "OTHER",
            type         : "EDIBLE",
            uom          : UOM.EACH,
            mergedTo     : orgProduct.id,
        });

        await CatalogProduct.create({
            catalogId: catalog.id,
            productId: orgProduct.id,
            price:     20,
        });

        await Promise.all([
            //externalReferenceStyleMerge
            await orgProduct.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'ABC'),
            await orgProduct.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'DEF'),

            //mergeToStyleMerge
            await storeProduct.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'GHI'),
        ]);

        await orgProduct.addExternalReference(ExternalReferenceType.SELL_TREEZ_ID, 'XYZ');

        const result = await createMergeProductUpdateOutbox(orgProduct);

        expect(result.payload.changedProductId).toBe(orgProduct.id);
        expect(result.payload.affectedCatalogIds).toStrictEqual([catalog.id]);
        expect(result.payload.affectedSellTreezIds.sort()).toStrictEqual(['ABC', 'DEF', 'GHI']);
        expect(result.payload.changeType).toBe(TypeOfProductChanges.MERGE);
    });

});
