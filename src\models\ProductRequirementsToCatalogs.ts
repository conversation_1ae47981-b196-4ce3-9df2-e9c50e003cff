import _                            from 'lodash';
import {
    AutoIncrement,
    BelongsTo,
    Column,
    NotNull,
    PrimaryKey,
    Table,
    ForeignKey,
}                                   from 'sequelize-typescript';
import {
    intField,
    notNull,
}                                   from '@treez/dev-pack/db/fieldTypes';
import { TreezModel }               from '@treez/dev-pack/models/TreezBaseModel';
import Catalog                      from './Catalog';
import ProductRequirements          from './ProductRequirements';

@Table({tableName: 'productRequirementsToCatalogs'})
export default class ProductRequirementsToCatalogs extends TreezModel<ProductRequirementsToCatalogs> {
    @AutoIncrement
    @PrimaryKey
    @Column(intField)
    id:                                     number;

    @NotNull
    @ForeignKey(() => ProductRequirements)
    @Column(notNull(intField))
    productRequirementsId:                   number;

    @NotNull
    @ForeignKey(() => Catalog)
    @Column(notNull(intField))
    catalogId:                              number;

    @BelongsTo(() => Catalog)
    catalog:                                Catalog;

    @BelongsTo(() => ProductRequirements)
    productRequirements:                    ProductRequirements;
}
