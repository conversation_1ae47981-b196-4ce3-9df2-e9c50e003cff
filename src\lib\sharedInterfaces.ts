export enum ValidationType {
    ARRAY   = 'array',
    BOOLEAN = 'boolean',
    INTEGER = 'integer',
    NULL    = 'null',
    NUMBER  = 'number',
    OBJECT  = 'object',
    STRING  = 'string',
}

export enum CatalogProductAction {
    CREATE      = 'CREATE',
    DEACTIVATE  = 'DEACTIVATE',
    DELETE      = 'DELETE',
    UPDATE      = 'UPDATE',
};

export enum ProductAction {
    CREATE  = 'CREATE',
    DELETE  = 'DELETE',
    LINK    = 'LINK',
    MERGE   = 'MERGE',
    UNLINK  = 'UNLINK',
    UPDATE  = 'UPDATE',
};
