import { QueryInterface }               from 'sequelize';
import { 
    decimalField,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';

const AMOUNT        = "amount";
const BRAND_NAME    = "brandName";
const PRODUCTS      = "products";

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.sequelize.transaction( (t) => Promise.all([
            queryInterface.changeColumn(PRODUCTS, AMOUNT, {
                ...decimalField,
                allowNull: true,
            }),
            queryInterface.changeColumn(PRODUCTS, BRAND_NAME, {
                ...stringField,
                allowNull: true,
            }),
        ]));
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.transaction( (t) => Promise.all([
            queryInterface.changeColumn(PRODUCTS, AMOUNT, {
                ...decimalField,
                allowNull: false,
            }),
            queryInterface.changeColumn(PRODUCTS, BRAND_NAME, {
                ...stringField,
                allowNull: false,
            }),
        ]));
    },
}
