import {
    ProductAttributesField,
    ProductData,
    ProductDetailsField,
}                         from '@treez/commons/sharedTypings/product';
import _                  from 'lodash';
import { ProductField }             from '@treez/commons/sharedTypings/product';
import { TreezModel }               from '@treez/dev-pack/models/TreezBaseModel';

export enum MeasurementType {
    FLUID         = 'fluid',
    PACKAGED_GOOD = 'packaged_good',
    WEIGHT        = 'weight',
}

export const getProductFieldPaths = (fields : ProductData[]) => {
    return _.map(fields, field => {
        if (_.includes(ProductAttributesField, field)) {
            return `attributes.${field}`;
        }
        else if (_.includes(ProductDetailsField, field)) {
            return `details.${field}`;
        }
        else {
            return field;
        }
    })
};

const DECIMALS_TO_PARSE: Set<string> = new Set([
    ProductField.AMOUNT,
    ProductField.MSRP,
    ProductField.PRICE,
]);

/**
 * Recursively parses known numeric fields into float.
 *
 * @param model TreezModel with numeric fields serialized as string to be parsed
 */
export const parseNumericFields = <M extends TreezModel | Record<string, any>>(model: M): Record<string, any> => {
    return _(model.get?.() || model)
        .mapValues((val, key) => {
            if (_.isObject(val) && !_.isArray(val)) {
                return parseNumericFields(val);
            }

            return DECIMALS_TO_PARSE.has(key) && _.isFinite(Number(val))
                ? parseFloat(val)
                : val
        }).value();
};
