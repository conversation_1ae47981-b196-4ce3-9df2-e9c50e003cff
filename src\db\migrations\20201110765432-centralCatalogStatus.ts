import { QueryInterface }   from 'sequelize';
import { CatalogProductStatus } from '@treez/commons/sharedTypings/product';

export = {
    up: async function(queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
        WITH children_catalog_products AS (
            SELECT
                child_catalog_product."productId",
                child_catalog."parentCatalogId",
                CASE
                    BOOL_OR(
                        CASE child_catalog_product."status" WHEN '${CatalogProductStatus.ACTIVE}' THEN TRUE ELSE FALSE END
                    )
                WHEN TRUE THEN '${CatalogProductStatus.ACTIVE}' ELSE '${CatalogProductStatus.DEACTIVATED}' END AS "childrenCollectiveStatus"
            FROM "catalogProducts" child_catalog_product
            LEFT JOIN "catalogs" child_catalog ON child_catalog_product."catalogId" = child_catalog."id"
            WHERE child_catalog."parentCatalogId" IS NOT NULL
            GROUP BY child_catalog_product."productId", child_catalog."parentCatalogId"
        )

        UPDATE "catalogProducts" cp
        SET "status" = children_catalog_products."childrenCollectiveStatus"::status
        FROM children_catalog_products
        WHERE children_catalog_products."parentCatalogId" = cp."catalogId"
        AND children_catalog_products."productId" = cp."productId"
        `)
    },

    down: async function(queryInterface: QueryInterface) {},
};



