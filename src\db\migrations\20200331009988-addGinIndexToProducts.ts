import { QueryInterface }   from 'sequelize';

const testEnv = 'test';

const ProductsTable = 'products';
const brandIndex = 'product_brand_name';
const nameIndex = 'products_name';
const brandName = 'brandName';
const productName = 'name';

export = {
    up: async function( queryInterface: QueryInterface) {
        await queryInterface.removeIndex(ProductsTable, nameIndex);
        await queryInterface.removeIndex(ProductsTable, brandIndex);

        /**
         * Setup for this extension happens outside of the regular
         * migration path because the production environment doesn't
         * allow it to happen (superuser permissions are needed for
         * enabling extensions), but for our CI env we need this to run
         * automatically (where you *do* have permissions to add the
         * extension).
         */
        if (process.env.NODE_ENV == testEnv) {
            await queryInterface.sequelize.query(`
                CREATE EXTENSION IF NOT EXISTS pg_trgm;
            `);
        }

        await queryInterface.sequelize.query(`
            CREATE INDEX product_name_idx ON products USING GIN(name gin_trgm_ops);
            CREATE INDEX brand_name_idx ON products USING GIN("brandName" gin_trgm_ops);
        `);
    },

    down: async function ( queryInterface: QueryInterface) {
        await queryInterface.sequelize.query(`
            DROP INDEX product_name_idx;
            DROP INDEX brand_name_idx;
        `);

        if (process.env.NODE_ENV == testEnv) {
            await queryInterface.sequelize.query(`
                DROP EXTENSION IF EXISTS pg_trgm;
            `);
        }

        await queryInterface.addIndex(ProductsTable, [brandName]);
        await queryInterface.addIndex(ProductsTable, [productName]);
    },
}
