import { OwnerType }                    from '@treez/commons/sharedTypings/product';
import {
    autoPK<PERSON>ield,
    booleanField,
    createUpdateColumns,
    foreignKey,
    intField,
    jsonField,
    notNull,
    stringField,
}                                       from '@treez/dev-pack/db/fieldTypes';
import { QueryInterface }               from 'sequelize';
import { Sequelize }                    from 'sequelize-typescript';
import _                                from 'lodash';

const CATALOG_TABLE = 'catalogs';
const PRODUCT_TABLE = 'products';
const CATALOG_PRODUCT_TABLE = 'catalogProducts';


const catalogEnumField = {
    ...stringField,

    allowNull: false,
    defaultValue: OwnerType.STORE,
    values: _.values(OwnerType),
}

const ownerEnumField = {
    ...stringField,

    allowNull: false,
    defaultValue: OwnerType.BRAND,
    values: _.values(OwnerType),
}

const newNotNullStringField = {
    ...stringField,

    allowNull: false,
    defaultValue: 'unknown',
}

export = {
    up: async function( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.addColumn(CATALOG_PRODUCT_TABLE, 'active', booleanField);
        await queryInterface.addColumn(CATALOG_PRODUCT_TABLE, 'overrides', jsonField);

        await queryInterface.dropTable('entityReference');

        await queryInterface.addColumn(CATALOG_TABLE, 'parentCatalogId', foreignKey(intField, CATALOG_TABLE, 'id'));
        await queryInterface.addColumn(CATALOG_TABLE, 'ownerType', catalogEnumField);
        await queryInterface.renameColumn(CATALOG_TABLE, 'sourceId', 'ownerId');
        await queryInterface.removeColumn(CATALOG_TABLE, 'source');

        await queryInterface.addColumn(PRODUCT_TABLE, 'ownerId', newNotNullStringField);
        await queryInterface.addColumn(PRODUCT_TABLE, 'ownerType', ownerEnumField)

        await queryInterface.addIndex(CATALOG_TABLE, ['ownerId']);
    },

    down: async function ( queryInterface: QueryInterface, sequelize: Sequelize ) {
        await queryInterface.removeIndex(CATALOG_TABLE, ['ownerId']);

        await queryInterface.removeColumn(PRODUCT_TABLE, 'ownerType');
        await queryInterface.removeColumn(PRODUCT_TABLE, 'ownerId');

        await queryInterface.addColumn(CATALOG_TABLE, 'source', stringField);
        await queryInterface.renameColumn(CATALOG_TABLE, 'ownerId', 'sourceId');
        await queryInterface.removeColumn(CATALOG_TABLE, 'ownerType');
        await queryInterface.removeColumn(CATALOG_TABLE, 'parentCatalogId');

        await queryInterface.createTable('entityReference', {
            id:                         autoPKField,
            catalogId:                  foreignKey(intField, CATALOG_TABLE, 'id'),
            ownerId:                    notNull(stringField),

            ...createUpdateColumns
        });

        await queryInterface.removeColumn(CATALOG_PRODUCT_TABLE, 'overrides');
        await queryInterface.removeColumn(CATALOG_PRODUCT_TABLE, 'active');
    },
}
